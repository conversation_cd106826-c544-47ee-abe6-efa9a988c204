<DxWindow @bind-Visible="Visible"
          @bind-Visible:after="OnVisibleChanged"
          HeaderText="@TituloVentana"
          Width="900px"
          Height="700px"
          ShowCloseButton="true"
          AllowResize="true"
          ShowFooter="false"
          MinHeight="calc(100vh - 100px)"
          MinWidth="calc(100vw - 100px)">
    <BodyTemplate>
        @if (Visible && !string.IsNullOrEmpty(NombreReporte))
        {
            <DxDocumentViewer ReportName="@NombreReporte"
                              Height="calc(100vh - 100px)"
                              Width="calc(100vw - 100px)">
                <DxDocumentViewerExportSettings UseSameTab="false" ShowPrintNotificationDialog="false" />
                <DxDocumentViewerCallbacks CustomizeElements="ReportingViewerCustomization.onCustomizeElements"></DxDocumentViewerCallbacks>
            </DxDocumentViewer>
        }
    </BodyTemplate>
</DxWindow>

@code {
    [Parameter] public bool Visible { get; set; }
    [Parameter] public EventCallback<bool> VisibleChanged { get; set; }
    [Parameter] public string NombreReporte { get; set; } = string.Empty;
    [Parameter] public string TituloVentana { get; set; } = "Reporte";

    private async Task OnVisibleChanged()
    {
        await VisibleChanged.InvokeAsync(Visible);
    }
}
