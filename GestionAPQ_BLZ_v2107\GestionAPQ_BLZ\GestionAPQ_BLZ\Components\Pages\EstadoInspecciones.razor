﻿@page "/EstadoInspecciones"
@page "/EstadoInspecciones/{IdProducto}"

@inject IMediator Mediator
@inject ICustomToastService ToastService

<PageTitle>APQ - Estado Inspecciones</PageTitle>
<CustomLoadingPanel @bind-Visible="Cargando"/>

<div class="container">
	<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded shadow-sm mb-3">
		<div class="d-flex align-items-center flex-grow-1">
			<h1 class="h5 fw-bold mb-0 text-primary me-2 flex-shrink-0">
				<i class="bi bi-file-earmark me-1"></i>
				ESTADO INSPECCIONES POR PRODUCTO:
			</h1>
			<div class="flex-grow-1">
				<DxComboBox Data="_ddProductos"
				            NullText="Selecciona un producto..."
				            @bind-Value="@BarnizSelected"
				            @bind-Value:after="async () => await HandleUpdateBarnizSelected(BarnizSelected)"
				            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
				            EditFormat="{0}, {1}"
				            SearchFilterCondition="ListSearchFilterCondition.Contains"
				            SearchMode="ListSearchMode.AutoSearch"
				            CssClass="w-100">
					<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Codigo))" Caption="Producto"/>
					<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Descrip))" Caption="Descripción"/>
				</DxComboBox>
			</div>
		</div>
	</div>
</div>

<div class="w-100">
	<DxFormLayout CssClass="mx-2 px-0">
		<DxFormLayoutItem Caption=" PRODUCTOS Y LOTES CON INSPECCIONES PENDIENTES"
		                  CaptionCssClass="px-1"
		                  ColSpanMd="6"
		                  ColSpanSm="12"
		                  CaptionPosition="CaptionPosition.Vertical"
		                  CssClass="pe-lg-2 pe-0 ps-0">
			<div class="px-1" style="height: calc(100vh - 200px);">
				<DxGrid Data="@_lotesSinInspeccion"
				        ShowFilterRow="true"
				        AllowSort="true"
				        PageSize="100"
				        CssClass="h-100"
				        EditStart="async e => await GridInspeccionesPendientes_EditStart(e)">
					<Columns>
						<CustomDxGridCommandColumn EditButtonVisible="true"
						                           TooltipBtnEditar="Generar Inspección"
						                           NewButtonVisible="false"
						                           DeleteButtonVisible="false"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Codigo)}")"
						                  Caption="Código" DisplayFormat="F0"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Lote)}")"
						                  Caption="Lote">
						</DxGridDataColumn>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.PrimeraEntrada)}.{nameof(DetalleLoteBarnizDTO.PrimeraEntrada.Fecent)}")"
						                  Caption="Primera Entrada" DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}" SortIndex="0" SortOrder="GridColumnSortOrder.Descending"/>
					</Columns>
					<TotalSummary>
						<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}" FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Codigo)}")"/>
					</TotalSummary>
				</DxGrid>
			</div>
		</DxFormLayoutItem>

		<DxFormLayoutItem Caption="PRODUCTOS Y LOTES CON INSPECCIONES REALIZADAS"
		                  CaptionCssClass="px-1"
		                  ColSpanMd="6"
		                  ColSpanSm="12"
		                  CaptionPosition="CaptionPosition.Vertical"
		                  CssClass="ps-lg-2 ps-0 pe-0">
			<div class="px-1" style="height: calc(100vh - 200px);">
				<DxGrid Data="@_lotesConInspeccion"
				        ShowFilterRow="true"
				        AllowSort="true"
				        PageSize="100"
				        CssClass="h-100"
				        EditStart="async e => await GridInspeccionesPasadas_EditStart(e)">
					<Columns>
						<CustomDxGridCommandColumn EditButtonVisible="true"
						                           TooltipBtnEditar="Editar Inspección"
						                           NewButtonVisible="false"
						                           DeleteButtonVisible="false"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Inspeccion)}.{nameof(DetalleLoteBarnizDTO.Inspeccion.Idproducto)}")"
						                  Caption="Código" DisplayFormat="F0"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Inspeccion)}.{nameof(DetalleLoteBarnizDTO.Inspeccion.Idlote)}")"
						                  Caption="Lote"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Inspeccion)}.{nameof(DetalleLoteBarnizDTO.Inspeccion.Fecha)}")"
						                  Caption="Fecha" DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}" SortIndex="0" SortOrder="GridColumnSortOrder.Descending"/>
					</Columns>
					<TotalSummary>
						<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}" FieldName="@($"{nameof(DetalleLoteBarnizDTO.Inspeccion)}.{nameof(DetalleLoteBarnizDTO.Inspeccion.Idproducto)}")"/>
					</TotalSummary>
				</DxGrid>
			</div>
		</DxFormLayoutItem>
	</DxFormLayout>
</div>

<PopupInspecciones @ref="_popUpInspecciones" @bind-Visible="MostrarPopup" @bind-Visible:after="OnPopupClosed"></PopupInspecciones>

@code
{
	[Parameter] public string? IdProducto { get; set; }

	private bool Cargando { get; set; }
	private AArticuDTO? BarnizSelected { get; set; }
	private bool MostrarPopup { get; set; }

	private List<AArticuDTO> _ddProductos = [];
	private List<DetalleLoteBarnizDTO> _lotesConInspeccion = [];
	private List<DetalleLoteBarnizDTO> _lotesSinInspeccion = [];
	private PopupInspecciones _popUpInspecciones;

	protected override async Task OnParametersSetAsync()
	{
		await EjecutarConCarga(async () =>
		{
			ResetValoresPorDefecto();

			// Si se pasa IdProducto como parámetro, preseleccionar ese producto
			if (!string.IsNullOrEmpty(IdProducto) && int.TryParse(IdProducto, out var idProductoParam))
			{
				// Primero cargamos todos los productos para poder seleccionar el correcto
				var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(null, false));
				if (!resultProductos.Errors.Any() && resultProductos.Data != null)
				{
					_ddProductos = resultProductos.Data
						.Select(i => i.Articulo!)
						.OrderBy(i => i.Codigo)
						.ToList();
					BarnizSelected = _ddProductos.FirstOrDefault(p => p.Codigo == IdProducto);
				}
			}

			await CargarDatos();
		});
	}

	private void ResetValoresPorDefecto()
	{
		BarnizSelected = null;
		_ddProductos = [];
		_lotesConInspeccion = [];
		_lotesSinInspeccion = [];
		MostrarPopup = false;
	}

	private async Task CargarDatos()
	{
		// comprobaciones de parámetros para mediatr
		int? idProdSeleccionado = null;
		string? strProdSeleccionado = null;
		if (!string.IsNullOrEmpty(BarnizSelected?.Codigo))
		{
			if (!int.TryParse(BarnizSelected.Codigo, out var idProductoSel))
			{
				ToastService.MostrarError("El código tiene que ser numérico");
				return;
			}

			idProdSeleccionado = idProductoSel;
		}

		strProdSeleccionado = idProdSeleccionado.HasValue ? idProdSeleccionado.Value.ToString() : null;

		// ejecutamos mediatr
		var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(null, false));
		var resultLotes = await Mediator.Send(new GetDetallesLotesBarnicesQuery(strProdSeleccionado, true));
		var listaErrores = resultProductos.Errors.Concat(resultLotes.Errors);
		if (listaErrores.Any())
		{
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// actualizamos variables
		if (resultLotes.Data != null && resultProductos.Data != null)
		{
			var listaLotesPorArticulo = resultLotes.Data.Select(i => i.ListaDetallesLotes).ToList();
			var listaLotes = listaLotesPorArticulo.Where(i => i != null && i.Any()).SelectMany(i => i!).ToList();
			var lotesConInspeccion = listaLotes.Where(i => i.Inspeccion != null).ToList();
			var lotesSinInspeccion = listaLotes.Where(i => i.Inspeccion == null).ToList();

			_ddProductos = resultProductos.Data
				.Select(i => i.Articulo!)
				.OrderBy(i => i.Codigo)
				.ToList();
			_lotesConInspeccion = lotesConInspeccion;
			_lotesSinInspeccion = lotesSinInspeccion;
			if (idProdSeleccionado.HasValue) // recuperamos el barnizselected, si hemos filtrado por el dropdown
				BarnizSelected = _ddProductos.FirstOrDefault(i => i.Codigo.Equals(idProdSeleccionado.ToString()));
		}
	}

	private async Task HandleUpdateBarnizSelected(AArticuDTO? nuevoBarnizSelected)
	{
		await EjecutarConCarga(async () =>
		{
			ResetValoresPorDefecto();
			BarnizSelected = nuevoBarnizSelected;
			await CargarDatos();
		});
	}

	private async Task GridInspeccionesPendientes_EditStart(GridEditStartEventArgs e)
	{
		e.Cancel = true; // para que no abra el modo de edición de la row

		var loteSelected = (DetalleLoteBarnizDTO)e.DataItem;
		if (loteSelected?.Lote != null)
		{
			MostrarPopup = true;
			_popUpInspecciones.ResetValoresPorDefecto();

			if (int.TryParse(loteSelected.Lote.Codigo, out var idProducto))
			{
				await _popUpInspecciones.CargarDatos(idProducto, loteSelected.Lote.Lote ?? string.Empty, false);
			}
			else
			{
				ToastService.MostrarError("El código del producto tiene que ser numérico");
				MostrarPopup = false;
			}
		}
	}

	private async Task GridInspeccionesPasadas_EditStart(GridEditStartEventArgs e)
	{
		e.Cancel = true; // para que no abra el modo de edición de la row

		var loteSelected = (DetalleLoteBarnizDTO)e.DataItem;
		if (loteSelected?.Inspeccion != null)
		{
			MostrarPopup = true;
			_popUpInspecciones.ResetValoresPorDefecto();

			if (loteSelected.Inspeccion.Idproducto.HasValue)
			{
				await _popUpInspecciones.CargarDatos(
					loteSelected.Inspeccion.Idproducto.Value,
					loteSelected.Inspeccion.Idlote ?? string.Empty,
					true);
			}
			else
			{
				ToastService.MostrarError("El código del producto tiene que ser numérico");
				MostrarPopup = false;
			}
		}
	}

	private async Task OnPopupClosed()
	{
		// recargar datos cuando se cierre el popup para reflejar cambios
		if (!MostrarPopup)
			await EjecutarConCarga(async () => await CargarDatos());
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}
}