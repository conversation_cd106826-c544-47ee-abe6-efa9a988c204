﻿@page "/ProductosPorNodrizas"

@inject IMediator Mediator
@inject ICustomToastService ToastService
@inject ICustomDialogService DialogService

<PageTitle>APQ - Productos Por Nodrizas</PageTitle>
<CustomLoadingPanel @bind-Visible="Cargando"/>
<DxDialogProvider/>

<div class="d-flex flex-column h-100 overflow-x-hidden container">
	<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded shadow-sm flex-shrink-0">
		<div class="d-flex align-items-center flex-grow-1">
			<h1 class="h5 fw-bold mb-0 text-primary me-2 flex-shrink-0">
				<i class="bi bi-archive-fill me-1"></i>
				PRODUCTOS POR NODRIZAS
			</h1>
		</div>
	</div>
	<div class="flex-grow-1 overflow-x-hidden pt-3 ">
		<div class="row h-100 overflow-x-hidden g-3 p-0 m-0 p-0">
			<div class="col-12 h-100 overflow-hidden px-0 my-0 pe-1">
				<DxGrid Data="@_listaDetallesNodrizas"
				        SizeMode="SizeMode.Medium"
				        CssClass="ch-320 h-100"
				        PageSize="25"
				        SelectionMode="GridSelectionMode.Single"
				        AllowSelectRowByClick="true"
				        Context="ContextGridNodrizas"
				        AllowSort="true"
				        ShowFilterRow="true"
				        EditMode="GridEditMode.EditRow"
				        EditModelSaving="GridNodrizas_EditModelSaving"
				        CustomizeCellDisplayText="GridNodrizas_CustomizeCellDisplayText"
				        CustomizeEditModel="GridNodrizas_CustomizeEditModel">
					<Columns>
						<CustomDxGridCommandColumn TooltipBtnEditar="Editar Nodriza"
						                           TooltipBtnEliminar="Eliminar Nodriza"
						                           TooltipBtnGuardar="Guardar Nodriza"
						                           TooltipBtnNuevo="Nueva Nodriza"
						                           DeleteButtonVisible="false"
						                           Width="70"
						                           OnDeleteButtonClicked="async s => await GridNodrizas_HandleDeleteEvt(s)"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.TablaProductoNodriza)}.{nameof(DetalleTablaProductoNodrizaDTO.TablaProductoNodriza.IdNodriza)}")"
						                  Caption="Nodriza"
						                  SortIndex="0"
						                  SortOrder="GridColumnSortOrder.Ascending"
						                  Width="90"
						                  TextAlignment="GridTextAlignment.Center"/>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.Articulo)}")"
						                  Caption="Producto"
						                  DisplayFormat="{0}"
						                  TextAlignment="GridTextAlignment.Left">
							<EditSettings>
								<DxComboBoxSettings Data="_ddProductos"
								                    NullText="Selecciona un producto..."
								                    ValueFieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.Articulo.Codigo)}")"
								                    ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
								                    EditFormat="{0}, {1}"
								                    DisplayFormat="{0}, {1}"
								                    SearchFilterCondition="ListSearchFilterCondition.Contains"
								                    SearchMode="ListSearchMode.AutoSearch"
								                    CssClass="w-100">
									<Columns>
										<DxListEditorColumn FieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.Articulo.Codigo)}")" Caption="Código"/>
										<DxListEditorColumn FieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.Articulo.Descrip)}")" Caption="Descripción"/>
									</Columns>
								</DxComboBoxSettings>
							</EditSettings>
						</DxGridDataColumn>
						<DxGridDataColumn FieldName="@($"{nameof(DetalleTablaProductoNodrizaDTO.TablaProductoNodriza)}.{nameof(DetalleTablaProductoNodrizaDTO.TablaProductoNodriza.Activo)}")" Caption="Activo" Width="80"/>
					</Columns>
				</DxGrid>
			</div>
		</div>
	</div>
</div>

@code
{
	private bool Cargando { get; set; }

	private List<AArticuDTO> _ddProductos = [];
	public List<DetalleTablaProductoNodrizaDTO> _listaDetallesNodrizas = [];

	protected override async Task OnInitializedAsync()
	{
		await EjecutarConCarga(async () => { await CargarDatos(); });
	}

	private async Task CargarDatos()
	{
		// ejecutamos querys
		var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(null, false));
		var resultProdsEnNodrizas = await Mediator.Send(new GetAllEntitiesQuery<TablaProductosNodrizas, TablaProductosNodrizasDTO>());

		// comprobamos si hay errores y mostramos dado el caso
		var listaErrores = resultProductos.Errors.Concat(resultProdsEnNodrizas.Errors).ToList();
		if (listaErrores.Any())
		{
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// asignamos valor a variables
		_ddProductos = resultProductos.Data.Select(i => i.Articulo).OrderBy(i => i.Codigo).ToList();
		_listaDetallesNodrizas = resultProdsEnNodrizas.Data
			.OrderBy(i => i.IdNodriza)
			.Select(n => new DetalleTablaProductoNodrizaDTO
			{
				TablaProductoNodriza = n,
				Articulo = n.Idproducto.HasValue && n.Idproducto.Value != 0 ? _ddProductos.FirstOrDefault(i => i.Codigo.Equals(n.Idproducto.Value.ToString())) : null
			}).ToList();
	}

	private void GridNodrizas_CustomizeCellDisplayText(GridCustomizeCellDisplayTextEventArgs e)
	{
		var detallesNodriza = (DetalleTablaProductoNodrizaDTO)e.DataItem;
		switch (e.FieldName)
		{
			case nameof(DetalleTablaProductoNodrizaDTO.Articulo):
				var cadenaDisplay = "0";
				if (detallesNodriza.Articulo != null)
					cadenaDisplay = detallesNodriza.Articulo == null ? cadenaDisplay : $"{detallesNodriza.Articulo.Codigo}, {detallesNodriza.Articulo.Descrip}";
				e.DisplayText = cadenaDisplay;
				break;
		}
	}

	private void GridNodrizas_CustomizeEditModel(GridCustomizeEditModelEventArgs e)
	{
		if (e.IsNew)
		{
			var detalleNodrizaEdit = (DetalleTablaProductoNodrizaDTO)e.EditModel;
			detalleNodrizaEdit.TablaProductoNodriza = new TablaProductosNodrizasDTO(); // inicializamos para que nos deje editar
		}
	}

	private async Task GridNodrizas_EditModelSaving(GridEditModelSavingEventArgs e)
	{
		var detalleEdit = (DetalleTablaProductoNodrizaDTO)e.EditModel;
		var nodrizaEdit = detalleEdit.TablaProductoNodriza;


		// sacamos el id del producto seleccionado
		// si no tiene barniz seleccionado, debemos indicar a base de datos el código 0
		int? idProductoSeleccionado = null;
		if (detalleEdit.Articulo?.Codigo == null)
			idProductoSeleccionado = 0;
		else
		{
			if (!int.TryParse(detalleEdit.Articulo.Codigo, out var idProductoSel))
			{
				ToastService.MostrarError("El código tiene que ser numérico.");
				return;
			}

			idProductoSeleccionado = idProductoSel;
		}

		nodrizaEdit.Idproducto = idProductoSeleccionado.Value;

		await EjecutarConCarga(async () =>
		{
			var result = await Mediator.Send(new GrabarTablaProductosNodrizasCommand(nodrizaEdit));
			if (result.Errors.Any())
			{
				ToastService.MostrarError(result.Errors.First());
				e.Cancel = true;
			}
			else
			{
				await CargarDatos();
				ToastService.MostrarOk($"Nodriza {(e.IsNew ? "añadida" : "actualizada")} con éxito.");
			}
		});
	}

	private async Task GridNodrizas_HandleDeleteEvt(GridCommandColumnCellDisplayTemplateContext e)
	{
		var detalleNodriza = (DetalleTablaProductoNodrizaDTO)e.DataItem;

		var resp = await DialogService.MostrarConfirmacionEstiloDanger("Atención",
			@"¿Seguro que deseas eliminar esta nodriza?",
			"Sí",
			"No");
		if (!resp)
			return;

		await EjecutarConCarga(async () =>
		{
			var result = await Mediator.Send(new DeleteTablaProductosNodrizasCommand(detalleNodriza.TablaProductoNodriza.Id.Value));
			if (result.Errors.Any())
				ToastService.MostrarError(result.Errors.First());
			else
			{
				await CargarDatos();
				ToastService.MostrarOk($"Nodriza {detalleNodriza.TablaProductoNodriza.IdNodriza.Value} eliminada con éxito.");
			}
		});
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}
}