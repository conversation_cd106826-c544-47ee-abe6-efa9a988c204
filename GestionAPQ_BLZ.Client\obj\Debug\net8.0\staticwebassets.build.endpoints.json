{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "GestionAPQ_BLZ.Client.5k9b6nvr3m.styles.css", "AssetFile": "GestionAPQ_BLZ.Client.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "886"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"28jqxxpBD4iH7pMnJ30kPw//GQuIcxd5oFdD7x80r1g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5k9b6nvr3m"}, {"Name": "integrity", "Value": "sha256-28jqxxpBD4iH7pMnJ30kPw//GQuIcxd5oFdD7x80r1g="}, {"Name": "label", "Value": "GestionAPQ_BLZ.Client.styles.css"}]}, {"Route": "GestionAPQ_BLZ.Client.styles.css", "AssetFile": "GestionAPQ_BLZ.Client.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "886"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"28jqxxpBD4iH7pMnJ30kPw//GQuIcxd5oFdD7x80r1g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-28jqxxpBD4iH7pMnJ30kPw//GQuIcxd5oFdD7x80r1g="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.pdb", "AssetFile": "_framework/GestionAPQ_BLZ.Client.pdb", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16708"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FD4b7bhQ/Zavyre4vZQIig2u8kMGo1zyO9PqUCzIsLk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FD4b7bhQ/Zavyre4vZQIig2u8kMGo1zyO9PqUCzIsLk="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.pdb", "AssetFile": "_framework/GestionAPQ_BLZ.Client.pdb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000092790201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10776"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pMyL05Vt4YmqoRVnB5RNYU/hhJkG0Tv3YMUQy+8IFGs=\""}, {"Name": "ETag", "Value": "W/\"FD4b7bhQ/Zavyre4vZQIig2u8kMGo1zyO9PqUCzIsLk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FD4b7bhQ/Zavyre4vZQIig2u8kMGo1zyO9PqUCzIsLk="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.pdb.gz", "AssetFile": "_framework/GestionAPQ_BLZ.Client.pdb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10776"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pMyL05Vt4YmqoRVnB5RNYU/hhJkG0Tv3YMUQy+8IFGs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pMyL05Vt4YmqoRVnB5RNYU/hhJkG0Tv3YMUQy+8IFGs="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.wasm", "AssetFile": "_framework/GestionAPQ_BLZ.Client.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8981"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1UO5Aj6nnXf7fucxDoUhWsa4b8uUVOXlSMhNcHvxERQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1UO5Aj6nnXf7fucxDoUhWsa4b8uUVOXlSMhNcHvxERQ="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.wasm", "AssetFile": "_framework/GestionAPQ_BLZ.Client.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000241080039"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4147"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IU+t+kLjrdE7jLXsUe+4jMiocz25lY9CEdkYzD5Mfu4=\""}, {"Name": "ETag", "Value": "W/\"1UO5Aj6nnXf7fucxDoUhWsa4b8uUVOXlSMhNcHvxERQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1UO5Aj6nnXf7fucxDoUhWsa4b8uUVOXlSMhNcHvxERQ="}]}, {"Route": "_framework/GestionAPQ_BLZ.Client.wasm.gz", "AssetFile": "_framework/GestionAPQ_BLZ.Client.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4147"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IU+t+kLjrdE7jLXsUe+4jMiocz25lY9CEdkYzD5Mfu4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:50:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IU+t+kLjrdE7jLXsUe+4jMiocz25lY9CEdkYzD5Mfu4="}]}, {"Route": "_framework/Microsoft.AspNetCore.Authorization.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Authorization.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "40725"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cF/PLwBpNWzO0iv0jUNLrwtSkbaiuMyMrnyDTzS8hPE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cF/PLwBpNWzO0iv0jUNLrwtSkbaiuMyMrnyDTzS8hPE="}]}, {"Route": "_framework/Microsoft.AspNetCore.Authorization.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000056541898"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "17685"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ecafT0lRqdDH42JaFIBQIHM1vDGuXyB4ue+WVG23bwQ=\""}, {"Name": "ETag", "Value": "W/\"cF/PLwBpNWzO0iv0jUNLrwtSkbaiuMyMrnyDTzS8hPE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cF/PLwBpNWzO0iv0jUNLrwtSkbaiuMyMrnyDTzS8hPE="}]}, {"Route": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Authorization.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "17685"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ecafT0lRqdDH42JaFIBQIHM1vDGuXyB4ue+WVG23bwQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ecafT0lRqdDH42JaFIBQIHM1vDGuXyB4ue+WVG23bwQ="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Forms.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Forms.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35093"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"a8CxDMB4fF6FqEXu0cqJvboh31ieCktL1ks3uAkF7Ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a8CxDMB4fF6FqEXu0cqJvboh31ieCktL1ks3uAkF7Ss="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Forms.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000061342167"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16301"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PG/Rb9AVt7nbUymKAZl7YxM0csW4HXn+9Agd1xBQSq0=\""}, {"Name": "ETag", "Value": "W/\"a8CxDMB4fF6FqEXu0cqJvboh31ieCktL1ks3uAkF7Ss=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a8CxDMB4fF6FqEXu0cqJvboh31ieCktL1ks3uAkF7Ss="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Forms.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16301"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PG/Rb9AVt7nbUymKAZl7YxM0csW4HXn+9Agd1xBQSq0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PG/Rb9AVt7nbUymKAZl7YxM0csW4HXn+9Agd1xBQSq0="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Web.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Web.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "163605"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LHMuScswTAzAfvWpGgHUbc43MhuFcXZtNoIpo+B58AE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHMuScswTAzAfvWpGgHUbc43MhuFcXZtNoIpo+B58AE="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Web.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015291689"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65394"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fUJHC8VFE8rv62hSwzm0wZtjcAOiJB205cGBLfusSbw=\""}, {"Name": "ETag", "Value": "W/\"LHMuScswTAzAfvWpGgHUbc43MhuFcXZtNoIpo+B58AE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHMuScswTAzAfvWpGgHUbc43MhuFcXZtNoIpo+B58AE="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Components.Web.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65394"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fUJHC8VFE8rv62hSwzm0wZtjcAOiJB205cGBLfusSbw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fUJHC8VFE8rv62hSwzm0wZtjcAOiJB205cGBLfusSbw="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "110357"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nvSHbrYDF2WYt3PcfLd+i7BxxUZJRueLJqMW5+x3c90=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nvSHbrYDF2WYt3PcfLd+i7BxxUZJRueLJqMW5+x3c90="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000021691503"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "46100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DwSPeHPhc3sqr7XkqHQKpEsqxF56tqS33p+w8Eo7DJA=\""}, {"Name": "ETag", "Value": "W/\"nvSHbrYDF2WYt3PcfLd+i7BxxUZJRueLJqMW5+x3c90=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nvSHbrYDF2WYt3PcfLd+i7BxxUZJRueLJqMW5+x3c90="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Components.WebAssembly.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "46100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DwSPeHPhc3sqr7XkqHQKpEsqxF56tqS33p+w8Eo7DJA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DwSPeHPhc3sqr7XkqHQKpEsqxF56tqS33p+w8Eo7DJA="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "323861"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JI3+JHhOyNzePVhcAHAdypjptNEZWE3B9W9CzixVrOA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JI3+JHhOyNzePVhcAHAdypjptNEZWE3B9W9CzixVrOA="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000007726303"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "129427"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Cne0XQzZbThKEV9jBoJ0VYQ6ry2e86OujA23oh5ldb4=\""}, {"Name": "ETag", "Value": "W/\"JI3+JHhOyNzePVhcAHAdypjptNEZWE3B9W9CzixVrOA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JI3+JHhOyNzePVhcAHAdypjptNEZWE3B9W9CzixVrOA="}]}, {"Route": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Components.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "129427"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Cne0XQzZbThKEV9jBoJ0VYQ6ry2e86OujA23oh5ldb4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cne0XQzZbThKEV9jBoJ0VYQ6ry2e86OujA23oh5ldb4="}]}, {"Route": "_framework/Microsoft.AspNetCore.Metadata.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Metadata.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"shjkuSdYh2ZAetmJiu3tFgdSNIhhx8Zh2JNZ5jYG+oA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-shjkuSdYh2ZAetmJiu3tFgdSNIhhx8Zh2JNZ5jYG+oA="}]}, {"Route": "_framework/Microsoft.AspNetCore.Metadata.wasm", "AssetFile": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000414937759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2409"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8dEjdQxjLkbVNAmm4jRxcMdmw5Ow/IUIBLSMB05PKrg=\""}, {"Name": "ETag", "Value": "W/\"shjkuSdYh2ZAetmJiu3tFgdSNIhhx8Zh2JNZ5jYG+oA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-shjkuSdYh2ZAetmJiu3tFgdSNIhhx8Zh2JNZ5jYG+oA="}]}, {"Route": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "AssetFile": "_framework/Microsoft.AspNetCore.Metadata.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2409"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8dEjdQxjLkbVNAmm4jRxcMdmw5Ow/IUIBLSMB05PKrg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8dEjdQxjLkbVNAmm4jRxcMdmw5Ow/IUIBLSMB05PKrg="}]}, {"Route": "_framework/Microsoft.CSharp.wasm", "AssetFile": "_framework/Microsoft.CSharp.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "298261"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IhuBf7RI+nbKH2/Eg9u9K09cKnEGdKRtKO96LdC+lA8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IhuBf7RI+nbKH2/Eg9u9K09cKnEGdKRtKO96LdC+lA8="}]}, {"Route": "_framework/Microsoft.CSharp.wasm", "AssetFile": "_framework/Microsoft.CSharp.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000007621777"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "131202"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uVJcSEdkXMCxN4TZtLg1rWijBmXqSzug1t3kz3bUoNc=\""}, {"Name": "ETag", "Value": "W/\"IhuBf7RI+nbKH2/Eg9u9K09cKnEGdKRtKO96LdC+lA8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IhuBf7RI+nbKH2/Eg9u9K09cKnEGdKRtKO96LdC+lA8="}]}, {"Route": "_framework/Microsoft.CSharp.wasm.gz", "AssetFile": "_framework/Microsoft.CSharp.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "131202"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uVJcSEdkXMCxN4TZtLg1rWijBmXqSzug1t3kz3bUoNc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uVJcSEdkXMCxN4TZtLg1rWijBmXqSzug1t3kz3bUoNc="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17173"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000128303823"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7793"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=\""}, {"Name": "ETag", "Value": "W/\"yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yNdqbqDWGiJo943D7LPak5xryCBEsNH0wtdiuU1R9VE="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Abstractions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7793"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SKcKAQ6unQQmWOLud3+yjljdvRq3k5HjYUL0Z0Ex8QM="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Binder.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Binder.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32021"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fNNlyyW44y8Gp6dvOtsvHoVNzpT8lrKmPleg5CDhRAo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fNNlyyW44y8Gp6dvOtsvHoVNzpT8lrKmPleg5CDhRAo="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Binder.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072411296"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13809"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=\""}, {"Name": "ETag", "Value": "W/\"fNNlyyW44y8Gp6dvOtsvHoVNzpT8lrKmPleg5CDhRAo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fNNlyyW44y8Gp6dvOtsvHoVNzpT8lrKmPleg5CDhRAo="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Binder.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13809"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WSuabncDxkAB8fqRIdPNHPgeAGnmfkzqrcBXgplQMGQ="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17173"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SrAtaND/3+ZSfueBc9meuKzyFgcamyVA581L4R7Amzs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SrAtaND/3+ZSfueBc9meuKzyFgcamyVA581L4R7Amzs="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000130191381"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=\""}, {"Name": "ETag", "Value": "W/\"SrAtaND/3+ZSfueBc9meuKzyFgcamyVA581L4R7Amzs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SrAtaND/3+ZSfueBc9meuKzyFgcamyVA581L4R7Amzs="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Configuration.FileExtensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WO+uRYcj3Zb9HIK7aDnF+ZYPe+fyAeKo2LMHDHQRlOI="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Json.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Json.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"CtLsl9w5UTJXGXTbp+SQxMzt9f/gjQ4EkywrtuCscvU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CtLsl9w5UTJXGXTbp+SQxMzt9f/gjQ4EkywrtuCscvU="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Json.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000133155792"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7509"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=\""}, {"Name": "ETag", "Value": "W/\"CtLsl9w5UTJXGXTbp+SQxMzt9f/gjQ4EkywrtuCscvU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CtLsl9w5UTJXGXTbp+SQxMzt9f/gjQ4EkywrtuCscvU="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Configuration.Json.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7509"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M1N3wrmu41ddGz5INp3pKS70tYR/Y+Xqu+oZ9rZqjZ0="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33045"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.wasm", "AssetFile": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066242713"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15095"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=\""}, {"Name": "ETag", "Value": "W/\"UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UDGEQR7J3WTfzYMgOzxVIBwFQtKEeJvO8UgrWagypdE="}]}, {"Route": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Configuration.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15095"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-woWY7cPpxRwo/ZlBGIpiuVyrCcNVURoJEClmhSxYIT0="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53013"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NICKEw2hjoBhl1lvxOBh8cxOIN8kkrGHz3pvV1eLLbY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NICKEw2hjoBhl1lvxOBh8cxOIN8kkrGHz3pvV1eLLbY="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000048346548"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20683"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=\""}, {"Name": "ETag", "Value": "W/\"NICKEw2hjoBhl1lvxOBh8cxOIN8kkrGHz3pvV1eLLbY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NICKEw2hjoBhl1lvxOBh8cxOIN8kkrGHz3pvV1eLLbY="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20683"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwyC9p6nt0mGMqIypm+SnvG+21YdrXDmlVaZDNsWJeA="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.wasm", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "82197"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"CS+QrPLaogl32aUg+ES1Ef5QGKoMyjt3V+wGoiG+tpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CS+QrPLaogl32aUg+ES1Ef5QGKoMyjt3V+wGoiG+tpw="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.wasm", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000028474615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35118"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=\""}, {"Name": "ETag", "Value": "W/\"CS+QrPLaogl32aUg+ES1Ef5QGKoMyjt3V+wGoiG+tpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CS+QrPLaogl32aUg+ES1Ef5QGKoMyjt3V+wGoiG+tpw="}]}, {"Route": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.DependencyInjection.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35118"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XoDoAbTIxo5MKAxsmkcf9azi6O5OLViGGKrBi2qMlgY="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11541"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000196270854"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5094"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=\""}, {"Name": "ETag", "Value": "W/\"IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IUopixuV8E09mP+TIgRp/lXEbeacXbUTW7cu0v5B9Tc="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Abstractions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5094"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pyOZoIFEM9t5FDCjL1vt7pFHGrJ/aCpe5ncDLhyScEs="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34069"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000061308320"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16310"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=\""}, {"Name": "ETag", "Value": "W/\"MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MS+zB0xkKhKk/QdE32ZwKtdlrLMLI/y2NAKPUWhcBVg="}]}, {"Route": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.FileProviders.Physical.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16310"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mL9aDIgzoCBBugdOwscAnV2L14lXopq1fPoBppkHjc0="}]}, {"Route": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35093"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic="}]}, {"Route": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetFile": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000062073246"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16109"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=\""}, {"Name": "ETag", "Value": "W/\"AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AnWUKd0qJ8ZNKkRC0AFK2cjjszB7reXiP+bXdIi8bic="}]}, {"Route": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.FileSystemGlobbing.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16109"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1Ah/YODlnwQ4s7t24etOtyb4hdzr3YlCHH3s8gUCH8="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54549"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uf1KJp0fVm0d3Bs2JFD1oxo857dVH+MA9AF/tlH5YEw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uf1KJp0fVm0d3Bs2JFD1oxo857dVH+MA9AF/tlH5YEw="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm", "AssetFile": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000042337003"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23619"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=\""}, {"Name": "ETag", "Value": "W/\"uf1KJp0fVm0d3Bs2JFD1oxo857dVH+MA9AF/tlH5YEw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uf1KJp0fVm0d3Bs2JFD1oxo857dVH+MA9AF/tlH5YEw="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Logging.Abstractions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23619"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/EpLGuZe59Ju8jspXqvtC8hdyOL8Zrhe8lxopsvj6w="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.wasm", "AssetFile": "_framework/Microsoft.Extensions.Logging.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "40213"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pa0M3exxNvk6g5anUwlaPC99Afawsi1GZvJeU1q/ZGM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pa0M3exxNvk6g5anUwlaPC99Afawsi1GZvJeU1q/ZGM="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.wasm", "AssetFile": "_framework/Microsoft.Extensions.Logging.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000054274084"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18424"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=\""}, {"Name": "ETag", "Value": "W/\"pa0M3exxNvk6g5anUwlaPC99Afawsi1GZvJeU1q/ZGM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pa0M3exxNvk6g5anUwlaPC99Afawsi1GZvJeU1q/ZGM="}]}, {"Route": "_framework/Microsoft.Extensions.Logging.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Logging.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18424"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z48FFALZ2sAP4Fd5H7/RhhuPDZBP1f3ES8esZSke/qU="}]}, {"Route": "_framework/Microsoft.Extensions.Options.wasm", "AssetFile": "_framework/Microsoft.Extensions.Options.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54037"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM="}]}, {"Route": "_framework/Microsoft.Extensions.Options.wasm", "AssetFile": "_framework/Microsoft.Extensions.Options.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000042925824"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23295"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=\""}, {"Name": "ETag", "Value": "W/\"5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5/m+yVFGRuY+N4jQnD+QETKH0AfhAsnVze5dJ5ogIVM="}]}, {"Route": "_framework/Microsoft.Extensions.Options.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Options.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23295"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WnOZRQAyyjOv8sTLVpC29t7cLD/gYEUsRWah0QSLSuk="}]}, {"Route": "_framework/Microsoft.Extensions.Primitives.wasm", "AssetFile": "_framework/Microsoft.Extensions.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33045"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE="}]}, {"Route": "_framework/Microsoft.Extensions.Primitives.wasm", "AssetFile": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000067870232"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14733"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=\""}, {"Name": "ETag", "Value": "W/\"pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pVrYOTfjb2ITls3LKIByW1t8jwOAWFhmkIVDewtJ1GE="}]}, {"Route": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "AssetFile": "_framework/Microsoft.Extensions.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14733"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VXif+d8llcvt+N2pU6LUABQr1EUvnwTg27PGFGjJoWo="}]}, {"Route": "_framework/Microsoft.JSInterop.WebAssembly.wasm", "AssetFile": "_framework/Microsoft.JSInterop.WebAssembly.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14101"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eLMLijAO3SBUgTxBxjsDJ02XfJJp0gOYOK1nx/3EXBQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eLMLijAO3SBUgTxBxjsDJ02XfJJp0gOYOK1nx/3EXBQ="}]}, {"Route": "_framework/Microsoft.JSInterop.WebAssembly.wasm", "AssetFile": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147732309"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6768"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IBcnxwGryT32PgZ2jVxdiG7MsxfOEQcPwcQv5urXQNM=\""}, {"Name": "ETag", "Value": "W/\"eLMLijAO3SBUgTxBxjsDJ02XfJJp0gOYOK1nx/3EXBQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eLMLijAO3SBUgTxBxjsDJ02XfJJp0gOYOK1nx/3EXBQ="}]}, {"Route": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "AssetFile": "_framework/Microsoft.JSInterop.WebAssembly.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6768"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IBcnxwGryT32PgZ2jVxdiG7MsxfOEQcPwcQv5urXQNM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IBcnxwGryT32PgZ2jVxdiG7MsxfOEQcPwcQv5urXQNM="}]}, {"Route": "_framework/Microsoft.JSInterop.wasm", "AssetFile": "_framework/Microsoft.JSInterop.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55573"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"aRQ4gpd5P7vv/MV+k8dXmbJm+l5Ak/ICtSHJDSUmR/s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aRQ4gpd5P7vv/MV+k8dXmbJm+l5Ak/ICtSHJDSUmR/s="}]}, {"Route": "_framework/Microsoft.JSInterop.wasm", "AssetFile": "_framework/Microsoft.JSInterop.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041595608"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24040"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SzBvYAV8/Qpj4zN2ZMXjMmWXI/4HzmUpdldv7Ry9Mf8=\""}, {"Name": "ETag", "Value": "W/\"aRQ4gpd5P7vv/MV+k8dXmbJm+l5Ak/ICtSHJDSUmR/s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aRQ4gpd5P7vv/MV+k8dXmbJm+l5Ak/ICtSHJDSUmR/s="}]}, {"Route": "_framework/Microsoft.JSInterop.wasm.gz", "AssetFile": "_framework/Microsoft.JSInterop.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24040"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SzBvYAV8/Qpj4zN2ZMXjMmWXI/4HzmUpdldv7Ry9Mf8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SzBvYAV8/Qpj4zN2ZMXjMmWXI/4HzmUpdldv7Ry9Mf8="}]}, {"Route": "_framework/Microsoft.VisualBasic.Core.wasm", "AssetFile": "_framework/Microsoft.VisualBasic.Core.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "418581"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GUe2GLVmGtnWA1jmK+ny4PVMDpjs5bDGdHW6i8pK++M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GUe2GLVmGtnWA1jmK+ny4PVMDpjs5bDGdHW6i8pK++M="}]}, {"Route": "_framework/Microsoft.VisualBasic.Core.wasm", "AssetFile": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000005989997"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "166944"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hZNV1m4eMeVxLaDvAwjzHdNoQq4sqJkDq362MHuCgSc=\""}, {"Name": "ETag", "Value": "W/\"GUe2GLVmGtnWA1jmK+ny4PVMDpjs5bDGdHW6i8pK++M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GUe2GLVmGtnWA1jmK+ny4PVMDpjs5bDGdHW6i8pK++M="}]}, {"Route": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "AssetFile": "_framework/Microsoft.VisualBasic.Core.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "166944"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hZNV1m4eMeVxLaDvAwjzHdNoQq4sqJkDq362MHuCgSc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hZNV1m4eMeVxLaDvAwjzHdNoQq4sqJkDq362MHuCgSc="}]}, {"Route": "_framework/Microsoft.VisualBasic.wasm", "AssetFile": "_framework/Microsoft.VisualBasic.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6933"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mx5VOJ93iJ/5zicUGgdi21DqvUFUlmX7AU/BokIxMCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mx5VOJ93iJ/5zicUGgdi21DqvUFUlmX7AU/BokIxMCQ="}]}, {"Route": "_framework/Microsoft.VisualBasic.wasm", "AssetFile": "_framework/Microsoft.VisualBasic.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000350140056"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DdfMypc2YQuuwzA8dZL+rsnrUNXerNWljpQuGKBhwfA=\""}, {"Name": "ETag", "Value": "W/\"mx5VOJ93iJ/5zicUGgdi21DqvUFUlmX7AU/BokIxMCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mx5VOJ93iJ/5zicUGgdi21DqvUFUlmX7AU/BokIxMCQ="}]}, {"Route": "_framework/Microsoft.VisualBasic.wasm.gz", "AssetFile": "_framework/Microsoft.VisualBasic.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DdfMypc2YQuuwzA8dZL+rsnrUNXerNWljpQuGKBhwfA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DdfMypc2YQuuwzA8dZL+rsnrUNXerNWljpQuGKBhwfA="}]}, {"Route": "_framework/Microsoft.Win32.Primitives.wasm", "AssetFile": "_framework/Microsoft.Win32.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SZRASjBjUqTkdLGG15rxRKo2YmA/5SRxfuLvmH//B0U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZRASjBjUqTkdLGG15rxRKo2YmA/5SRxfuLvmH//B0U="}]}, {"Route": "_framework/Microsoft.Win32.Primitives.wasm", "AssetFile": "_framework/Microsoft.Win32.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2199"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"4VGF6hRRTg0FizroCMqqJN6gbdNW9Xpp0PJzXpmGKAk=\""}, {"Name": "ETag", "Value": "W/\"SZRASjBjUqTkdLGG15rxRKo2YmA/5SRxfuLvmH//B0U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZRASjBjUqTkdLGG15rxRKo2YmA/5SRxfuLvmH//B0U="}]}, {"Route": "_framework/Microsoft.Win32.Primitives.wasm.gz", "AssetFile": "_framework/Microsoft.Win32.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2199"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"4VGF6hRRTg0FizroCMqqJN6gbdNW9Xpp0PJzXpmGKAk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4VGF6hRRTg0FizroCMqqJN6gbdNW9Xpp0PJzXpmGKAk="}]}, {"Route": "_framework/Microsoft.Win32.Registry.wasm", "AssetFile": "_framework/Microsoft.Win32.Registry.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21269"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"BBFo5MRWCA733pNa6sCUMDbqls8iJ9s56E+oH4xHduw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BBFo5MRWCA733pNa6sCUMDbqls8iJ9s56E+oH4xHduw="}]}, {"Route": "_framework/Microsoft.Win32.Registry.wasm", "AssetFile": "_framework/Microsoft.Win32.Registry.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000117013808"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8545"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5qneCGle481G71rlC6XQJrqjjMG/FpVPMiUHZ1IjNYU=\""}, {"Name": "ETag", "Value": "W/\"BBFo5MRWCA733pNa6sCUMDbqls8iJ9s56E+oH4xHduw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BBFo5MRWCA733pNa6sCUMDbqls8iJ9s56E+oH4xHduw="}]}, {"Route": "_framework/Microsoft.Win32.Registry.wasm.gz", "AssetFile": "_framework/Microsoft.Win32.Registry.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8545"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5qneCGle481G71rlC6XQJrqjjMG/FpVPMiUHZ1IjNYU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5qneCGle481G71rlC6XQJrqjjMG/FpVPMiUHZ1IjNYU="}]}, {"Route": "_framework/System.AppContext.wasm", "AssetFile": "_framework/System.AppContext.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"sOUWO333G26Nc7pM6QYrC1lpXripk/cm5p7qYwAO5ME=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sOUWO333G26Nc7pM6QYrC1lpXripk/cm5p7qYwAO5ME="}]}, {"Route": "_framework/System.AppContext.wasm", "AssetFile": "_framework/System.AppContext.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476871722"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2096"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NvCSqRjn2hTndGvnhX1uqmbMkuvHHGxXeNV0DeB4/EI=\""}, {"Name": "ETag", "Value": "W/\"sOUWO333G26Nc7pM6QYrC1lpXripk/cm5p7qYwAO5ME=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sOUWO333G26Nc7pM6QYrC1lpXripk/cm5p7qYwAO5ME="}]}, {"Route": "_framework/System.AppContext.wasm.gz", "AssetFile": "_framework/System.AppContext.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2096"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NvCSqRjn2hTndGvnhX1uqmbMkuvHHGxXeNV0DeB4/EI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NvCSqRjn2hTndGvnhX1uqmbMkuvHHGxXeNV0DeB4/EI="}]}, {"Route": "_framework/System.Buffers.wasm", "AssetFile": "_framework/System.Buffers.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"VOIWDyhpg0Ab6+rEXGNiV2Qet1zWR4zk7sAfEBxB0+o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VOIWDyhpg0Ab6+rEXGNiV2Qet1zWR4zk7sAfEBxB0+o="}]}, {"Route": "_framework/System.Buffers.wasm", "AssetFile": "_framework/System.Buffers.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476871722"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2096"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/srFpS+XiaCBaCUA2g1LM1QdwtfWt3B19LAex/M/Ttc=\""}, {"Name": "ETag", "Value": "W/\"VOIWDyhpg0Ab6+rEXGNiV2Qet1zWR4zk7sAfEBxB0+o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VOIWDyhpg0Ab6+rEXGNiV2Qet1zWR4zk7sAfEBxB0+o="}]}, {"Route": "_framework/System.Buffers.wasm.gz", "AssetFile": "_framework/System.Buffers.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2096"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/srFpS+XiaCBaCUA2g1LM1QdwtfWt3B19LAex/M/Ttc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/srFpS+XiaCBaCUA2g1LM1QdwtfWt3B19LAex/M/Ttc="}]}, {"Route": "_framework/System.Collections.Concurrent.wasm", "AssetFile": "_framework/System.Collections.Concurrent.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75029"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"kmKvmM5RwqQcY75i37fekWfJiZL3h4kvy9yYZEptCYM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmKvmM5RwqQcY75i37fekWfJiZL3h4kvy9yYZEptCYM="}]}, {"Route": "_framework/System.Collections.Concurrent.wasm", "AssetFile": "_framework/System.Collections.Concurrent.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030960711"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32298"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"TMiyQMitBRPUDiAZhfx3bN9X79L3kP2p1wcB6VmATbQ=\""}, {"Name": "ETag", "Value": "W/\"kmKvmM5RwqQcY75i37fekWfJiZL3h4kvy9yYZEptCYM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmKvmM5RwqQcY75i37fekWfJiZL3h4kvy9yYZEptCYM="}]}, {"Route": "_framework/System.Collections.Concurrent.wasm.gz", "AssetFile": "_framework/System.Collections.Concurrent.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32298"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"TMiyQMitBRPUDiAZhfx3bN9X79L3kP2p1wcB6VmATbQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TMiyQMitBRPUDiAZhfx3bN9X79L3kP2p1wcB6VmATbQ="}]}, {"Route": "_framework/System.Collections.Immutable.wasm", "AssetFile": "_framework/System.Collections.Immutable.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "234773"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"AB1eG5GutTS68O4MfkzkPghDM47Fuby9THYZx2D8NI8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AB1eG5GutTS68O4MfkzkPghDM47Fuby9THYZx2D8NI8="}]}, {"Route": "_framework/System.Collections.Immutable.wasm", "AssetFile": "_framework/System.Collections.Immutable.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010475153"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "95463"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9oPtKVVL7G9QQahifTa/PLMP8k5R41I7XQbh5RQ4X4c=\""}, {"Name": "ETag", "Value": "W/\"AB1eG5GutTS68O4MfkzkPghDM47Fuby9THYZx2D8NI8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AB1eG5GutTS68O4MfkzkPghDM47Fuby9THYZx2D8NI8="}]}, {"Route": "_framework/System.Collections.Immutable.wasm.gz", "AssetFile": "_framework/System.Collections.Immutable.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "95463"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9oPtKVVL7G9QQahifTa/PLMP8k5R41I7XQbh5RQ4X4c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9oPtKVVL7G9QQahifTa/PLMP8k5R41I7XQbh5RQ4X4c="}]}, {"Route": "_framework/System.Collections.NonGeneric.wasm", "AssetFile": "_framework/System.Collections.NonGeneric.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36117"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"p5x/JRGkdmzegbvPugbtz1qYWc1vQAHJtyaOVc1QWhI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p5x/JRGkdmzegbvPugbtz1qYWc1vQAHJtyaOVc1QWhI="}]}, {"Route": "_framework/System.Collections.NonGeneric.wasm", "AssetFile": "_framework/System.Collections.NonGeneric.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070741370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14135"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"qt8eWjzLE4z2sBV6K+QUKJtWTnSJwcWtz58ri1xDOvI=\""}, {"Name": "ETag", "Value": "W/\"p5x/JRGkdmzegbvPugbtz1qYWc1vQAHJtyaOVc1QWhI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p5x/JRGkdmzegbvPugbtz1qYWc1vQAHJtyaOVc1QWhI="}]}, {"Route": "_framework/System.Collections.NonGeneric.wasm.gz", "AssetFile": "_framework/System.Collections.NonGeneric.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14135"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"qt8eWjzLE4z2sBV6K+QUKJtWTnSJwcWtz58ri1xDOvI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qt8eWjzLE4z2sBV6K+QUKJtWTnSJwcWtz58ri1xDOvI="}]}, {"Route": "_framework/System.Collections.Specialized.wasm", "AssetFile": "_framework/System.Collections.Specialized.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36629"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2kmtsoGNh0lL7Vyx2puvfWr2qrom0CLpKc8Gx3GKIKM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2kmtsoGNh0lL7Vyx2puvfWr2qrom0CLpKc8Gx3GKIKM="}]}, {"Route": "_framework/System.Collections.Specialized.wasm", "AssetFile": "_framework/System.Collections.Specialized.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000062336367"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16041"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uMKQ6BdJOS50ToMjgN3YmIVKGpTSr/dlKo/nrn0oJ0Q=\""}, {"Name": "ETag", "Value": "W/\"2kmtsoGNh0lL7Vyx2puvfWr2qrom0CLpKc8Gx3GKIKM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2kmtsoGNh0lL7Vyx2puvfWr2qrom0CLpKc8Gx3GKIKM="}]}, {"Route": "_framework/System.Collections.Specialized.wasm.gz", "AssetFile": "_framework/System.Collections.Specialized.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16041"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uMKQ6BdJOS50ToMjgN3YmIVKGpTSr/dlKo/nrn0oJ0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uMKQ6BdJOS50ToMjgN3YmIVKGpTSr/dlKo/nrn0oJ0Q="}]}, {"Route": "_framework/System.Collections.wasm", "AssetFile": "_framework/System.Collections.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91413"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"CQQugFwAlUAcbjZxeKqz4Vrpm3itr3DJsadlbxXYGQA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CQQugFwAlUAcbjZxeKqz4Vrpm3itr3DJsadlbxXYGQA="}]}, {"Route": "_framework/System.Collections.wasm", "AssetFile": "_framework/System.Collections.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025693731"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38919"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"L9qTcUmfz4yenKFykty3jQrOIMyDvm9+ra0gnTv9w88=\""}, {"Name": "ETag", "Value": "W/\"CQQugFwAlUAcbjZxeKqz4Vrpm3itr3DJsadlbxXYGQA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CQQugFwAlUAcbjZxeKqz4Vrpm3itr3DJsadlbxXYGQA="}]}, {"Route": "_framework/System.Collections.wasm.gz", "AssetFile": "_framework/System.Collections.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "38919"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"L9qTcUmfz4yenKFykty3jQrOIMyDvm9+ra0gnTv9w88=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L9qTcUmfz4yenKFykty3jQrOIMyDvm9+ra0gnTv9w88="}]}, {"Route": "_framework/System.ComponentModel.Annotations.wasm", "AssetFile": "_framework/System.ComponentModel.Annotations.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90901"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eMQbJ5Rz/SXnSegJUYuuR5+AiFdxqGc9ZdPElOv2BEo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eMQbJ5Rz/SXnSegJUYuuR5+AiFdxqGc9ZdPElOv2BEo="}]}, {"Route": "_framework/System.ComponentModel.Annotations.wasm", "AssetFile": "_framework/System.ComponentModel.Annotations.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000028372014"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35245"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nRYTatEchDCpR/u86IozXbJMec9L094hg2vzz2ihAbE=\""}, {"Name": "ETag", "Value": "W/\"eMQbJ5Rz/SXnSegJUYuuR5+AiFdxqGc9ZdPElOv2BEo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eMQbJ5Rz/SXnSegJUYuuR5+AiFdxqGc9ZdPElOv2BEo="}]}, {"Route": "_framework/System.ComponentModel.Annotations.wasm.gz", "AssetFile": "_framework/System.ComponentModel.Annotations.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35245"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nRYTatEchDCpR/u86IozXbJMec9L094hg2vzz2ihAbE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nRYTatEchDCpR/u86IozXbJMec9L094hg2vzz2ihAbE="}]}, {"Route": "_framework/System.ComponentModel.DataAnnotations.wasm", "AssetFile": "_framework/System.ComponentModel.DataAnnotations.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fzCs9lgsHIUSXLatZlRDuwPWwmoWIF6Pl+XGXgjmRD8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fzCs9lgsHIUSXLatZlRDuwPWwmoWIF6Pl+XGXgjmRD8="}]}, {"Route": "_framework/System.ComponentModel.DataAnnotations.wasm", "AssetFile": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000388802488"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2571"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"O2wzUonp8o6XnhaKMwdW5WB+ru/3EpUDAZoo4SVim8Q=\""}, {"Name": "ETag", "Value": "W/\"fzCs9lgsHIUSXLatZlRDuwPWwmoWIF6Pl+XGXgjmRD8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fzCs9lgsHIUSXLatZlRDuwPWwmoWIF6Pl+XGXgjmRD8="}]}, {"Route": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "AssetFile": "_framework/System.ComponentModel.DataAnnotations.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2571"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"O2wzUonp8o6XnhaKMwdW5WB+ru/3EpUDAZoo4SVim8Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2wzUonp8o6XnhaKMwdW5WB+ru/3EpUDAZoo4SVim8Q="}]}, {"Route": "_framework/System.ComponentModel.EventBasedAsync.wasm", "AssetFile": "_framework/System.ComponentModel.EventBasedAsync.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15637"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8GM3dEi8sekgrLzwvvify8HB/H3QYRXipE2IRlKwex8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8GM3dEi8sekgrLzwvvify8HB/H3QYRXipE2IRlKwex8="}]}, {"Route": "_framework/System.ComponentModel.EventBasedAsync.wasm", "AssetFile": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147383935"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6784"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"zaufxICHKEKp819QeLPWsELEXnSv0dluSquHfmgf1kI=\""}, {"Name": "ETag", "Value": "W/\"8GM3dEi8sekgrLzwvvify8HB/H3QYRXipE2IRlKwex8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8GM3dEi8sekgrLzwvvify8HB/H3QYRXipE2IRlKwex8="}]}, {"Route": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "AssetFile": "_framework/System.ComponentModel.EventBasedAsync.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6784"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"zaufxICHKEKp819QeLPWsELEXnSv0dluSquHfmgf1kI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zaufxICHKEKp819QeLPWsELEXnSv0dluSquHfmgf1kI="}]}, {"Route": "_framework/System.ComponentModel.Primitives.wasm", "AssetFile": "_framework/System.ComponentModel.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30997"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"20mvdJDJ1oh/1aRNIBCBTAMU99RkvQhLG4xLQ69IQIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-20mvdJDJ1oh/1aRNIBCBTAMU99RkvQhLG4xLQ69IQIM="}]}, {"Route": "_framework/System.ComponentModel.Primitives.wasm", "AssetFile": "_framework/System.ComponentModel.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076493536"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13072"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LsgT0jQ+cgVDMBj6OH2BE83I8epEz190Ir6exwS7Uow=\""}, {"Name": "ETag", "Value": "W/\"20mvdJDJ1oh/1aRNIBCBTAMU99RkvQhLG4xLQ69IQIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-20mvdJDJ1oh/1aRNIBCBTAMU99RkvQhLG4xLQ69IQIM="}]}, {"Route": "_framework/System.ComponentModel.Primitives.wasm.gz", "AssetFile": "_framework/System.ComponentModel.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13072"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LsgT0jQ+cgVDMBj6OH2BE83I8epEz190Ir6exwS7Uow=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LsgT0jQ+cgVDMBj6OH2BE83I8epEz190Ir6exwS7Uow="}]}, {"Route": "_framework/System.ComponentModel.TypeConverter.wasm", "AssetFile": "_framework/System.ComponentModel.TypeConverter.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "291605"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"N1pTh9obXcHHBZpkKM9614Fqqktjp/Btc/x46e1PsCY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N1pTh9obXcHHBZpkKM9614Fqqktjp/Btc/x46e1PsCY="}]}, {"Route": "_framework/System.ComponentModel.TypeConverter.wasm", "AssetFile": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008468978"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "118077"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/gHp2EhgY//U2do8KriJFcATaWa2T74WI/14jnXpDLo=\""}, {"Name": "ETag", "Value": "W/\"N1pTh9obXcHHBZpkKM9614Fqqktjp/Btc/x46e1PsCY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N1pTh9obXcHHBZpkKM9614Fqqktjp/Btc/x46e1PsCY="}]}, {"Route": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "AssetFile": "_framework/System.ComponentModel.TypeConverter.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "118077"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/gHp2EhgY//U2do8KriJFcATaWa2T74WI/14jnXpDLo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/gHp2EhgY//U2do8KriJFcATaWa2T74WI/14jnXpDLo="}]}, {"Route": "_framework/System.ComponentModel.wasm", "AssetFile": "_framework/System.ComponentModel.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EM2OVE/zqWK5JAIJdMFRoj1Hvv1tscn+e50qQwurzsg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EM2OVE/zqWK5JAIJdMFRoj1Hvv1tscn+e50qQwurzsg="}]}, {"Route": "_framework/System.ComponentModel.wasm", "AssetFile": "_framework/System.ComponentModel.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000390777648"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2558"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"HOpAHrpVqI5csYy9OSQCoECv6MeCE6VF3c9JY61sdUc=\""}, {"Name": "ETag", "Value": "W/\"EM2OVE/zqWK5JAIJdMFRoj1Hvv1tscn+e50qQwurzsg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EM2OVE/zqWK5JAIJdMFRoj1Hvv1tscn+e50qQwurzsg="}]}, {"Route": "_framework/System.ComponentModel.wasm.gz", "AssetFile": "_framework/System.ComponentModel.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2558"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"HOpAHrpVqI5csYy9OSQCoECv6MeCE6VF3c9JY61sdUc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HOpAHrpVqI5csYy9OSQCoECv6MeCE6VF3c9JY61sdUc="}]}, {"Route": "_framework/System.Configuration.wasm", "AssetFile": "_framework/System.Configuration.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8981"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Td+VHDCGktPNsQXHb+cn3kqvRA2R0zdMWZai20wy5vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Td+VHDCGktPNsQXHb+cn3kqvRA2R0zdMWZai20wy5vQ="}]}, {"Route": "_framework/System.Configuration.wasm", "AssetFile": "_framework/System.Configuration.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000321853878"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3106"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"h1JScOYZgBSw712Fd5Hxg3vYZ2BEljlDvOKTIK7CQ60=\""}, {"Name": "ETag", "Value": "W/\"Td+VHDCGktPNsQXHb+cn3kqvRA2R0zdMWZai20wy5vQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Td+VHDCGktPNsQXHb+cn3kqvRA2R0zdMWZai20wy5vQ="}]}, {"Route": "_framework/System.Configuration.wasm.gz", "AssetFile": "_framework/System.Configuration.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3106"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"h1JScOYZgBSw712Fd5Hxg3vYZ2BEljlDvOKTIK7CQ60=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h1JScOYZgBSw712Fd5Hxg3vYZ2BEljlDvOKTIK7CQ60="}]}, {"Route": "_framework/System.Console.wasm", "AssetFile": "_framework/System.Console.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43797"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Vv6EwZbTOMEr5gUiKwt7kNmhpQTJASInhtUVNEVAXkY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vv6EwZbTOMEr5gUiKwt7kNmhpQTJASInhtUVNEVAXkY="}]}, {"Route": "_framework/System.Console.wasm", "AssetFile": "_framework/System.Console.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000050505051"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19799"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hpJzAyoS92fmZ9APSQNzy/ydR1a3/nQJ6sseXj9Pkp0=\""}, {"Name": "ETag", "Value": "W/\"Vv6EwZbTOMEr5gUiKwt7kNmhpQTJASInhtUVNEVAXkY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vv6EwZbTOMEr5gUiKwt7kNmhpQTJASInhtUVNEVAXkY="}]}, {"Route": "_framework/System.Console.wasm.gz", "AssetFile": "_framework/System.Console.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19799"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hpJzAyoS92fmZ9APSQNzy/ydR1a3/nQJ6sseXj9Pkp0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hpJzAyoS92fmZ9APSQNzy/ydR1a3/nQJ6sseXj9Pkp0="}]}, {"Route": "_framework/System.Core.wasm", "AssetFile": "_framework/System.Core.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13077"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5tr5wsZ646iFhw11H7LIRV82ccth1UZB9SLMtRjOTWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5tr5wsZ646iFhw11H7LIRV82ccth1UZB9SLMtRjOTWM="}]}, {"Route": "_framework/System.Core.wasm", "AssetFile": "_framework/System.Core.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000220409963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4536"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GnvGHqzkSGbep2i+fe8UpJiFO2hfxvFQtPymgNweKa0=\""}, {"Name": "ETag", "Value": "W/\"5tr5wsZ646iFhw11H7LIRV82ccth1UZB9SLMtRjOTWM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5tr5wsZ646iFhw11H7LIRV82ccth1UZB9SLMtRjOTWM="}]}, {"Route": "_framework/System.Core.wasm.gz", "AssetFile": "_framework/System.Core.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4536"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GnvGHqzkSGbep2i+fe8UpJiFO2hfxvFQtPymgNweKa0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GnvGHqzkSGbep2i+fe8UpJiFO2hfxvFQtPymgNweKa0="}]}, {"Route": "_framework/System.Data.Common.wasm", "AssetFile": "_framework/System.Data.Common.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1007893"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2/t0sCDhJ/+erevnl3vU4PY5n4+7pyDe/j/VlbMXcus=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2/t0sCDhJ/+erevnl3vU4PY5n4+7pyDe/j/VlbMXcus="}]}, {"Route": "_framework/System.Data.Common.wasm", "AssetFile": "_framework/System.Data.Common.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000002658125"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "376204"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UC++NWWHo1xh5yhAsGVXhnbFwF1P3tLkNNfnKXdGtAE=\""}, {"Name": "ETag", "Value": "W/\"2/t0sCDhJ/+erevnl3vU4PY5n4+7pyDe/j/VlbMXcus=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2/t0sCDhJ/+erevnl3vU4PY5n4+7pyDe/j/VlbMXcus="}]}, {"Route": "_framework/System.Data.Common.wasm.gz", "AssetFile": "_framework/System.Data.Common.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "376204"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UC++NWWHo1xh5yhAsGVXhnbFwF1P3tLkNNfnKXdGtAE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UC++NWWHo1xh5yhAsGVXhnbFwF1P3tLkNNfnKXdGtAE="}]}, {"Route": "_framework/System.Data.DataSetExtensions.wasm", "AssetFile": "_framework/System.Data.DataSetExtensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IFtMn9E+40xezq3U+6TBCmTWhylFyjHL8XQ0zgaSOhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IFtMn9E+40xezq3U+6TBCmTWhylFyjHL8XQ0zgaSOhA="}]}, {"Route": "_framework/System.Data.DataSetExtensions.wasm", "AssetFile": "_framework/System.Data.DataSetExtensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000484027106"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2065"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GrMAphn1NWft5jHvbmcLsU6DW3YtSufH/A5+XS2vK54=\""}, {"Name": "ETag", "Value": "W/\"IFtMn9E+40xezq3U+6TBCmTWhylFyjHL8XQ0zgaSOhA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IFtMn9E+40xezq3U+6TBCmTWhylFyjHL8XQ0zgaSOhA="}]}, {"Route": "_framework/System.Data.DataSetExtensions.wasm.gz", "AssetFile": "_framework/System.Data.DataSetExtensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2065"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GrMAphn1NWft5jHvbmcLsU6DW3YtSufH/A5+XS2vK54=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GrMAphn1NWft5jHvbmcLsU6DW3YtSufH/A5+XS2vK54="}]}, {"Route": "_framework/System.Data.wasm", "AssetFile": "_framework/System.Data.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14613"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mfUk28rdvqdlE/aas7qpwAmSs3oZeB6gUsVIQaJC770=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mfUk28rdvqdlE/aas7qpwAmSs3oZeB6gUsVIQaJC770="}]}, {"Route": "_framework/System.Data.wasm", "AssetFile": "_framework/System.Data.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000200360649"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4990"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cxqUuRagOJhUTzgpYQqMZK7btwIF7YehwtxtLKqBJ/c=\""}, {"Name": "ETag", "Value": "W/\"mfUk28rdvqdlE/aas7qpwAmSs3oZeB6gUsVIQaJC770=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mfUk28rdvqdlE/aas7qpwAmSs3oZeB6gUsVIQaJC770="}]}, {"Route": "_framework/System.Data.wasm.gz", "AssetFile": "_framework/System.Data.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4990"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cxqUuRagOJhUTzgpYQqMZK7btwIF7YehwtxtLKqBJ/c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxqUuRagOJhUTzgpYQqMZK7btwIF7YehwtxtLKqBJ/c="}]}, {"Route": "_framework/System.Diagnostics.Contracts.wasm", "AssetFile": "_framework/System.Diagnostics.Contracts.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gshF7h3mGz/o1SNwlFG6//7WyQsDMRjR2xwOJ8PZa8k=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gshF7h3mGz/o1SNwlFG6//7WyQsDMRjR2xwOJ8PZa8k="}]}, {"Route": "_framework/System.Diagnostics.Contracts.wasm", "AssetFile": "_framework/System.Diagnostics.Contracts.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418410042"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Km2XUN7/8mmozF9vMFRs5YAkcp6WENtE9ZcKi75ibn0=\""}, {"Name": "ETag", "Value": "W/\"gshF7h3mGz/o1SNwlFG6//7WyQsDMRjR2xwOJ8PZa8k=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gshF7h3mGz/o1SNwlFG6//7WyQsDMRjR2xwOJ8PZa8k="}]}, {"Route": "_framework/System.Diagnostics.Contracts.wasm.gz", "AssetFile": "_framework/System.Diagnostics.Contracts.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Km2XUN7/8mmozF9vMFRs5YAkcp6WENtE9ZcKi75ibn0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Km2XUN7/8mmozF9vMFRs5YAkcp6WENtE9ZcKi75ibn0="}]}, {"Route": "_framework/System.Diagnostics.Debug.wasm", "AssetFile": "_framework/System.Diagnostics.Debug.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"asheiFgdEdI7Cp6DhCWjW80OJN7efwvyeCEEob0XilE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-asheiFgdEdI7Cp6DhCWjW80OJN7efwvyeCEEob0XilE="}]}, {"Route": "_framework/System.Diagnostics.Debug.wasm", "AssetFile": "_framework/System.Diagnostics.Debug.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000440334654"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2270"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rrJWCprLodBBLFSleJZxEU+9yqMv9+a4tq3onCdcFtM=\""}, {"Name": "ETag", "Value": "W/\"asheiFgdEdI7Cp6DhCWjW80OJN7efwvyeCEEob0XilE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-asheiFgdEdI7Cp6DhCWjW80OJN7efwvyeCEEob0XilE="}]}, {"Route": "_framework/System.Diagnostics.Debug.wasm.gz", "AssetFile": "_framework/System.Diagnostics.Debug.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2270"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rrJWCprLodBBLFSleJZxEU+9yqMv9+a4tq3onCdcFtM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrJWCprLodBBLFSleJZxEU+9yqMv9+a4tq3onCdcFtM="}]}, {"Route": "_framework/System.Diagnostics.DiagnosticSource.wasm", "AssetFile": "_framework/System.Diagnostics.DiagnosticSource.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "153365"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ayTWWe9xsYSKBWb7LPmNN40wNW1RhMcPj6F/YgIICGg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ayTWWe9xsYSKBWb7LPmNN40wNW1RhMcPj6F/YgIICGg="}]}, {"Route": "_framework/System.Diagnostics.DiagnosticSource.wasm", "AssetFile": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015352965"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65133"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"waF2fspygZ98hA5twI1ADpr/FGWixd3dPh0MKSr/0gQ=\""}, {"Name": "ETag", "Value": "W/\"ayTWWe9xsYSKBWb7LPmNN40wNW1RhMcPj6F/YgIICGg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ayTWWe9xsYSKBWb7LPmNN40wNW1RhMcPj6F/YgIICGg="}]}, {"Route": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "AssetFile": "_framework/System.Diagnostics.DiagnosticSource.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65133"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"waF2fspygZ98hA5twI1ADpr/FGWixd3dPh0MKSr/0gQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-waF2fspygZ98hA5twI1ADpr/FGWixd3dPh0MKSr/0gQ="}]}, {"Route": "_framework/System.Diagnostics.FileVersionInfo.wasm", "AssetFile": "_framework/System.Diagnostics.FileVersionInfo.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10517"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Hy0abIkwWQucxqxWStb1FKpgp4AMkLqmJJ85Qn/FOzk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hy0abIkwWQucxqxWStb1FKpgp4AMkLqmJJ85Qn/FOzk="}]}, {"Route": "_framework/System.Diagnostics.FileVersionInfo.wasm", "AssetFile": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000220458554"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4535"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dT4LJ20hExFcvLrLQZ41lc7yW4tZAEgJjjzDqH6/kBY=\""}, {"Name": "ETag", "Value": "W/\"Hy0abIkwWQucxqxWStb1FKpgp4AMkLqmJJ85Qn/FOzk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hy0abIkwWQucxqxWStb1FKpgp4AMkLqmJJ85Qn/FOzk="}]}, {"Route": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "AssetFile": "_framework/System.Diagnostics.FileVersionInfo.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4535"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dT4LJ20hExFcvLrLQZ41lc7yW4tZAEgJjjzDqH6/kBY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dT4LJ20hExFcvLrLQZ41lc7yW4tZAEgJjjzDqH6/kBY="}]}, {"Route": "_framework/System.Diagnostics.Process.wasm", "AssetFile": "_framework/System.Diagnostics.Process.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44309"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Taf23cZuN4u7LeXbMIAacDlpXvz4rsuti77NrVjc2rQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Taf23cZuN4u7LeXbMIAacDlpXvz4rsuti77NrVjc2rQ="}]}, {"Route": "_framework/System.Diagnostics.Process.wasm", "AssetFile": "_framework/System.Diagnostics.Process.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064499484"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15503"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MGjgujYLvT9jLmLhZvuIoaElsp7r0ll+HLRy2sBl82M=\""}, {"Name": "ETag", "Value": "W/\"Taf23cZuN4u7LeXbMIAacDlpXvz4rsuti77NrVjc2rQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Taf23cZuN4u7LeXbMIAacDlpXvz4rsuti77NrVjc2rQ="}]}, {"Route": "_framework/System.Diagnostics.Process.wasm.gz", "AssetFile": "_framework/System.Diagnostics.Process.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15503"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MGjgujYLvT9jLmLhZvuIoaElsp7r0ll+HLRy2sBl82M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MGjgujYLvT9jLmLhZvuIoaElsp7r0ll+HLRy2sBl82M="}]}, {"Route": "_framework/System.Diagnostics.StackTrace.wasm", "AssetFile": "_framework/System.Diagnostics.StackTrace.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15637"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"njHbGndg79EnG5ocInBki6TtJWgBNfs7xAZiGZURugc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-njHbGndg79EnG5ocInBki6TtJWgBNfs7xAZiGZURugc="}]}, {"Route": "_framework/System.Diagnostics.StackTrace.wasm", "AssetFile": "_framework/System.Diagnostics.StackTrace.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000136351241"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7333"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ftVJae+h3TDTsro+O9bOHy44p1HPXvSErY1HZ8MaGPs=\""}, {"Name": "ETag", "Value": "W/\"njHbGndg79EnG5ocInBki6TtJWgBNfs7xAZiGZURugc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-njHbGndg79EnG5ocInBki6TtJWgBNfs7xAZiGZURugc="}]}, {"Route": "_framework/System.Diagnostics.StackTrace.wasm.gz", "AssetFile": "_framework/System.Diagnostics.StackTrace.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7333"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ftVJae+h3TDTsro+O9bOHy44p1HPXvSErY1HZ8MaGPs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ftVJae+h3TDTsro+O9bOHy44p1HPXvSErY1HZ8MaGPs="}]}, {"Route": "_framework/System.Diagnostics.TextWriterTraceListener.wasm", "AssetFile": "_framework/System.Diagnostics.TextWriterTraceListener.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20757"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"tBxBq1lLXM/jadCDSo2Nwm/LxxvuUJ4AItagMhkqiiY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tBxBq1lLXM/jadCDSo2Nwm/LxxvuUJ4AItagMhkqiiY="}]}, {"Route": "_framework/System.Diagnostics.TextWriterTraceListener.wasm", "AssetFile": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000106564365"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9383"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UaC3xl/xzPSrTw2ZTNp1VoP7UwrZdByAPgGghNm9Z+w=\""}, {"Name": "ETag", "Value": "W/\"tBxBq1lLXM/jadCDSo2Nwm/LxxvuUJ4AItagMhkqiiY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tBxBq1lLXM/jadCDSo2Nwm/LxxvuUJ4AItagMhkqiiY="}]}, {"Route": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "AssetFile": "_framework/System.Diagnostics.TextWriterTraceListener.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9383"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UaC3xl/xzPSrTw2ZTNp1VoP7UwrZdByAPgGghNm9Z+w=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UaC3xl/xzPSrTw2ZTNp1VoP7UwrZdByAPgGghNm9Z+w="}]}, {"Route": "_framework/System.Diagnostics.Tools.wasm", "AssetFile": "_framework/System.Diagnostics.Tools.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dUSuR2I+62Ayu4EVF6mM8laZ18/AnA7U0PfRk3JB5sE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dUSuR2I+62Ayu4EVF6mM8laZ18/AnA7U0PfRk3JB5sE="}]}, {"Route": "_framework/System.Diagnostics.Tools.wasm", "AssetFile": "_framework/System.Diagnostics.Tools.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000460193281"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2172"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pM1Cs3stoOwdLwpFR7TBXOB3WViqRW7BI3gaB9msP2A=\""}, {"Name": "ETag", "Value": "W/\"dUSuR2I+62Ayu4EVF6mM8laZ18/AnA7U0PfRk3JB5sE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dUSuR2I+62Ayu4EVF6mM8laZ18/AnA7U0PfRk3JB5sE="}]}, {"Route": "_framework/System.Diagnostics.Tools.wasm.gz", "AssetFile": "_framework/System.Diagnostics.Tools.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2172"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pM1Cs3stoOwdLwpFR7TBXOB3WViqRW7BI3gaB9msP2A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pM1Cs3stoOwdLwpFR7TBXOB3WViqRW7BI3gaB9msP2A="}]}, {"Route": "_framework/System.Diagnostics.TraceSource.wasm", "AssetFile": "_framework/System.Diagnostics.TraceSource.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "46869"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/1FnlciB+2L7XeivcibzuRdAy/eM/rCZErAXcD3sLcc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/1FnlciB+2L7XeivcibzuRdAy/eM/rCZErAXcD3sLcc="}]}, {"Route": "_framework/System.Diagnostics.TraceSource.wasm", "AssetFile": "_framework/System.Diagnostics.TraceSource.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000051221636"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19522"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"R7tTyqFNf6wr7C1RVtwA583pGWqlJXR5CL7+wVzOBNg=\""}, {"Name": "ETag", "Value": "W/\"/1FnlciB+2L7XeivcibzuRdAy/eM/rCZErAXcD3sLcc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/1FnlciB+2L7XeivcibzuRdAy/eM/rCZErAXcD3sLcc="}]}, {"Route": "_framework/System.Diagnostics.TraceSource.wasm.gz", "AssetFile": "_framework/System.Diagnostics.TraceSource.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19522"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"R7tTyqFNf6wr7C1RVtwA583pGWqlJXR5CL7+wVzOBNg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R7tTyqFNf6wr7C1RVtwA583pGWqlJXR5CL7+wVzOBNg="}]}, {"Route": "_framework/System.Diagnostics.Tracing.wasm", "AssetFile": "_framework/System.Diagnostics.Tracing.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JLl+zGEPwMxj6bTOhd3vnCVrMPgwmir7R2ly7YA3c7E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JLl+zGEPwMxj6bTOhd3vnCVrMPgwmir7R2ly7YA3c7E="}]}, {"Route": "_framework/System.Diagnostics.Tracing.wasm", "AssetFile": "_framework/System.Diagnostics.Tracing.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000400641026"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2495"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"vOCpDSO4rsr0RpJ4/8OBnxSEnPRDQi3HpoxbA6jj4ZA=\""}, {"Name": "ETag", "Value": "W/\"JLl+zGEPwMxj6bTOhd3vnCVrMPgwmir7R2ly7YA3c7E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JLl+zGEPwMxj6bTOhd3vnCVrMPgwmir7R2ly7YA3c7E="}]}, {"Route": "_framework/System.Diagnostics.Tracing.wasm.gz", "AssetFile": "_framework/System.Diagnostics.Tracing.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2495"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"vOCpDSO4rsr0RpJ4/8OBnxSEnPRDQi3HpoxbA6jj4ZA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vOCpDSO4rsr0RpJ4/8OBnxSEnPRDQi3HpoxbA6jj4ZA="}]}, {"Route": "_framework/System.Drawing.Primitives.wasm", "AssetFile": "_framework/System.Drawing.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52501"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ddbYhKAmAe31uJJ0DEaYrRwqWXQervMYVJON3o8tekI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ddbYhKAmAe31uJJ0DEaYrRwqWXQervMYVJON3o8tekI="}]}, {"Route": "_framework/System.Drawing.Primitives.wasm", "AssetFile": "_framework/System.Drawing.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000043090447"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23206"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1Ac0IHBnRfvF4RS2250mZdJbMGQ6EHb4hQENJGffZ9c=\""}, {"Name": "ETag", "Value": "W/\"ddbYhKAmAe31uJJ0DEaYrRwqWXQervMYVJON3o8tekI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ddbYhKAmAe31uJJ0DEaYrRwqWXQervMYVJON3o8tekI="}]}, {"Route": "_framework/System.Drawing.Primitives.wasm.gz", "AssetFile": "_framework/System.Drawing.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23206"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1Ac0IHBnRfvF4RS2250mZdJbMGQ6EHb4hQENJGffZ9c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Ac0IHBnRfvF4RS2250mZdJbMGQ6EHb4hQENJGffZ9c="}]}, {"Route": "_framework/System.Drawing.wasm", "AssetFile": "_framework/System.Drawing.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10005"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"04D4OWDwcMgG53yxmZW2eYstE2Fm+HM0sJIrigE29Kk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-04D4OWDwcMgG53yxmZW2eYstE2Fm+HM0sJIrigE29Kk="}]}, {"Route": "_framework/System.Drawing.wasm", "AssetFile": "_framework/System.Drawing.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000260688217"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3835"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rNzGzWbtLYZ3N/VzaxhaH5UccujKdY1NrQByndSLqEM=\""}, {"Name": "ETag", "Value": "W/\"04D4OWDwcMgG53yxmZW2eYstE2Fm+HM0sJIrigE29Kk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-04D4OWDwcMgG53yxmZW2eYstE2Fm+HM0sJIrigE29Kk="}]}, {"Route": "_framework/System.Drawing.wasm.gz", "AssetFile": "_framework/System.Drawing.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3835"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rNzGzWbtLYZ3N/VzaxhaH5UccujKdY1NrQByndSLqEM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rNzGzWbtLYZ3N/VzaxhaH5UccujKdY1NrQByndSLqEM="}]}, {"Route": "_framework/System.Dynamic.Runtime.wasm", "AssetFile": "_framework/System.Dynamic.Runtime.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ykUYdSDmSm+6i7e4J3BxdDCbdm09ZC779NSxrvPc1W8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ykUYdSDmSm+6i7e4J3BxdDCbdm09ZC779NSxrvPc1W8="}]}, {"Route": "_framework/System.Dynamic.Runtime.wasm", "AssetFile": "_framework/System.Dynamic.Runtime.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000410004100"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2438"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1G68bP5g8G1aksSKrjsf5pYkSpVeanoswXfGGRKVd8c=\""}, {"Name": "ETag", "Value": "W/\"ykUYdSDmSm+6i7e4J3BxdDCbdm09ZC779NSxrvPc1W8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ykUYdSDmSm+6i7e4J3BxdDCbdm09ZC779NSxrvPc1W8="}]}, {"Route": "_framework/System.Dynamic.Runtime.wasm.gz", "AssetFile": "_framework/System.Dynamic.Runtime.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2438"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1G68bP5g8G1aksSKrjsf5pYkSpVeanoswXfGGRKVd8c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1G68bP5g8G1aksSKrjsf5pYkSpVeanoswXfGGRKVd8c="}]}, {"Route": "_framework/System.Formats.Asn1.wasm", "AssetFile": "_framework/System.Formats.Asn1.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "86293"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"kISPRbvVfnqz8745/QAl4gHEk7YNaFbjKm/3TnsGJUg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kISPRbvVfnqz8745/QAl4gHEk7YNaFbjKm/3TnsGJUg="}]}, {"Route": "_framework/System.Formats.Asn1.wasm", "AssetFile": "_framework/System.Formats.Asn1.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000028210336"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35447"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GPR++tj5qOQahjx7SMMeY1FJOYQDMXYGZ/OJ6ez1zns=\""}, {"Name": "ETag", "Value": "W/\"kISPRbvVfnqz8745/QAl4gHEk7YNaFbjKm/3TnsGJUg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kISPRbvVfnqz8745/QAl4gHEk7YNaFbjKm/3TnsGJUg="}]}, {"Route": "_framework/System.Formats.Asn1.wasm.gz", "AssetFile": "_framework/System.Formats.Asn1.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "35447"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GPR++tj5qOQahjx7SMMeY1FJOYQDMXYGZ/OJ6ez1zns=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GPR++tj5qOQahjx7SMMeY1FJOYQDMXYGZ/OJ6ez1zns="}]}, {"Route": "_framework/System.Formats.Tar.wasm", "AssetFile": "_framework/System.Formats.Tar.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2HtD3NrpmOkYPTx+ajeI4lqQxd11t0UXmvN+0uyDJ3Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2HtD3NrpmOkYPTx+ajeI4lqQxd11t0UXmvN+0uyDJ3Y="}]}, {"Route": "_framework/System.Formats.Tar.wasm", "AssetFile": "_framework/System.Formats.Tar.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000102082483"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9795"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eq/9rvAGHQgt0cXL0Ga+9/bH24vbchzSZQEIfFejEss=\""}, {"Name": "ETag", "Value": "W/\"2HtD3NrpmOkYPTx+ajeI4lqQxd11t0UXmvN+0uyDJ3Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2HtD3NrpmOkYPTx+ajeI4lqQxd11t0UXmvN+0uyDJ3Y="}]}, {"Route": "_framework/System.Formats.Tar.wasm.gz", "AssetFile": "_framework/System.Formats.Tar.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9795"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eq/9rvAGHQgt0cXL0Ga+9/bH24vbchzSZQEIfFejEss=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eq/9rvAGHQgt0cXL0Ga+9/bH24vbchzSZQEIfFejEss="}]}, {"Route": "_framework/System.Globalization.Calendars.wasm", "AssetFile": "_framework/System.Globalization.Calendars.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EKpvWhn0g63TfIcwLb0xcuNSrN9tzJuaACa/+qI7muM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EKpvWhn0g63TfIcwLb0xcuNSrN9tzJuaACa/+qI7muM="}]}, {"Route": "_framework/System.Globalization.Calendars.wasm", "AssetFile": "_framework/System.Globalization.Calendars.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000438020149"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2282"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XfLHOuNRBAO2llfRsFHiIU4rqGADbOz+aaHVJIqObrs=\""}, {"Name": "ETag", "Value": "W/\"EKpvWhn0g63TfIcwLb0xcuNSrN9tzJuaACa/+qI7muM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EKpvWhn0g63TfIcwLb0xcuNSrN9tzJuaACa/+qI7muM="}]}, {"Route": "_framework/System.Globalization.Calendars.wasm.gz", "AssetFile": "_framework/System.Globalization.Calendars.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2282"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XfLHOuNRBAO2llfRsFHiIU4rqGADbOz+aaHVJIqObrs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XfLHOuNRBAO2llfRsFHiIU4rqGADbOz+aaHVJIqObrs="}]}, {"Route": "_framework/System.Globalization.Extensions.wasm", "AssetFile": "_framework/System.Globalization.Extensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Fhll7ZbE0LxmZerQeOUcwbfEyENgffPKCOWfGhATxUE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fhll7ZbE0LxmZerQeOUcwbfEyENgffPKCOWfGhATxUE="}]}, {"Route": "_framework/System.Globalization.Extensions.wasm", "AssetFile": "_framework/System.Globalization.Extensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461254613"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2167"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pQUaNs/DKLDL9dHKFv0y+zyEthhhl3K+QjMgt3XsBUk=\""}, {"Name": "ETag", "Value": "W/\"Fhll7ZbE0LxmZerQeOUcwbfEyENgffPKCOWfGhATxUE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Fhll7ZbE0LxmZerQeOUcwbfEyENgffPKCOWfGhATxUE="}]}, {"Route": "_framework/System.Globalization.Extensions.wasm.gz", "AssetFile": "_framework/System.Globalization.Extensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2167"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pQUaNs/DKLDL9dHKFv0y+zyEthhhl3K+QjMgt3XsBUk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pQUaNs/DKLDL9dHKFv0y+zyEthhhl3K+QjMgt3XsBUk="}]}, {"Route": "_framework/System.Globalization.wasm", "AssetFile": "_framework/System.Globalization.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+21/yoh9LhilOmdRum94SItdBvOoPrk1NNNiFwipG/0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+21/yoh9LhilOmdRum94SItdBvOoPrk1NNNiFwipG/0="}]}, {"Route": "_framework/System.Globalization.wasm", "AssetFile": "_framework/System.Globalization.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000444049734"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2251"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"xt+BKvyNd/+RCrmWUS6HCWSKlKEtcuNgftqY40L56/w=\""}, {"Name": "ETag", "Value": "W/\"+21/yoh9LhilOmdRum94SItdBvOoPrk1NNNiFwipG/0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+21/yoh9LhilOmdRum94SItdBvOoPrk1NNNiFwipG/0="}]}, {"Route": "_framework/System.Globalization.wasm.gz", "AssetFile": "_framework/System.Globalization.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2251"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"xt+BKvyNd/+RCrmWUS6HCWSKlKEtcuNgftqY40L56/w=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xt+BKvyNd/+RCrmWUS6HCWSKlKEtcuNgftqY40L56/w="}]}, {"Route": "_framework/System.IO.Compression.Brotli.wasm", "AssetFile": "_framework/System.IO.Compression.Brotli.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"F40mKL3roIz3R7Uh+RNuxg7Zk8h+JX1eqtEq01TpiLI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F40mKL3roIz3R7Uh+RNuxg7Zk8h+JX1eqtEq01TpiLI="}]}, {"Route": "_framework/System.IO.Compression.Brotli.wasm", "AssetFile": "_framework/System.IO.Compression.Brotli.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000159413359"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6272"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"6c9dbhZ87JS9pq4BgvKp2pmVfHjs+yKqD2C9uJXEf2E=\""}, {"Name": "ETag", "Value": "W/\"F40mKL3roIz3R7Uh+RNuxg7Zk8h+JX1eqtEq01TpiLI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F40mKL3roIz3R7Uh+RNuxg7Zk8h+JX1eqtEq01TpiLI="}]}, {"Route": "_framework/System.IO.Compression.Brotli.wasm.gz", "AssetFile": "_framework/System.IO.Compression.Brotli.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6272"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"6c9dbhZ87JS9pq4BgvKp2pmVfHjs+yKqD2C9uJXEf2E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6c9dbhZ87JS9pq4BgvKp2pmVfHjs+yKqD2C9uJXEf2E="}]}, {"Route": "_framework/System.IO.Compression.FileSystem.wasm", "AssetFile": "_framework/System.IO.Compression.FileSystem.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"lyFepUq6ZOn2Bi4KuLuPn1b7fLKB4HqGWLRuEUx2eAs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lyFepUq6ZOn2Bi4KuLuPn1b7fLKB4HqGWLRuEUx2eAs="}]}, {"Route": "_framework/System.IO.Compression.FileSystem.wasm", "AssetFile": "_framework/System.IO.Compression.FileSystem.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000503018109"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1987"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yTsHV7dPmyFAkvHATCvC44WhL2Kb5Svl3ZvCOXjXWFQ=\""}, {"Name": "ETag", "Value": "W/\"lyFepUq6ZOn2Bi4KuLuPn1b7fLKB4HqGWLRuEUx2eAs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lyFepUq6ZOn2Bi4KuLuPn1b7fLKB4HqGWLRuEUx2eAs="}]}, {"Route": "_framework/System.IO.Compression.FileSystem.wasm.gz", "AssetFile": "_framework/System.IO.Compression.FileSystem.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1987"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yTsHV7dPmyFAkvHATCvC44WhL2Kb5Svl3ZvCOXjXWFQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yTsHV7dPmyFAkvHATCvC44WhL2Kb5Svl3ZvCOXjXWFQ="}]}, {"Route": "_framework/System.IO.Compression.ZipFile.wasm", "AssetFile": "_framework/System.IO.Compression.ZipFile.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"O/A7uvoyTFnRtP7JOwtTm8d88ZSYtYfQrsEJLuQExHg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/A7uvoyTFnRtP7JOwtTm8d88ZSYtYfQrsEJLuQExHg="}]}, {"Route": "_framework/System.IO.Compression.ZipFile.wasm", "AssetFile": "_framework/System.IO.Compression.ZipFile.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082358755"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12141"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Pvrr9g+4hhyLJqgl1gWUPTZUkeHe59DcWsdo6wFd+oo=\""}, {"Name": "ETag", "Value": "W/\"O/A7uvoyTFnRtP7JOwtTm8d88ZSYtYfQrsEJLuQExHg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/A7uvoyTFnRtP7JOwtTm8d88ZSYtYfQrsEJLuQExHg="}]}, {"Route": "_framework/System.IO.Compression.ZipFile.wasm.gz", "AssetFile": "_framework/System.IO.Compression.ZipFile.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12141"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Pvrr9g+4hhyLJqgl1gWUPTZUkeHe59DcWsdo6wFd+oo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pvrr9g+4hhyLJqgl1gWUPTZUkeHe59DcWsdo6wFd+oo="}]}, {"Route": "_framework/System.IO.Compression.wasm", "AssetFile": "_framework/System.IO.Compression.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "97557"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SrzfJPukcbCSuheshH5CgeAYAVsgLM7gBb/XKDvkDeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SrzfJPukcbCSuheshH5CgeAYAVsgLM7gBb/XKDvkDeY="}]}, {"Route": "_framework/System.IO.Compression.wasm", "AssetFile": "_framework/System.IO.Compression.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023692191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "42207"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3CgBKGIPfKltVivdIYhGMGpM9jl8WUbBg5EnRRqz9l8=\""}, {"Name": "ETag", "Value": "W/\"SrzfJPukcbCSuheshH5CgeAYAVsgLM7gBb/XKDvkDeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SrzfJPukcbCSuheshH5CgeAYAVsgLM7gBb/XKDvkDeY="}]}, {"Route": "_framework/System.IO.Compression.wasm.gz", "AssetFile": "_framework/System.IO.Compression.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "42207"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3CgBKGIPfKltVivdIYhGMGpM9jl8WUbBg5EnRRqz9l8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3CgBKGIPfKltVivdIYhGMGpM9jl8WUbBg5EnRRqz9l8="}]}, {"Route": "_framework/System.IO.FileSystem.AccessControl.wasm", "AssetFile": "_framework/System.IO.FileSystem.AccessControl.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "20245"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"z9sMNzreln+jN7kcl/qaRw6MfemhHWupU5sX0pGj0GM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z9sMNzreln+jN7kcl/qaRw6MfemhHWupU5sX0pGj0GM="}]}, {"Route": "_framework/System.IO.FileSystem.AccessControl.wasm", "AssetFile": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000126614333"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7897"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MtynzMF66oDTHS4h9XQItZanh+bO+NEQ89Ab8E5oQkw=\""}, {"Name": "ETag", "Value": "W/\"z9sMNzreln+jN7kcl/qaRw6MfemhHWupU5sX0pGj0GM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z9sMNzreln+jN7kcl/qaRw6MfemhHWupU5sX0pGj0GM="}]}, {"Route": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "AssetFile": "_framework/System.IO.FileSystem.AccessControl.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7897"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MtynzMF66oDTHS4h9XQItZanh+bO+NEQ89Ab8E5oQkw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MtynzMF66oDTHS4h9XQItZanh+bO+NEQ89Ab8E5oQkw="}]}, {"Route": "_framework/System.IO.FileSystem.DriveInfo.wasm", "AssetFile": "_framework/System.IO.FileSystem.DriveInfo.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12565"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"FtxaFswNgFpdu2qh//fZXLOXDObPE/8EPkGZXlR4Evk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FtxaFswNgFpdu2qh//fZXLOXDObPE/8EPkGZXlR4Evk="}]}, {"Route": "_framework/System.IO.FileSystem.DriveInfo.wasm", "AssetFile": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000178603322"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5598"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OjZM1H7qhI6LDeAzw3wE/uHzp2HBKaRh3vqxGGcnNxE=\""}, {"Name": "ETag", "Value": "W/\"FtxaFswNgFpdu2qh//fZXLOXDObPE/8EPkGZXlR4Evk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FtxaFswNgFpdu2qh//fZXLOXDObPE/8EPkGZXlR4Evk="}]}, {"Route": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "AssetFile": "_framework/System.IO.FileSystem.DriveInfo.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5598"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OjZM1H7qhI6LDeAzw3wE/uHzp2HBKaRh3vqxGGcnNxE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OjZM1H7qhI6LDeAzw3wE/uHzp2HBKaRh3vqxGGcnNxE="}]}, {"Route": "_framework/System.IO.FileSystem.Primitives.wasm", "AssetFile": "_framework/System.IO.FileSystem.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"tx0jtXXF/ixYJEDT8wIQTIq4H835HYbj/SoZ3CG1918=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tx0jtXXF/ixYJEDT8wIQTIq4H835HYbj/SoZ3CG1918="}]}, {"Route": "_framework/System.IO.FileSystem.Primitives.wasm", "AssetFile": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000460193281"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2172"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s/IGTPqtJuY1cY85bwTagQawkSHxBD8ESWfMP8y2NVI=\""}, {"Name": "ETag", "Value": "W/\"tx0jtXXF/ixYJEDT8wIQTIq4H835HYbj/SoZ3CG1918=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tx0jtXXF/ixYJEDT8wIQTIq4H835HYbj/SoZ3CG1918="}]}, {"Route": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "AssetFile": "_framework/System.IO.FileSystem.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2172"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s/IGTPqtJuY1cY85bwTagQawkSHxBD8ESWfMP8y2NVI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s/IGTPqtJuY1cY85bwTagQawkSHxBD8ESWfMP8y2NVI="}]}, {"Route": "_framework/System.IO.FileSystem.Watcher.wasm", "AssetFile": "_framework/System.IO.FileSystem.Watcher.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21269"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"zwRreY0ezuCKwf2ZxakaUS26dIB1TWeXbjGjSnDAqRw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zwRreY0ezuCKwf2ZxakaUS26dIB1TWeXbjGjSnDAqRw="}]}, {"Route": "_framework/System.IO.FileSystem.Watcher.wasm", "AssetFile": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000122174710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8184"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WrR+tXYRw1/gisJX+Whx0S0Z24GLFwlcVV9H2C0XJO0=\""}, {"Name": "ETag", "Value": "W/\"zwRreY0ezuCKwf2ZxakaUS26dIB1TWeXbjGjSnDAqRw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zwRreY0ezuCKwf2ZxakaUS26dIB1TWeXbjGjSnDAqRw="}]}, {"Route": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "AssetFile": "_framework/System.IO.FileSystem.Watcher.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8184"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WrR+tXYRw1/gisJX+Whx0S0Z24GLFwlcVV9H2C0XJO0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WrR+tXYRw1/gisJX+Whx0S0Z24GLFwlcVV9H2C0XJO0="}]}, {"Route": "_framework/System.IO.FileSystem.wasm", "AssetFile": "_framework/System.IO.FileSystem.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZuBLiq8aZ55P9xhiy3QijaNksmkU4HtxE0tzud+b5jY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZuBLiq8aZ55P9xhiy3QijaNksmkU4HtxE0tzud+b5jY="}]}, {"Route": "_framework/System.IO.FileSystem.wasm", "AssetFile": "_framework/System.IO.FileSystem.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000436490615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2290"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YluF58havvt/uGviUYj6OZG08+H0nvVu8IyJPySClLU=\""}, {"Name": "ETag", "Value": "W/\"ZuBLiq8aZ55P9xhiy3QijaNksmkU4HtxE0tzud+b5jY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZuBLiq8aZ55P9xhiy3QijaNksmkU4HtxE0tzud+b5jY="}]}, {"Route": "_framework/System.IO.FileSystem.wasm.gz", "AssetFile": "_framework/System.IO.FileSystem.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2290"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YluF58havvt/uGviUYj6OZG08+H0nvVu8IyJPySClLU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YluF58havvt/uGviUYj6OZG08+H0nvVu8IyJPySClLU="}]}, {"Route": "_framework/System.IO.IsolatedStorage.wasm", "AssetFile": "_framework/System.IO.IsolatedStorage.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23317"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GO1ON6jqPUzIf1QvKuFay4isw6NDcPc89ONCd8nZPqQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GO1ON6jqPUzIf1QvKuFay4isw6NDcPc89ONCd8nZPqQ="}]}, {"Route": "_framework/System.IO.IsolatedStorage.wasm", "AssetFile": "_framework/System.IO.IsolatedStorage.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000113908190"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8778"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"htaB7kjVhEBLAis+DQKM72Pxv1kSaErQIM/PLWQ53Lw=\""}, {"Name": "ETag", "Value": "W/\"GO1ON6jqPUzIf1QvKuFay4isw6NDcPc89ONCd8nZPqQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GO1ON6jqPUzIf1QvKuFay4isw6NDcPc89ONCd8nZPqQ="}]}, {"Route": "_framework/System.IO.IsolatedStorage.wasm.gz", "AssetFile": "_framework/System.IO.IsolatedStorage.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8778"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"htaB7kjVhEBLAis+DQKM72Pxv1kSaErQIM/PLWQ53Lw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-htaB7kjVhEBLAis+DQKM72Pxv1kSaErQIM/PLWQ53Lw="}]}, {"Route": "_framework/System.IO.MemoryMappedFiles.wasm", "AssetFile": "_framework/System.IO.MemoryMappedFiles.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37653"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Ln4Zp5+z7y4g/lRfHcKPhf8z+JIvF/VgF7ACPpRO7CE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ln4Zp5+z7y4g/lRfHcKPhf8z+JIvF/VgF7ACPpRO7CE="}]}, {"Route": "_framework/System.IO.MemoryMappedFiles.wasm", "AssetFile": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000061199510"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16339"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KAUMkaRMa2N4zLIFVhaP2FV7OlasDFAWK9EM+STZMIc=\""}, {"Name": "ETag", "Value": "W/\"Ln4Zp5+z7y4g/lRfHcKPhf8z+JIvF/VgF7ACPpRO7CE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ln4Zp5+z7y4g/lRfHcKPhf8z+JIvF/VgF7ACPpRO7CE="}]}, {"Route": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "AssetFile": "_framework/System.IO.MemoryMappedFiles.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16339"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KAUMkaRMa2N4zLIFVhaP2FV7OlasDFAWK9EM+STZMIc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KAUMkaRMa2N4zLIFVhaP2FV7OlasDFAWK9EM+STZMIc="}]}, {"Route": "_framework/System.IO.Pipelines.wasm", "AssetFile": "_framework/System.IO.Pipelines.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "67349"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JqVBy6SV+3qk+IK3VJiyIDt9CsVk6b1/ytTXmrI4mTM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JqVBy6SV+3qk+IK3VJiyIDt9CsVk6b1/ytTXmrI4mTM="}]}, {"Route": "_framework/System.IO.Pipelines.wasm", "AssetFile": "_framework/System.IO.Pipelines.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033647376"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29719"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=\""}, {"Name": "ETag", "Value": "W/\"JqVBy6SV+3qk+IK3VJiyIDt9CsVk6b1/ytTXmrI4mTM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JqVBy6SV+3qk+IK3VJiyIDt9CsVk6b1/ytTXmrI4mTM="}]}, {"Route": "_framework/System.IO.Pipelines.wasm.gz", "AssetFile": "_framework/System.IO.Pipelines.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29719"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CMzRd155p5hsb9RsAB5w0e7pOoS0xg2okJH6wC5TZJk="}]}, {"Route": "_framework/System.IO.Pipes.AccessControl.wasm", "AssetFile": "_framework/System.IO.Pipes.AccessControl.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12053"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Ig63nXxLBC3SYkP8pUBmVmWlLGEQr4BbxvCNkr2x/iA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ig63nXxLBC3SYkP8pUBmVmWlLGEQr4BbxvCNkr2x/iA="}]}, {"Route": "_framework/System.IO.Pipes.AccessControl.wasm", "AssetFile": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000188608072"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5301"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mMxHVV2w2s3PVhqorWdTNGr/DqWMQsf8sAyd5EtvtX8=\""}, {"Name": "ETag", "Value": "W/\"Ig63nXxLBC3SYkP8pUBmVmWlLGEQr4BbxvCNkr2x/iA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ig63nXxLBC3SYkP8pUBmVmWlLGEQr4BbxvCNkr2x/iA="}]}, {"Route": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "AssetFile": "_framework/System.IO.Pipes.AccessControl.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5301"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mMxHVV2w2s3PVhqorWdTNGr/DqWMQsf8sAyd5EtvtX8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mMxHVV2w2s3PVhqorWdTNGr/DqWMQsf8sAyd5EtvtX8="}]}, {"Route": "_framework/System.IO.Pipes.wasm", "AssetFile": "_framework/System.IO.Pipes.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "29973"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"oTSjR6/LfyxQLq52ABWQ6Ekcf8nWWGlrWzG5s9h9efw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oTSjR6/LfyxQLq52ABWQ6Ekcf8nWWGlrWzG5s9h9efw="}]}, {"Route": "_framework/System.IO.Pipes.wasm", "AssetFile": "_framework/System.IO.Pipes.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000095456281"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10475"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yasGbTRVRg/fPyCk0Ps7Smslb+5Zk7zdHrEuFhFRZrQ=\""}, {"Name": "ETag", "Value": "W/\"oTSjR6/LfyxQLq52ABWQ6Ekcf8nWWGlrWzG5s9h9efw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oTSjR6/LfyxQLq52ABWQ6Ekcf8nWWGlrWzG5s9h9efw="}]}, {"Route": "_framework/System.IO.Pipes.wasm.gz", "AssetFile": "_framework/System.IO.Pipes.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10475"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yasGbTRVRg/fPyCk0Ps7Smslb+5Zk7zdHrEuFhFRZrQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yasGbTRVRg/fPyCk0Ps7Smslb+5Zk7zdHrEuFhFRZrQ="}]}, {"Route": "_framework/System.IO.UnmanagedMemoryStream.wasm", "AssetFile": "_framework/System.IO.UnmanagedMemoryStream.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"7peYZj7KZ7OzQkJ+yTzxAEzOObNw0aTJjpdiOHgbwWc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7peYZj7KZ7OzQkJ+yTzxAEzOObNw0aTJjpdiOHgbwWc="}]}, {"Route": "_framework/System.IO.UnmanagedMemoryStream.wasm", "AssetFile": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000454752160"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2198"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ShD+PiKLQA4dr+kA4gku3yxKZ4DuyfX258hLSzY6LxY=\""}, {"Name": "ETag", "Value": "W/\"7peYZj7KZ7OzQkJ+yTzxAEzOObNw0aTJjpdiOHgbwWc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7peYZj7KZ7OzQkJ+yTzxAEzOObNw0aTJjpdiOHgbwWc="}]}, {"Route": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "AssetFile": "_framework/System.IO.UnmanagedMemoryStream.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2198"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ShD+PiKLQA4dr+kA4gku3yxKZ4DuyfX258hLSzY6LxY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ShD+PiKLQA4dr+kA4gku3yxKZ4DuyfX258hLSzY6LxY="}]}, {"Route": "_framework/System.IO.wasm", "AssetFile": "_framework/System.IO.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2nDc5kZ5q+hCjwfATdguIaXBRaqDSibV1sPvDT505DY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2nDc5kZ5q+hCjwfATdguIaXBRaqDSibV1sPvDT505DY="}]}, {"Route": "_framework/System.IO.wasm", "AssetFile": "_framework/System.IO.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000444049734"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2251"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"qqw4f98TMPJBB1iM0HWDqjsdAK3VqSBeezZxallANdY=\""}, {"Name": "ETag", "Value": "W/\"2nDc5kZ5q+hCjwfATdguIaXBRaqDSibV1sPvDT505DY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2nDc5kZ5q+hCjwfATdguIaXBRaqDSibV1sPvDT505DY="}]}, {"Route": "_framework/System.IO.wasm.gz", "AssetFile": "_framework/System.IO.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2251"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"qqw4f98TMPJBB1iM0HWDqjsdAK3VqSBeezZxallANdY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qqw4f98TMPJBB1iM0HWDqjsdAK3VqSBeezZxallANdY="}]}, {"Route": "_framework/System.Linq.Expressions.wasm", "AssetFile": "_framework/System.Linq.Expressions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "564501"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DRurglbdE/6YWdvh7r8a+IwbYGLaks3oy8gslrxr260=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRurglbdE/6YWdvh7r8a+IwbYGLaks3oy8gslrxr260="}]}, {"Route": "_framework/System.Linq.Expressions.wasm", "AssetFile": "_framework/System.Linq.Expressions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000004687375"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "213338"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LA+wZdFW9veJxgJYDo7G5nahVUze0m5FnCSR+sQKLP8=\""}, {"Name": "ETag", "Value": "W/\"DRurglbdE/6YWdvh7r8a+IwbYGLaks3oy8gslrxr260=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRurglbdE/6YWdvh7r8a+IwbYGLaks3oy8gslrxr260="}]}, {"Route": "_framework/System.Linq.Expressions.wasm.gz", "AssetFile": "_framework/System.Linq.Expressions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "213338"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LA+wZdFW9veJxgJYDo7G5nahVUze0m5FnCSR+sQKLP8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LA+wZdFW9veJxgJYDo7G5nahVUze0m5FnCSR+sQKLP8="}]}, {"Route": "_framework/System.Linq.Parallel.wasm", "AssetFile": "_framework/System.Linq.Parallel.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "213781"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DehyLaMCEpCGtjmvlljnrBcLhVFeonu0PzlivPnqlQM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DehyLaMCEpCGtjmvlljnrBcLhVFeonu0PzlivPnqlQM="}]}, {"Route": "_framework/System.Linq.Parallel.wasm", "AssetFile": "_framework/System.Linq.Parallel.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011509334"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ORs7kIo9rF88Hk1uoUMasrxf2xLvt1Njcdd4UhfxqKE=\""}, {"Name": "ETag", "Value": "W/\"DehyLaMCEpCGtjmvlljnrBcLhVFeonu0PzlivPnqlQM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DehyLaMCEpCGtjmvlljnrBcLhVFeonu0PzlivPnqlQM="}]}, {"Route": "_framework/System.Linq.Parallel.wasm.gz", "AssetFile": "_framework/System.Linq.Parallel.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ORs7kIo9rF88Hk1uoUMasrxf2xLvt1Njcdd4UhfxqKE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ORs7kIo9rF88Hk1uoUMasrxf2xLvt1Njcdd4UhfxqKE="}]}, {"Route": "_framework/System.Linq.Queryable.wasm", "AssetFile": "_framework/System.Linq.Queryable.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63253"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"h4QeNdTGDDjjw5W0QDEMqbhJdRjZghwQLVoBkznHRow=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h4QeNdTGDDjjw5W0QDEMqbhJdRjZghwQLVoBkznHRow="}]}, {"Route": "_framework/System.Linq.Queryable.wasm", "AssetFile": "_framework/System.Linq.Queryable.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000050178132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19928"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"G005DozveFEhRf7qJ2MoHnPdOBobEubuyOsWj/l5hqs=\""}, {"Name": "ETag", "Value": "W/\"h4QeNdTGDDjjw5W0QDEMqbhJdRjZghwQLVoBkznHRow=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h4QeNdTGDDjjw5W0QDEMqbhJdRjZghwQLVoBkznHRow="}]}, {"Route": "_framework/System.Linq.Queryable.wasm.gz", "AssetFile": "_framework/System.Linq.Queryable.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19928"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"G005DozveFEhRf7qJ2MoHnPdOBobEubuyOsWj/l5hqs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G005DozveFEhRf7qJ2MoHnPdOBobEubuyOsWj/l5hqs="}]}, {"Route": "_framework/System.Linq.wasm", "AssetFile": "_framework/System.Linq.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "124181"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OkmLooGYai5UTg7SZNBRIkVzTOCHD4t9TmzxWaMvvmU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OkmLooGYai5UTg7SZNBRIkVzTOCHD4t9TmzxWaMvvmU="}]}, {"Route": "_framework/System.Linq.wasm", "AssetFile": "_framework/System.Linq.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000019941372"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "50146"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SPA34ZK3xk+CrI17ibuMczx6Tn9obc+O6dZKTh9Wmz4=\""}, {"Name": "ETag", "Value": "W/\"OkmLooGYai5UTg7SZNBRIkVzTOCHD4t9TmzxWaMvvmU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OkmLooGYai5UTg7SZNBRIkVzTOCHD4t9TmzxWaMvvmU="}]}, {"Route": "_framework/System.Linq.wasm.gz", "AssetFile": "_framework/System.Linq.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "50146"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SPA34ZK3xk+CrI17ibuMczx6Tn9obc+O6dZKTh9Wmz4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SPA34ZK3xk+CrI17ibuMczx6Tn9obc+O6dZKTh9Wmz4="}]}, {"Route": "_framework/System.Memory.wasm", "AssetFile": "_framework/System.Memory.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44309"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"7RojiX6HF9509ngDhnJrOw0f0ClNGsunQ2AqJo6RgP4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7RojiX6HF9509ngDhnJrOw0f0ClNGsunQ2AqJo6RgP4="}]}, {"Route": "_framework/System.Memory.wasm", "AssetFile": "_framework/System.Memory.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000049166626"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20338"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"bIcI5B+wRf1SIiOG7fNKrk/DEduLK7RzEi7/AdQmXNE=\""}, {"Name": "ETag", "Value": "W/\"7RojiX6HF9509ngDhnJrOw0f0ClNGsunQ2AqJo6RgP4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7RojiX6HF9509ngDhnJrOw0f0ClNGsunQ2AqJo6RgP4="}]}, {"Route": "_framework/System.Memory.wasm.gz", "AssetFile": "_framework/System.Memory.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20338"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"bIcI5B+wRf1SIiOG7fNKrk/DEduLK7RzEi7/AdQmXNE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bIcI5B+wRf1SIiOG7fNKrk/DEduLK7RzEi7/AdQmXNE="}]}, {"Route": "_framework/System.Net.Http.Json.wasm", "AssetFile": "_framework/System.Net.Http.Json.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44821"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"TWVenINRW1SuuC7XC0l3OqvHkMaE2LnjojH1VTLvwrA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TWVenINRW1SuuC7XC0l3OqvHkMaE2LnjojH1VTLvwrA="}]}, {"Route": "_framework/System.Net.Http.Json.wasm", "AssetFile": "_framework/System.Net.Http.Json.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000052353280"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"umuc3ql7P93kmO7sNaRh4gowjab93+uw1y9Phl0g0MI=\""}, {"Name": "ETag", "Value": "W/\"TWVenINRW1SuuC7XC0l3OqvHkMaE2LnjojH1VTLvwrA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TWVenINRW1SuuC7XC0l3OqvHkMaE2LnjojH1VTLvwrA="}]}, {"Route": "_framework/System.Net.Http.Json.wasm.gz", "AssetFile": "_framework/System.Net.Http.Json.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"umuc3ql7P93kmO7sNaRh4gowjab93+uw1y9Phl0g0MI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umuc3ql7P93kmO7sNaRh4gowjab93+uw1y9Phl0g0MI="}]}, {"Route": "_framework/System.Net.Http.wasm", "AssetFile": "_framework/System.Net.Http.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "266517"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jJjangwFVf1/mU91TqwknpeefmNLsCwmc3iiHJNK9us=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jJjangwFVf1/mU91TqwknpeefmNLsCwmc3iiHJNK9us="}]}, {"Route": "_framework/System.Net.Http.wasm", "AssetFile": "_framework/System.Net.Http.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009023968"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "110815"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"irBHtodFcqlrGfHVrn2F/hAmqM+joe8np1Ov8oRyA0E=\""}, {"Name": "ETag", "Value": "W/\"jJjangwFVf1/mU91TqwknpeefmNLsCwmc3iiHJNK9us=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jJjangwFVf1/mU91TqwknpeefmNLsCwmc3iiHJNK9us="}]}, {"Route": "_framework/System.Net.Http.wasm.gz", "AssetFile": "_framework/System.Net.Http.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "110815"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"irBHtodFcqlrGfHVrn2F/hAmqM+joe8np1Ov8oRyA0E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-irBHtodFcqlrGfHVrn2F/hAmqM+joe8np1Ov8oRyA0E="}]}, {"Route": "_framework/System.Net.HttpListener.wasm", "AssetFile": "_framework/System.Net.HttpListener.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44309"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"udbFnO8Urnk15FCjB59S1LvfWy63NKGa+V3lK5nr0Vg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-udbFnO8Urnk15FCjB59S1LvfWy63NKGa+V3lK5nr0Vg="}]}, {"Route": "_framework/System.Net.HttpListener.wasm", "AssetFile": "_framework/System.Net.HttpListener.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064712354"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15452"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pxpz+1cTDE4Vd/jT/3BvYbV94LdkENGItd247CwRW0g=\""}, {"Name": "ETag", "Value": "W/\"udbFnO8Urnk15FCjB59S1LvfWy63NKGa+V3lK5nr0Vg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-udbFnO8Urnk15FCjB59S1LvfWy63NKGa+V3lK5nr0Vg="}]}, {"Route": "_framework/System.Net.HttpListener.wasm.gz", "AssetFile": "_framework/System.Net.HttpListener.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15452"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"pxpz+1cTDE4Vd/jT/3BvYbV94LdkENGItd247CwRW0g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pxpz+1cTDE4Vd/jT/3BvYbV94LdkENGItd247CwRW0g="}]}, {"Route": "_framework/System.Net.Mail.wasm", "AssetFile": "_framework/System.Net.Mail.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "92949"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gI+jj4suqZCIaJYxyF/8d/K0y1k+g7a+J2wBKBOQ4Qw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI+jj4suqZCIaJYxyF/8d/K0y1k+g7a+J2wBKBOQ4Qw="}]}, {"Route": "_framework/System.Net.Mail.wasm", "AssetFile": "_framework/System.Net.Mail.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000024145841"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "41414"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"42Td+hVzzpdPRTgJZz9gfq1k7A1rFCODhyKvrd7XqRA=\""}, {"Name": "ETag", "Value": "W/\"gI+jj4suqZCIaJYxyF/8d/K0y1k+g7a+J2wBKBOQ4Qw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gI+jj4suqZCIaJYxyF/8d/K0y1k+g7a+J2wBKBOQ4Qw="}]}, {"Route": "_framework/System.Net.Mail.wasm.gz", "AssetFile": "_framework/System.Net.Mail.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "41414"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"42Td+hVzzpdPRTgJZz9gfq1k7A1rFCODhyKvrd7XqRA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-42Td+hVzzpdPRTgJZz9gfq1k7A1rFCODhyKvrd7XqRA="}]}, {"Route": "_framework/System.Net.NameResolution.wasm", "AssetFile": "_framework/System.Net.NameResolution.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12565"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"opAswh0YmnABZqTYzFGPk5xFPL8nZsVfiZFTr8AwTx4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-opAswh0YmnABZqTYzFGPk5xFPL8nZsVfiZFTr8AwTx4="}]}, {"Route": "_framework/System.Net.NameResolution.wasm", "AssetFile": "_framework/System.Net.NameResolution.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000187195807"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5341"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"TIRxxDULsXUHJpeO+ekS0OUGVHt18KadY9B+7dvnZtA=\""}, {"Name": "ETag", "Value": "W/\"opAswh0YmnABZqTYzFGPk5xFPL8nZsVfiZFTr8AwTx4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-opAswh0YmnABZqTYzFGPk5xFPL8nZsVfiZFTr8AwTx4="}]}, {"Route": "_framework/System.Net.NameResolution.wasm.gz", "AssetFile": "_framework/System.Net.NameResolution.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5341"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"TIRxxDULsXUHJpeO+ekS0OUGVHt18KadY9B+7dvnZtA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TIRxxDULsXUHJpeO+ekS0OUGVHt18KadY9B+7dvnZtA="}]}, {"Route": "_framework/System.Net.NetworkInformation.wasm", "AssetFile": "_framework/System.Net.NetworkInformation.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30485"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"scm+BvRDA+t4ndJjiDIYeiJh+U2eNIWIiXwufD21F2g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-scm+BvRDA+t4ndJjiDIYeiJh+U2eNIWIiXwufD21F2g="}]}, {"Route": "_framework/System.Net.NetworkInformation.wasm", "AssetFile": "_framework/System.Net.NetworkInformation.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082338411"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12144"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cWe8LRQRGYBa3S9JDhcdIRl5MucrktRbVkdEROVR3Qs=\""}, {"Name": "ETag", "Value": "W/\"scm+BvRDA+t4ndJjiDIYeiJh+U2eNIWIiXwufD21F2g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-scm+BvRDA+t4ndJjiDIYeiJh+U2eNIWIiXwufD21F2g="}]}, {"Route": "_framework/System.Net.NetworkInformation.wasm.gz", "AssetFile": "_framework/System.Net.NetworkInformation.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12144"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cWe8LRQRGYBa3S9JDhcdIRl5MucrktRbVkdEROVR3Qs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWe8LRQRGYBa3S9JDhcdIRl5MucrktRbVkdEROVR3Qs="}]}, {"Route": "_framework/System.Net.Ping.wasm", "AssetFile": "_framework/System.Net.Ping.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"G7NtYG0IaV9ivM/1WQ2htK24uLkyFYLZa5NtWg+FEb8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G7NtYG0IaV9ivM/1WQ2htK24uLkyFYLZa5NtWg+FEb8="}]}, {"Route": "_framework/System.Net.Ping.wasm", "AssetFile": "_framework/System.Net.Ping.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000144112985"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6938"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"K5PiErhj8zrb3UuaouBPzsaP8p9f8EFcDl0hXlkCjg4=\""}, {"Name": "ETag", "Value": "W/\"G7NtYG0IaV9ivM/1WQ2htK24uLkyFYLZa5NtWg+FEb8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G7NtYG0IaV9ivM/1WQ2htK24uLkyFYLZa5NtWg+FEb8="}]}, {"Route": "_framework/System.Net.Ping.wasm.gz", "AssetFile": "_framework/System.Net.Ping.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6938"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"K5PiErhj8zrb3UuaouBPzsaP8p9f8EFcDl0hXlkCjg4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K5PiErhj8zrb3UuaouBPzsaP8p9f8EFcDl0hXlkCjg4="}]}, {"Route": "_framework/System.Net.Primitives.wasm", "AssetFile": "_framework/System.Net.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "93973"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Jlh2x1oemwcwceemUPX1GYIb5m9NNtbZfLDO5whoOLk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jlh2x1oemwcwceemUPX1GYIb5m9NNtbZfLDO5whoOLk="}]}, {"Route": "_framework/System.Net.Primitives.wasm", "AssetFile": "_framework/System.Net.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022224692"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44994"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SG9sGYqz8Xo/C141YmMWjZo3fARS82SlKAjsBhSd2ts=\""}, {"Name": "ETag", "Value": "W/\"Jlh2x1oemwcwceemUPX1GYIb5m9NNtbZfLDO5whoOLk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jlh2x1oemwcwceemUPX1GYIb5m9NNtbZfLDO5whoOLk="}]}, {"Route": "_framework/System.Net.Primitives.wasm.gz", "AssetFile": "_framework/System.Net.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44994"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SG9sGYqz8Xo/C141YmMWjZo3fARS82SlKAjsBhSd2ts=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SG9sGYqz8Xo/C141YmMWjZo3fARS82SlKAjsBhSd2ts="}]}, {"Route": "_framework/System.Net.Quic.wasm", "AssetFile": "_framework/System.Net.Quic.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27413"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mN8HdWUrPLxLtB5mVfKHJSlFcspbFPmFrlSvRRGAteQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mN8HdWUrPLxLtB5mVfKHJSlFcspbFPmFrlSvRRGAteQ="}]}, {"Route": "_framework/System.Net.Quic.wasm", "AssetFile": "_framework/System.Net.Quic.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000094741829"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10554"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WckCVw1BVi4+e5KIXinM7cEg9LHxCG5DZVESc3uDuoQ=\""}, {"Name": "ETag", "Value": "W/\"mN8HdWUrPLxLtB5mVfKHJSlFcspbFPmFrlSvRRGAteQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mN8HdWUrPLxLtB5mVfKHJSlFcspbFPmFrlSvRRGAteQ="}]}, {"Route": "_framework/System.Net.Quic.wasm.gz", "AssetFile": "_framework/System.Net.Quic.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10554"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WckCVw1BVi4+e5KIXinM7cEg9LHxCG5DZVESc3uDuoQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WckCVw1BVi4+e5KIXinM7cEg9LHxCG5DZVESc3uDuoQ="}]}, {"Route": "_framework/System.Net.Requests.wasm", "AssetFile": "_framework/System.Net.Requests.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49941"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9NTSOXjjJreDf31TApKa+reIf+CHb9Z52JOqRHkR3iw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9NTSOXjjJreDf31TApKa+reIf+CHb9Z52JOqRHkR3iw="}]}, {"Route": "_framework/System.Net.Requests.wasm", "AssetFile": "_framework/System.Net.Requests.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000055114638"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18143"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mxk3oetbGxQy0MNWlOyr6ClfVF32ZRPEV+beKSk0cP0=\""}, {"Name": "ETag", "Value": "W/\"9NTSOXjjJreDf31TApKa+reIf+CHb9Z52JOqRHkR3iw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9NTSOXjjJreDf31TApKa+reIf+CHb9Z52JOqRHkR3iw="}]}, {"Route": "_framework/System.Net.Requests.wasm.gz", "AssetFile": "_framework/System.Net.Requests.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18143"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mxk3oetbGxQy0MNWlOyr6ClfVF32ZRPEV+beKSk0cP0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mxk3oetbGxQy0MNWlOyr6ClfVF32ZRPEV+beKSk0cP0="}]}, {"Route": "_framework/System.Net.Security.wasm", "AssetFile": "_framework/System.Net.Security.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "97557"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rA4feRFGZGbkGfz6gHbVWYkcEyD+e8Mnb6lmAG+c4tE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rA4feRFGZGbkGfz6gHbVWYkcEyD+e8Mnb6lmAG+c4tE="}]}, {"Route": "_framework/System.Net.Security.wasm", "AssetFile": "_framework/System.Net.Security.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000031659596"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31585"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"H5r4GXI/8FreA+4th7YWPvBF736RXYMPa3IoEjKyiGo=\""}, {"Name": "ETag", "Value": "W/\"rA4feRFGZGbkGfz6gHbVWYkcEyD+e8Mnb6lmAG+c4tE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rA4feRFGZGbkGfz6gHbVWYkcEyD+e8Mnb6lmAG+c4tE="}]}, {"Route": "_framework/System.Net.Security.wasm.gz", "AssetFile": "_framework/System.Net.Security.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31585"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"H5r4GXI/8FreA+4th7YWPvBF736RXYMPa3IoEjKyiGo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H5r4GXI/8FreA+4th7YWPvBF736RXYMPa3IoEjKyiGo="}]}, {"Route": "_framework/System.Net.ServicePoint.wasm", "AssetFile": "_framework/System.Net.ServicePoint.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15637"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"247wyddQpQIp+CyjKIP8c5yCVzpAEGm7rsPk7z5i1kU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-247wyddQpQIp+CyjKIP8c5yCVzpAEGm7rsPk7z5i1kU="}]}, {"Route": "_framework/System.Net.ServicePoint.wasm", "AssetFile": "_framework/System.Net.ServicePoint.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000138140627"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7238"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Lx9kaEJKoGzMTaE+N/OVdjn6Fk6ycNpQiq9fUV6H9to=\""}, {"Name": "ETag", "Value": "W/\"247wyddQpQIp+CyjKIP8c5yCVzpAEGm7rsPk7z5i1kU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-247wyddQpQIp+CyjKIP8c5yCVzpAEGm7rsPk7z5i1kU="}]}, {"Route": "_framework/System.Net.ServicePoint.wasm.gz", "AssetFile": "_framework/System.Net.ServicePoint.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7238"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Lx9kaEJKoGzMTaE+N/OVdjn6Fk6ycNpQiq9fUV6H9to=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lx9kaEJKoGzMTaE+N/OVdjn6Fk6ycNpQiq9fUV6H9to="}]}, {"Route": "_framework/System.Net.Sockets.wasm", "AssetFile": "_framework/System.Net.Sockets.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "62741"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"6LNyhzYdfttL+4s+NkuByFcfXNK2L3c949nkD4JBBKo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6LNyhzYdfttL+4s+NkuByFcfXNK2L3c949nkD4JBBKo="}]}, {"Route": "_framework/System.Net.Sockets.wasm", "AssetFile": "_framework/System.Net.Sockets.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000044525580"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "22458"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"FnnEvDXcUn4dMa4cZqiDsiMoUgZLUm0fJ+VQpn9Cy8M=\""}, {"Name": "ETag", "Value": "W/\"6LNyhzYdfttL+4s+NkuByFcfXNK2L3c949nkD4JBBKo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6LNyhzYdfttL+4s+NkuByFcfXNK2L3c949nkD4JBBKo="}]}, {"Route": "_framework/System.Net.Sockets.wasm.gz", "AssetFile": "_framework/System.Net.Sockets.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "22458"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"FnnEvDXcUn4dMa4cZqiDsiMoUgZLUm0fJ+VQpn9Cy8M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FnnEvDXcUn4dMa4cZqiDsiMoUgZLUm0fJ+VQpn9Cy8M="}]}, {"Route": "_framework/System.Net.WebClient.wasm", "AssetFile": "_framework/System.Net.WebClient.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41749"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"0pdNs9MqVVBwZ/Qg+R1SJzNok2+OJ1h1Ke7xLaHAfpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0pdNs9MqVVBwZ/Qg+R1SJzNok2+OJ1h1Ke7xLaHAfpc="}]}, {"Route": "_framework/System.Net.WebClient.wasm", "AssetFile": "_framework/System.Net.WebClient.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000074956900"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13340"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ivB+xxaeuCMZI3PJRMtapzVZw+kC1Kl3rCszK+Zw0e0=\""}, {"Name": "ETag", "Value": "W/\"0pdNs9MqVVBwZ/Qg+R1SJzNok2+OJ1h1Ke7xLaHAfpc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0pdNs9MqVVBwZ/Qg+R1SJzNok2+OJ1h1Ke7xLaHAfpc="}]}, {"Route": "_framework/System.Net.WebClient.wasm.gz", "AssetFile": "_framework/System.Net.WebClient.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13340"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ivB+xxaeuCMZI3PJRMtapzVZw+kC1Kl3rCszK+Zw0e0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ivB+xxaeuCMZI3PJRMtapzVZw+kC1Kl3rCszK+Zw0e0="}]}, {"Route": "_framework/System.Net.WebHeaderCollection.wasm", "AssetFile": "_framework/System.Net.WebHeaderCollection.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22805"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"lGL+b/LFtRgoxEZMJEW//4YJgxDpvr2uYkPo887fQ54=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lGL+b/LFtRgoxEZMJEW//4YJgxDpvr2uYkPo887fQ54="}]}, {"Route": "_framework/System.Net.WebHeaderCollection.wasm", "AssetFile": "_framework/System.Net.WebHeaderCollection.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000093976130"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10640"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GE5720By+Ry8XvzT86FP7T6RjLz2/fu+bbWKjcr24oU=\""}, {"Name": "ETag", "Value": "W/\"lGL+b/LFtRgoxEZMJEW//4YJgxDpvr2uYkPo887fQ54=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lGL+b/LFtRgoxEZMJEW//4YJgxDpvr2uYkPo887fQ54="}]}, {"Route": "_framework/System.Net.WebHeaderCollection.wasm.gz", "AssetFile": "_framework/System.Net.WebHeaderCollection.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10640"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GE5720By+Ry8XvzT86FP7T6RjLz2/fu+bbWKjcr24oU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GE5720By+Ry8XvzT86FP7T6RjLz2/fu+bbWKjcr24oU="}]}, {"Route": "_framework/System.Net.WebProxy.wasm", "AssetFile": "_framework/System.Net.WebProxy.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11541"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eTb/ztDu5oZboqoaBcJ20qYlxoNkJ4Ro9C9SucCdgps=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eTb/ztDu5oZboqoaBcJ20qYlxoNkJ4Ro9C9SucCdgps="}]}, {"Route": "_framework/System.Net.WebProxy.wasm", "AssetFile": "_framework/System.Net.WebProxy.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000178380307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5605"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"d/YAAeCuqZEl5XWMrnwMuAXCtGQt7CkIPSZYeWBQLjg=\""}, {"Name": "ETag", "Value": "W/\"eTb/ztDu5oZboqoaBcJ20qYlxoNkJ4Ro9C9SucCdgps=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eTb/ztDu5oZboqoaBcJ20qYlxoNkJ4Ro9C9SucCdgps="}]}, {"Route": "_framework/System.Net.WebProxy.wasm.gz", "AssetFile": "_framework/System.Net.WebProxy.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5605"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"d/YAAeCuqZEl5XWMrnwMuAXCtGQt7CkIPSZYeWBQLjg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d/YAAeCuqZEl5XWMrnwMuAXCtGQt7CkIPSZYeWBQLjg="}]}, {"Route": "_framework/System.Net.WebSockets.Client.wasm", "AssetFile": "_framework/System.Net.WebSockets.Client.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36629"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YRXLy61QYB6DLCFORO8zPtlMxdbTTSUsQWTam9XBEtQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YRXLy61QYB6DLCFORO8zPtlMxdbTTSUsQWTam9XBEtQ="}]}, {"Route": "_framework/System.Net.WebSockets.Client.wasm", "AssetFile": "_framework/System.Net.WebSockets.Client.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000062332481"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16042"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M6tLcBhTm2ykHT2eReAjSR6a+fGfMF6uFoATYm5mcdE=\""}, {"Name": "ETag", "Value": "W/\"YRXLy61QYB6DLCFORO8zPtlMxdbTTSUsQWTam9XBEtQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YRXLy61QYB6DLCFORO8zPtlMxdbTTSUsQWTam9XBEtQ="}]}, {"Route": "_framework/System.Net.WebSockets.Client.wasm.gz", "AssetFile": "_framework/System.Net.WebSockets.Client.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16042"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M6tLcBhTm2ykHT2eReAjSR6a+fGfMF6uFoATYm5mcdE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M6tLcBhTm2ykHT2eReAjSR6a+fGfMF6uFoATYm5mcdE="}]}, {"Route": "_framework/System.Net.WebSockets.wasm", "AssetFile": "_framework/System.Net.WebSockets.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70933"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+3IJ50Hy356UgL+F7x6qemAC3B57uR9wU5w1tOT/ynQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+3IJ50Hy356UgL+F7x6qemAC3B57uR9wU5w1tOT/ynQ="}]}, {"Route": "_framework/System.Net.WebSockets.wasm", "AssetFile": "_framework/System.Net.WebSockets.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032105821"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31146"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"sA6kjD9telC6bj4ZeeuKEabbIJNk44Hq7YyWYtMJDP4=\""}, {"Name": "ETag", "Value": "W/\"+3IJ50Hy356UgL+F7x6qemAC3B57uR9wU5w1tOT/ynQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+3IJ50Hy356UgL+F7x6qemAC3B57uR9wU5w1tOT/ynQ="}]}, {"Route": "_framework/System.Net.WebSockets.wasm.gz", "AssetFile": "_framework/System.Net.WebSockets.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "31146"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"sA6kjD9telC6bj4ZeeuKEabbIJNk44Hq7YyWYtMJDP4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sA6kjD9telC6bj4ZeeuKEabbIJNk44Hq7YyWYtMJDP4="}]}, {"Route": "_framework/System.Net.wasm", "AssetFile": "_framework/System.Net.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6933"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mpF8HY0TvuKuYpcoghIc34LASNj/Nzw8nHtasKFF/OQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mpF8HY0TvuKuYpcoghIc34LASNj/Nzw8nHtasKFF/OQ="}]}, {"Route": "_framework/System.Net.wasm", "AssetFile": "_framework/System.Net.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000365497076"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2735"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"W6A6SpP6RII5ff5B2E6AiACun+VczOB3MRVC3g0zfkk=\""}, {"Name": "ETag", "Value": "W/\"mpF8HY0TvuKuYpcoghIc34LASNj/Nzw8nHtasKFF/OQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mpF8HY0TvuKuYpcoghIc34LASNj/Nzw8nHtasKFF/OQ="}]}, {"Route": "_framework/System.Net.wasm.gz", "AssetFile": "_framework/System.Net.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2735"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"W6A6SpP6RII5ff5B2E6AiACun+VczOB3MRVC3g0zfkk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W6A6SpP6RII5ff5B2E6AiACun+VczOB3MRVC3g0zfkk="}]}, {"Route": "_framework/System.Numerics.Vectors.wasm", "AssetFile": "_framework/System.Numerics.Vectors.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"k29w4qq4d96s5z5Bs668kHjbj6zfPzk3x8DnSYuvg6M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k29w4qq4d96s5z5Bs668kHjbj6zfPzk3x8DnSYuvg6M="}]}, {"Route": "_framework/System.Numerics.Vectors.wasm", "AssetFile": "_framework/System.Numerics.Vectors.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000443066017"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2256"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZoJ/TdoxzYZ4PpY41BhC60K7LD2CptQJFE5O0Wdwuek=\""}, {"Name": "ETag", "Value": "W/\"k29w4qq4d96s5z5Bs668kHjbj6zfPzk3x8DnSYuvg6M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k29w4qq4d96s5z5Bs668kHjbj6zfPzk3x8DnSYuvg6M="}]}, {"Route": "_framework/System.Numerics.Vectors.wasm.gz", "AssetFile": "_framework/System.Numerics.Vectors.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2256"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZoJ/TdoxzYZ4PpY41BhC60K7LD2CptQJFE5O0Wdwuek=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZoJ/TdoxzYZ4PpY41BhC60K7LD2CptQJFE5O0Wdwuek="}]}, {"Route": "_framework/System.Numerics.wasm", "AssetFile": "_framework/System.Numerics.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9gM481auPMnXu9uJNKm0V2jzk+/xNNlO6YSOqw0MWpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9gM481auPMnXu9uJNKm0V2jzk+/xNNlO6YSOqw0MWpw="}]}, {"Route": "_framework/System.Numerics.wasm", "AssetFile": "_framework/System.Numerics.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000496031746"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2015"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"tFl6WM57JlwE1ItEyH903MlWMMKlKNP7CMmU53XFBBE=\""}, {"Name": "ETag", "Value": "W/\"9gM481auPMnXu9uJNKm0V2jzk+/xNNlO6YSOqw0MWpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9gM481auPMnXu9uJNKm0V2jzk+/xNNlO6YSOqw0MWpw="}]}, {"Route": "_framework/System.Numerics.wasm.gz", "AssetFile": "_framework/System.Numerics.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2015"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"tFl6WM57JlwE1ItEyH903MlWMMKlKNP7CMmU53XFBBE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tFl6WM57JlwE1ItEyH903MlWMMKlKNP7CMmU53XFBBE="}]}, {"Route": "_framework/System.ObjectModel.wasm", "AssetFile": "_framework/System.ObjectModel.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "28949"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"xzz6mrUR8zTYmZulWUw1oo3NjwREGvVex2O/4UB1ltk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xzz6mrUR8zTYmZulWUw1oo3NjwREGvVex2O/4UB1ltk="}]}, {"Route": "_framework/System.ObjectModel.wasm", "AssetFile": "_framework/System.ObjectModel.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079314721"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12607"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cvIdgSRFkh5SCFtb9CdC1ntjQLmclQ5ojg+YNqwTWsU=\""}, {"Name": "ETag", "Value": "W/\"xzz6mrUR8zTYmZulWUw1oo3NjwREGvVex2O/4UB1ltk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xzz6mrUR8zTYmZulWUw1oo3NjwREGvVex2O/4UB1ltk="}]}, {"Route": "_framework/System.ObjectModel.wasm.gz", "AssetFile": "_framework/System.ObjectModel.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12607"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cvIdgSRFkh5SCFtb9CdC1ntjQLmclQ5ojg+YNqwTWsU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cvIdgSRFkh5SCFtb9CdC1ntjQLmclQ5ojg+YNqwTWsU="}]}, {"Route": "_framework/System.Private.CoreLib.wasm", "AssetFile": "_framework/System.Private.CoreLib.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4199705"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"oBqNgH7R24FqGbaL3XnfQDqn3rk28jERFosgAryKT6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oBqNgH7R24FqGbaL3XnfQDqn3rk28jERFosgAryKT6U="}]}, {"Route": "_framework/System.Private.CoreLib.wasm", "AssetFile": "_framework/System.Private.CoreLib.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000000717069"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1394565"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UW7y7C77Oenw0raRY2CZHbQ6jIObANRBBvT8/exzOcY=\""}, {"Name": "ETag", "Value": "W/\"oBqNgH7R24FqGbaL3XnfQDqn3rk28jERFosgAryKT6U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oBqNgH7R24FqGbaL3XnfQDqn3rk28jERFosgAryKT6U="}]}, {"Route": "_framework/System.Private.CoreLib.wasm.gz", "AssetFile": "_framework/System.Private.CoreLib.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1394565"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UW7y7C77Oenw0raRY2CZHbQ6jIObANRBBvT8/exzOcY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW7y7C77Oenw0raRY2CZHbQ6jIObANRBBvT8/exzOcY="}]}, {"Route": "_framework/System.Private.DataContractSerialization.wasm", "AssetFile": "_framework/System.Private.DataContractSerialization.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "843541"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"z/s+VApirx8CNeL4y0Yw8T1dOUSQoQzYAHHs1bIGRXE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z/s+VApirx8CNeL4y0Yw8T1dOUSQoQzYAHHs1bIGRXE="}]}, {"Route": "_framework/System.Private.DataContractSerialization.wasm", "AssetFile": "_framework/System.Private.DataContractSerialization.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003320715"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "301139"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Hfylz9Y5HXlH75DJZo/vyUAwXKadWjaVHRGMWBOak+c=\""}, {"Name": "ETag", "Value": "W/\"z/s+VApirx8CNeL4y0Yw8T1dOUSQoQzYAHHs1bIGRXE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z/s+VApirx8CNeL4y0Yw8T1dOUSQoQzYAHHs1bIGRXE="}]}, {"Route": "_framework/System.Private.DataContractSerialization.wasm.gz", "AssetFile": "_framework/System.Private.DataContractSerialization.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "301139"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Hfylz9Y5HXlH75DJZo/vyUAwXKadWjaVHRGMWBOak+c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hfylz9Y5HXlH75DJZo/vyUAwXKadWjaVHRGMWBOak+c="}]}, {"Route": "_framework/System.Private.Uri.wasm", "AssetFile": "_framework/System.Private.Uri.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91413"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"70kjB71ui4apkA0a/ltHBEJBrq0OFGWPLeWSlkQJ7y4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-70kjB71ui4apkA0a/ltHBEJBrq0OFGWPLeWSlkQJ7y4="}]}, {"Route": "_framework/System.Private.Uri.wasm", "AssetFile": "_framework/System.Private.Uri.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000024304880"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "41143"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"oLFxn02F9Nd5QIyWs/p4UfctfF/sW/5tbj3o+M9RMwM=\""}, {"Name": "ETag", "Value": "W/\"70kjB71ui4apkA0a/ltHBEJBrq0OFGWPLeWSlkQJ7y4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-70kjB71ui4apkA0a/ltHBEJBrq0OFGWPLeWSlkQJ7y4="}]}, {"Route": "_framework/System.Private.Uri.wasm.gz", "AssetFile": "_framework/System.Private.Uri.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "41143"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"oLFxn02F9Nd5QIyWs/p4UfctfF/sW/5tbj3o+M9RMwM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oLFxn02F9Nd5QIyWs/p4UfctfF/sW/5tbj3o+M9RMwM="}]}, {"Route": "_framework/System.Private.Xml.Linq.wasm", "AssetFile": "_framework/System.Private.Xml.Linq.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "142613"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ekbaVVApxUcZpsRgQR7ajm2dE7i5XYH1wMWrEnW9m6g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ekbaVVApxUcZpsRgQR7ajm2dE7i5XYH1wMWrEnW9m6g="}]}, {"Route": "_framework/System.Private.Xml.Linq.wasm", "AssetFile": "_framework/System.Private.Xml.Linq.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017068633"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "58586"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JcMDwIddFyIdoO9Osj1s/sAg7XwG6m0KGxKpYpARjrE=\""}, {"Name": "ETag", "Value": "W/\"ekbaVVApxUcZpsRgQR7ajm2dE7i5XYH1wMWrEnW9m6g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ekbaVVApxUcZpsRgQR7ajm2dE7i5XYH1wMWrEnW9m6g="}]}, {"Route": "_framework/System.Private.Xml.Linq.wasm.gz", "AssetFile": "_framework/System.Private.Xml.Linq.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "58586"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JcMDwIddFyIdoO9Osj1s/sAg7XwG6m0KGxKpYpARjrE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JcMDwIddFyIdoO9Osj1s/sAg7XwG6m0KGxKpYpARjrE="}]}, {"Route": "_framework/System.Private.Xml.wasm", "AssetFile": "_framework/System.Private.Xml.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3106073"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"C1klc/+cxirxQZiUf6bXVPLRlVwF0E/IHxfQ9whLNNE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C1klc/+cxirxQZiUf6bXVPLRlVwF0E/IHxfQ9whLNNE="}]}, {"Route": "_framework/System.Private.Xml.wasm", "AssetFile": "_framework/System.Private.Xml.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000000940760"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1062969"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZTraNrZbvsNWCIvXUHeWQfqTOhYSgRJa/SK6VVl45cI=\""}, {"Name": "ETag", "Value": "W/\"C1klc/+cxirxQZiUf6bXVPLRlVwF0E/IHxfQ9whLNNE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C1klc/+cxirxQZiUf6bXVPLRlVwF0E/IHxfQ9whLNNE="}]}, {"Route": "_framework/System.Private.Xml.wasm.gz", "AssetFile": "_framework/System.Private.Xml.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1062969"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZTraNrZbvsNWCIvXUHeWQfqTOhYSgRJa/SK6VVl45cI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZTraNrZbvsNWCIvXUHeWQfqTOhYSgRJa/SK6VVl45cI="}]}, {"Route": "_framework/System.Reflection.DispatchProxy.wasm", "AssetFile": "_framework/System.Reflection.DispatchProxy.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3rAQ3O+GGqTYvIukwTxu9moHrbyk5tmBBk/XZj6lpiY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3rAQ3O+GGqTYvIukwTxu9moHrbyk5tmBBk/XZj6lpiY="}]}, {"Route": "_framework/System.Reflection.DispatchProxy.wasm", "AssetFile": "_framework/System.Reflection.DispatchProxy.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082637799"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yWdV3Jxsy7SySv4kL4vZmOEc9uF/UtzkaEPLjT48Wkg=\""}, {"Name": "ETag", "Value": "W/\"3rAQ3O+GGqTYvIukwTxu9moHrbyk5tmBBk/XZj6lpiY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3rAQ3O+GGqTYvIukwTxu9moHrbyk5tmBBk/XZj6lpiY="}]}, {"Route": "_framework/System.Reflection.DispatchProxy.wasm.gz", "AssetFile": "_framework/System.Reflection.DispatchProxy.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12100"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yWdV3Jxsy7SySv4kL4vZmOEc9uF/UtzkaEPLjT48Wkg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yWdV3Jxsy7SySv4kL4vZmOEc9uF/UtzkaEPLjT48Wkg="}]}, {"Route": "_framework/System.Reflection.Emit.ILGeneration.wasm", "AssetFile": "_framework/System.Reflection.Emit.ILGeneration.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ks+ewjxLw42KCZG7duli51X5ICLqTI0dHgcvno7pkJg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ks+ewjxLw42KCZG7duli51X5ICLqTI0dHgcvno7pkJg="}]}, {"Route": "_framework/System.Reflection.Emit.ILGeneration.wasm", "AssetFile": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000440334654"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2270"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"BnUEt3yxMJsHWRi6cyDqp564rRT+NYAXAOxer5Kg650=\""}, {"Name": "ETag", "Value": "W/\"ks+ewjxLw42KCZG7duli51X5ICLqTI0dHgcvno7pkJg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ks+ewjxLw42KCZG7duli51X5ICLqTI0dHgcvno7pkJg="}]}, {"Route": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "AssetFile": "_framework/System.Reflection.Emit.ILGeneration.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2270"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"BnUEt3yxMJsHWRi6cyDqp564rRT+NYAXAOxer5Kg650=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BnUEt3yxMJsHWRi6cyDqp564rRT+NYAXAOxer5Kg650="}]}, {"Route": "_framework/System.Reflection.Emit.Lightweight.wasm", "AssetFile": "_framework/System.Reflection.Emit.Lightweight.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5FuwUuzqaTQHNtz1kFbIP5nwVfoETSwd5QcDhs8kATE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FuwUuzqaTQHNtz1kFbIP5nwVfoETSwd5QcDhs8kATE="}]}, {"Route": "_framework/System.Reflection.Emit.Lightweight.wasm", "AssetFile": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000449842555"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2222"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"R+Yy/ul2kJNhAFszJdIjgzjsUQjEmsW+q0lTDZeIJl0=\""}, {"Name": "ETag", "Value": "W/\"5FuwUuzqaTQHNtz1kFbIP5nwVfoETSwd5QcDhs8kATE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FuwUuzqaTQHNtz1kFbIP5nwVfoETSwd5QcDhs8kATE="}]}, {"Route": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "AssetFile": "_framework/System.Reflection.Emit.Lightweight.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2222"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"R+Yy/ul2kJNhAFszJdIjgzjsUQjEmsW+q0lTDZeIJl0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R+Yy/ul2kJNhAFszJdIjgzjsUQjEmsW+q0lTDZeIJl0="}]}, {"Route": "_framework/System.Reflection.Emit.wasm", "AssetFile": "_framework/System.Reflection.Emit.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "61205"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"geH3kVi9jqZWu+V/h6FQ3v0/6/KyIajblriiFr2ob0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geH3kVi9jqZWu+V/h6FQ3v0/6/KyIajblriiFr2ob0Q="}]}, {"Route": "_framework/System.Reflection.Emit.wasm", "AssetFile": "_framework/System.Reflection.Emit.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000037220382"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26866"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1D4KYla2SSuWwx6Nnq7dmNQ0ORUKbKLybDjz8Fo/5V4=\""}, {"Name": "ETag", "Value": "W/\"geH3kVi9jqZWu+V/h6FQ3v0/6/KyIajblriiFr2ob0Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geH3kVi9jqZWu+V/h6FQ3v0/6/KyIajblriiFr2ob0Q="}]}, {"Route": "_framework/System.Reflection.Emit.wasm.gz", "AssetFile": "_framework/System.Reflection.Emit.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "26866"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1D4KYla2SSuWwx6Nnq7dmNQ0ORUKbKLybDjz8Fo/5V4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1D4KYla2SSuWwx6Nnq7dmNQ0ORUKbKLybDjz8Fo/5V4="}]}, {"Route": "_framework/System.Reflection.Extensions.wasm", "AssetFile": "_framework/System.Reflection.Extensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/5ZY/KPSc9+sqLIliNfILf4WUGtVZ81IGSrKgoEM4EY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/5ZY/KPSc9+sqLIliNfILf4WUGtVZ81IGSrKgoEM4EY="}]}, {"Route": "_framework/System.Reflection.Extensions.wasm", "AssetFile": "_framework/System.Reflection.Extensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000467289720"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2139"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ABX/nLYc9IHepfviba/Ecl/YXa9OLHrSAnHRC6ULOdA=\""}, {"Name": "ETag", "Value": "W/\"/5ZY/KPSc9+sqLIliNfILf4WUGtVZ81IGSrKgoEM4EY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/5ZY/KPSc9+sqLIliNfILf4WUGtVZ81IGSrKgoEM4EY="}]}, {"Route": "_framework/System.Reflection.Extensions.wasm.gz", "AssetFile": "_framework/System.Reflection.Extensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2139"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ABX/nLYc9IHepfviba/Ecl/YXa9OLHrSAnHRC6ULOdA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ABX/nLYc9IHepfviba/Ecl/YXa9OLHrSAnHRC6ULOdA="}]}, {"Route": "_framework/System.Reflection.Metadata.wasm", "AssetFile": "_framework/System.Reflection.Metadata.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "465173"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1itL2/O+B6b8qaV/sFE7Rht+GwEFvjq8djkUPiy8Fpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1itL2/O+B6b8qaV/sFE7Rht+GwEFvjq8djkUPiy8Fpw="}]}, {"Route": "_framework/System.Reflection.Metadata.wasm", "AssetFile": "_framework/System.Reflection.Metadata.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000005541210"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "180465"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NnIvjIzU3eyCrj16pOWYzJSdkUpv9KQi7QMgUdu8cus=\""}, {"Name": "ETag", "Value": "W/\"1itL2/O+B6b8qaV/sFE7Rht+GwEFvjq8djkUPiy8Fpw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1itL2/O+B6b8qaV/sFE7Rht+GwEFvjq8djkUPiy8Fpw="}]}, {"Route": "_framework/System.Reflection.Metadata.wasm.gz", "AssetFile": "_framework/System.Reflection.Metadata.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "180465"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NnIvjIzU3eyCrj16pOWYzJSdkUpv9KQi7QMgUdu8cus=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NnIvjIzU3eyCrj16pOWYzJSdkUpv9KQi7QMgUdu8cus="}]}, {"Route": "_framework/System.Reflection.Primitives.wasm", "AssetFile": "_framework/System.Reflection.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s41Okai85RHvjhrt9ISlChgEOYRot1LCWtmzzF8+FSc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s41Okai85RHvjhrt9ISlChgEOYRot1LCWtmzzF8+FSc="}]}, {"Route": "_framework/System.Reflection.Primitives.wasm", "AssetFile": "_framework/System.Reflection.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000425170068"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2351"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fsME1d1lKN4WN3EySREmBQrzfHnKKs9IcwU50ZEqAFI=\""}, {"Name": "ETag", "Value": "W/\"s41Okai85RHvjhrt9ISlChgEOYRot1LCWtmzzF8+FSc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s41Okai85RHvjhrt9ISlChgEOYRot1LCWtmzzF8+FSc="}]}, {"Route": "_framework/System.Reflection.Primitives.wasm.gz", "AssetFile": "_framework/System.Reflection.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2351"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fsME1d1lKN4WN3EySREmBQrzfHnKKs9IcwU50ZEqAFI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fsME1d1lKN4WN3EySREmBQrzfHnKKs9IcwU50ZEqAFI="}]}, {"Route": "_framework/System.Reflection.TypeExtensions.wasm", "AssetFile": "_framework/System.Reflection.TypeExtensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13077"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZvqEzj53F+rUMVI6W+IfKexqNA8ZEqynCpSwK+/iw/o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZvqEzj53F+rUMVI6W+IfKexqNA8ZEqynCpSwK+/iw/o="}]}, {"Route": "_framework/System.Reflection.TypeExtensions.wasm", "AssetFile": "_framework/System.Reflection.TypeExtensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000176647235"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5660"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Z+8lc+iHCQoA0GcqmyFY/OBlJet7TA5kPoTDIMkv53c=\""}, {"Name": "ETag", "Value": "W/\"ZvqEzj53F+rUMVI6W+IfKexqNA8ZEqynCpSwK+/iw/o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZvqEzj53F+rUMVI6W+IfKexqNA8ZEqynCpSwK+/iw/o="}]}, {"Route": "_framework/System.Reflection.TypeExtensions.wasm.gz", "AssetFile": "_framework/System.Reflection.TypeExtensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5660"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Z+8lc+iHCQoA0GcqmyFY/OBlJet7TA5kPoTDIMkv53c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z+8lc+iHCQoA0GcqmyFY/OBlJet7TA5kPoTDIMkv53c="}]}, {"Route": "_framework/System.Reflection.wasm", "AssetFile": "_framework/System.Reflection.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rP6/jzstyouHIhyEAvk6lmCvQpBZn2WVHIE7Mqea+8E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rP6/jzstyouHIhyEAvk6lmCvQpBZn2WVHIE7Mqea+8E="}]}, {"Route": "_framework/System.Reflection.wasm", "AssetFile": "_framework/System.Reflection.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000407996736"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2450"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LgFSMqUB7AI+S44ZoKrzR5/WuOHUCzenwd92BL0oD5k=\""}, {"Name": "ETag", "Value": "W/\"rP6/jzstyouHIhyEAvk6lmCvQpBZn2WVHIE7Mqea+8E=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rP6/jzstyouHIhyEAvk6lmCvQpBZn2WVHIE7Mqea+8E="}]}, {"Route": "_framework/System.Reflection.wasm.gz", "AssetFile": "_framework/System.Reflection.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2450"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LgFSMqUB7AI+S44ZoKrzR5/WuOHUCzenwd92BL0oD5k=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LgFSMqUB7AI+S44ZoKrzR5/WuOHUCzenwd92BL0oD5k="}]}, {"Route": "_framework/System.Resources.Reader.wasm", "AssetFile": "_framework/System.Resources.Reader.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rf32G2+zN2ycUrePDebo4tiUi3YJsK1vIVopDNNEWAA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rf32G2+zN2ycUrePDebo4tiUi3YJsK1vIVopDNNEWAA="}]}, {"Route": "_framework/System.Resources.Reader.wasm", "AssetFile": "_framework/System.Resources.Reader.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000474833808"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2105"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M/yIP43B1SB69b7X0J6fq+0eCfZWfpv1A2JPyIl1eGY=\""}, {"Name": "ETag", "Value": "W/\"rf32G2+zN2ycUrePDebo4tiUi3YJsK1vIVopDNNEWAA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rf32G2+zN2ycUrePDebo4tiUi3YJsK1vIVopDNNEWAA="}]}, {"Route": "_framework/System.Resources.Reader.wasm.gz", "AssetFile": "_framework/System.Resources.Reader.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2105"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"M/yIP43B1SB69b7X0J6fq+0eCfZWfpv1A2JPyIl1eGY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M/yIP43B1SB69b7X0J6fq+0eCfZWfpv1A2JPyIl1eGY="}]}, {"Route": "_framework/System.Resources.ResourceManager.wasm", "AssetFile": "_framework/System.Resources.ResourceManager.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EyQnqNq/9xGc8OTMYlffWA/ZDmseLwdjuf58/n5UBa8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EyQnqNq/9xGc8OTMYlffWA/ZDmseLwdjuf58/n5UBa8="}]}, {"Route": "_framework/System.Resources.ResourceManager.wasm", "AssetFile": "_framework/System.Resources.ResourceManager.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000448631673"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2228"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jczV5MpyJAvM2V9OW9GQxpeQuHa+BDgCzNK6OW7+3wU=\""}, {"Name": "ETag", "Value": "W/\"EyQnqNq/9xGc8OTMYlffWA/ZDmseLwdjuf58/n5UBa8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EyQnqNq/9xGc8OTMYlffWA/ZDmseLwdjuf58/n5UBa8="}]}, {"Route": "_framework/System.Resources.ResourceManager.wasm.gz", "AssetFile": "_framework/System.Resources.ResourceManager.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2228"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jczV5MpyJAvM2V9OW9GQxpeQuHa+BDgCzNK6OW7+3wU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jczV5MpyJAvM2V9OW9GQxpeQuHa+BDgCzNK6OW7+3wU="}]}, {"Route": "_framework/System.Resources.Writer.wasm", "AssetFile": "_framework/System.Resources.Writer.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"QDTdVbKQukgrs+zjyxlmQtEGMz2qK9SFN24jL7nN6JI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QDTdVbKQukgrs+zjyxlmQtEGMz2qK9SFN24jL7nN6JI="}]}, {"Route": "_framework/System.Resources.Writer.wasm", "AssetFile": "_framework/System.Resources.Writer.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131216376"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7620"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OZwhH0OBIpMLnWkxVPPMxD4j9V4da22wy59XFy2H/0Y=\""}, {"Name": "ETag", "Value": "W/\"QDTdVbKQukgrs+zjyxlmQtEGMz2qK9SFN24jL7nN6JI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QDTdVbKQukgrs+zjyxlmQtEGMz2qK9SFN24jL7nN6JI="}]}, {"Route": "_framework/System.Resources.Writer.wasm.gz", "AssetFile": "_framework/System.Resources.Writer.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7620"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OZwhH0OBIpMLnWkxVPPMxD4j9V4da22wy59XFy2H/0Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZwhH0OBIpMLnWkxVPPMxD4j9V4da22wy59XFy2H/0Y="}]}, {"Route": "_framework/System.Runtime.CompilerServices.Unsafe.wasm", "AssetFile": "_framework/System.Runtime.CompilerServices.Unsafe.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uEfdKlBlqQLIOk8jbxQQe5/sGXTEFbRmoimNoBXc8FI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uEfdKlBlqQLIOk8jbxQQe5/sGXTEFbRmoimNoBXc8FI="}]}, {"Route": "_framework/System.Runtime.CompilerServices.Unsafe.wasm", "AssetFile": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000472366556"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2116"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JnmxnP6KQbIZjS1KqcvE8HqYEop9DhRhPf0wJ0TX38A=\""}, {"Name": "ETag", "Value": "W/\"uEfdKlBlqQLIOk8jbxQQe5/sGXTEFbRmoimNoBXc8FI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uEfdKlBlqQLIOk8jbxQQe5/sGXTEFbRmoimNoBXc8FI="}]}, {"Route": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "AssetFile": "_framework/System.Runtime.CompilerServices.Unsafe.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2116"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JnmxnP6KQbIZjS1KqcvE8HqYEop9DhRhPf0wJ0TX38A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JnmxnP6KQbIZjS1KqcvE8HqYEop9DhRhPf0wJ0TX38A="}]}, {"Route": "_framework/System.Runtime.CompilerServices.VisualC.wasm", "AssetFile": "_framework/System.Runtime.CompilerServices.VisualC.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6933"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EtgIM8Y4gu4RfLSk0re9MiRtAMTek5hszKWmcjY1Y2Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EtgIM8Y4gu4RfLSk0re9MiRtAMTek5hszKWmcjY1Y2Q="}]}, {"Route": "_framework/System.Runtime.CompilerServices.VisualC.wasm", "AssetFile": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000329489292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3034"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MDb2PlUB9LGFkafNgIonL7XF+9S5+qKPbUiiMliLD4A=\""}, {"Name": "ETag", "Value": "W/\"EtgIM8Y4gu4RfLSk0re9MiRtAMTek5hszKWmcjY1Y2Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EtgIM8Y4gu4RfLSk0re9MiRtAMTek5hszKWmcjY1Y2Q="}]}, {"Route": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "AssetFile": "_framework/System.Runtime.CompilerServices.VisualC.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3034"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"MDb2PlUB9LGFkafNgIonL7XF+9S5+qKPbUiiMliLD4A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MDb2PlUB9LGFkafNgIonL7XF+9S5+qKPbUiiMliLD4A="}]}, {"Route": "_framework/System.Runtime.Extensions.wasm", "AssetFile": "_framework/System.Runtime.Extensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7445"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"tEaZugoFkicpzE4ztESkuFwyi/eAKJf5mNuNk6Yn6PE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tEaZugoFkicpzE4ztESkuFwyi/eAKJf5mNuNk6Yn6PE="}]}, {"Route": "_framework/System.Runtime.Extensions.wasm", "AssetFile": "_framework/System.Runtime.Extensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000337040782"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2966"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9v6UPYqXRtDtqQZSh0MCqKN7yYMhwXyjflYvALixW2w=\""}, {"Name": "ETag", "Value": "W/\"tEaZugoFkicpzE4ztESkuFwyi/eAKJf5mNuNk6Yn6PE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tEaZugoFkicpzE4ztESkuFwyi/eAKJf5mNuNk6Yn6PE="}]}, {"Route": "_framework/System.Runtime.Extensions.wasm.gz", "AssetFile": "_framework/System.Runtime.Extensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2966"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9v6UPYqXRtDtqQZSh0MCqKN7yYMhwXyjflYvALixW2w=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9v6UPYqXRtDtqQZSh0MCqKN7yYMhwXyjflYvALixW2w="}]}, {"Route": "_framework/System.Runtime.Handles.wasm", "AssetFile": "_framework/System.Runtime.Handles.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KOsdql/UbqwJPWsLjAPd9iTqGHS0NFagZxSUzvnX97A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KOsdql/UbqwJPWsLjAPd9iTqGHS0NFagZxSUzvnX97A="}]}, {"Route": "_framework/System.Runtime.Handles.wasm", "AssetFile": "_framework/System.Runtime.Handles.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000456621005"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2189"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uUwi6RKnhpZXAXql7zIMlN+gkpfQ70SjH8vlztDyOv0=\""}, {"Name": "ETag", "Value": "W/\"KOsdql/UbqwJPWsLjAPd9iTqGHS0NFagZxSUzvnX97A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KOsdql/UbqwJPWsLjAPd9iTqGHS0NFagZxSUzvnX97A="}]}, {"Route": "_framework/System.Runtime.Handles.wasm.gz", "AssetFile": "_framework/System.Runtime.Handles.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2189"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"uUwi6RKnhpZXAXql7zIMlN+gkpfQ70SjH8vlztDyOv0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uUwi6RKnhpZXAXql7zIMlN+gkpfQ70SjH8vlztDyOv0="}]}, {"Route": "_framework/System.Runtime.InteropServices.JavaScript.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.JavaScript.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "82709"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ayrzLdJZOJo95VHuySEfFBEgF5Qx8gIAPA1DJswCI60=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ayrzLdJZOJo95VHuySEfFBEgF5Qx8gIAPA1DJswCI60="}]}, {"Route": "_framework/System.Runtime.InteropServices.JavaScript.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029341862"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "34080"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"g0yE63zikGzxb1ygPZTG/MGdaAD8o1CziXGvwISMcv8=\""}, {"Name": "ETag", "Value": "W/\"ayrzLdJZOJo95VHuySEfFBEgF5Qx8gIAPA1DJswCI60=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ayrzLdJZOJo95VHuySEfFBEgF5Qx8gIAPA1DJswCI60="}]}, {"Route": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "AssetFile": "_framework/System.Runtime.InteropServices.JavaScript.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "34080"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"g0yE63zikGzxb1ygPZTG/MGdaAD8o1CziXGvwISMcv8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g0yE63zikGzxb1ygPZTG/MGdaAD8o1CziXGvwISMcv8="}]}, {"Route": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"QvfUj4ImNKVM1NswZPpINo5q7A1DLZ6pLqZPkGFxCeM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QvfUj4ImNKVM1NswZPpINo5q7A1DLZ6pLqZPkGFxCeM="}]}, {"Route": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000465983225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2145"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KVs7XaXibOm1KoVNyADAZUMvzxvPm8QwuJpa89iekQM=\""}, {"Name": "ETag", "Value": "W/\"QvfUj4ImNKVM1NswZPpINo5q7A1DLZ6pLqZPkGFxCeM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QvfUj4ImNKVM1NswZPpINo5q7A1DLZ6pLqZPkGFxCeM="}]}, {"Route": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "AssetFile": "_framework/System.Runtime.InteropServices.RuntimeInformation.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2145"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KVs7XaXibOm1KoVNyADAZUMvzxvPm8QwuJpa89iekQM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KVs7XaXibOm1KoVNyADAZUMvzxvPm8QwuJpa89iekQM="}]}, {"Route": "_framework/System.Runtime.InteropServices.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "47381"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"vjXpP2vcnrk6+5yEHR5tVxvQFKhYPVE2ROc66wMMKUk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vjXpP2vcnrk6+5yEHR5tVxvQFKhYPVE2ROc66wMMKUk="}]}, {"Route": "_framework/System.Runtime.InteropServices.wasm", "AssetFile": "_framework/System.Runtime.InteropServices.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000049113501"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20360"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rUh3gD9ryv4csIkl/KfJvkNUKoNUz1KMZyX7CF179NY=\""}, {"Name": "ETag", "Value": "W/\"vjXpP2vcnrk6+5yEHR5tVxvQFKhYPVE2ROc66wMMKUk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vjXpP2vcnrk6+5yEHR5tVxvQFKhYPVE2ROc66wMMKUk="}]}, {"Route": "_framework/System.Runtime.InteropServices.wasm.gz", "AssetFile": "_framework/System.Runtime.InteropServices.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "20360"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rUh3gD9ryv4csIkl/KfJvkNUKoNUz1KMZyX7CF179NY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rUh3gD9ryv4csIkl/KfJvkNUKoNUz1KMZyX7CF179NY="}]}, {"Route": "_framework/System.Runtime.Intrinsics.wasm", "AssetFile": "_framework/System.Runtime.Intrinsics.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GI7R2IjK56M9A0tCjnTqeIvepdxJr4xuPK7Jqmnjha0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GI7R2IjK56M9A0tCjnTqeIvepdxJr4xuPK7Jqmnjha0="}]}, {"Route": "_framework/System.Runtime.Intrinsics.wasm", "AssetFile": "_framework/System.Runtime.Intrinsics.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000379506641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2634"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fKtbxhJ0Rbm14kMa8UMkvNKZzwHQBDwqq7sEfHWPWE4=\""}, {"Name": "ETag", "Value": "W/\"GI7R2IjK56M9A0tCjnTqeIvepdxJr4xuPK7Jqmnjha0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GI7R2IjK56M9A0tCjnTqeIvepdxJr4xuPK7Jqmnjha0="}]}, {"Route": "_framework/System.Runtime.Intrinsics.wasm.gz", "AssetFile": "_framework/System.Runtime.Intrinsics.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2634"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"fKtbxhJ0Rbm14kMa8UMkvNKZzwHQBDwqq7sEfHWPWE4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fKtbxhJ0Rbm14kMa8UMkvNKZzwHQBDwqq7sEfHWPWE4="}]}, {"Route": "_framework/System.Runtime.Loader.wasm", "AssetFile": "_framework/System.Runtime.Loader.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Mr1CdVYgDhwo0GL+6T1IGXIa2l01YFfbWf28YwAwsP0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mr1CdVYgDhwo0GL+6T1IGXIa2l01YFfbWf28YwAwsP0="}]}, {"Route": "_framework/System.Runtime.Loader.wasm", "AssetFile": "_framework/System.Runtime.Loader.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000432525952"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2311"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DQPAbB0K0r+9AxnDSIBcgpSoS/H81NgEP+yOTLPouaE=\""}, {"Name": "ETag", "Value": "W/\"Mr1CdVYgDhwo0GL+6T1IGXIa2l01YFfbWf28YwAwsP0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mr1CdVYgDhwo0GL+6T1IGXIa2l01YFfbWf28YwAwsP0="}]}, {"Route": "_framework/System.Runtime.Loader.wasm.gz", "AssetFile": "_framework/System.Runtime.Loader.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2311"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DQPAbB0K0r+9AxnDSIBcgpSoS/H81NgEP+yOTLPouaE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DQPAbB0K0r+9AxnDSIBcgpSoS/H81NgEP+yOTLPouaE="}]}, {"Route": "_framework/System.Runtime.Numerics.wasm", "AssetFile": "_framework/System.Runtime.Numerics.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117525"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"w1RXtoUmCkc2fQcEaJW/KKqNAatH2x6Mjl++GJlnDqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w1RXtoUmCkc2fQcEaJW/KKqNAatH2x6Mjl++GJlnDqU="}]}, {"Route": "_framework/System.Runtime.Numerics.wasm", "AssetFile": "_framework/System.Runtime.Numerics.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022046827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "45357"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"czHFf3PgUJ8lROHFm8jtp6JP+KPhOC1ts/nITCbLfQc=\""}, {"Name": "ETag", "Value": "W/\"w1RXtoUmCkc2fQcEaJW/KKqNAatH2x6Mjl++GJlnDqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-w1RXtoUmCkc2fQcEaJW/KKqNAatH2x6Mjl++GJlnDqU="}]}, {"Route": "_framework/System.Runtime.Numerics.wasm.gz", "AssetFile": "_framework/System.Runtime.Numerics.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "45357"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"czHFf3PgUJ8lROHFm8jtp6JP+KPhOC1ts/nITCbLfQc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-czHFf3PgUJ8lROHFm8jtp6JP+KPhOC1ts/nITCbLfQc="}]}, {"Route": "_framework/System.Runtime.Serialization.Formatters.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Formatters.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55061"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hm1mpOknSyruLJ5znjAb4oQnbpT5bqodvFqYe8s2uCk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hm1mpOknSyruLJ5znjAb4oQnbpT5bqodvFqYe8s2uCk="}]}, {"Route": "_framework/System.Runtime.Serialization.Formatters.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041853262"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23892"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8hpt0ScUQ55kicZ+2Cezto14qHaEtdnNSpNfjgiRE/U=\""}, {"Name": "ETag", "Value": "W/\"hm1mpOknSyruLJ5znjAb4oQnbpT5bqodvFqYe8s2uCk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hm1mpOknSyruLJ5znjAb4oQnbpT5bqodvFqYe8s2uCk="}]}, {"Route": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "AssetFile": "_framework/System.Runtime.Serialization.Formatters.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23892"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8hpt0ScUQ55kicZ+2Cezto14qHaEtdnNSpNfjgiRE/U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8hpt0ScUQ55kicZ+2Cezto14qHaEtdnNSpNfjgiRE/U="}]}, {"Route": "_framework/System.Runtime.Serialization.Json.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Json.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"6NE4YI0PIhwhvoJ0/eBKdc/sNTLrk74NGpPvHaV032U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6NE4YI0PIhwhvoJ0/eBKdc/sNTLrk74NGpPvHaV032U="}]}, {"Route": "_framework/System.Runtime.Serialization.Json.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Json.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000444247001"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2250"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZAE8uMfA7ZPis3R0HOPYHvKbQgA0vGgU6urNXAlW7m4=\""}, {"Name": "ETag", "Value": "W/\"6NE4YI0PIhwhvoJ0/eBKdc/sNTLrk74NGpPvHaV032U=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6NE4YI0PIhwhvoJ0/eBKdc/sNTLrk74NGpPvHaV032U="}]}, {"Route": "_framework/System.Runtime.Serialization.Json.wasm.gz", "AssetFile": "_framework/System.Runtime.Serialization.Json.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2250"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZAE8uMfA7ZPis3R0HOPYHvKbQgA0vGgU6urNXAlW7m4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZAE8uMfA7ZPis3R0HOPYHvKbQgA0vGgU6urNXAlW7m4="}]}, {"Route": "_framework/System.Runtime.Serialization.Primitives.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12565"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cbsksW0ha0V/4ntN+j/BjZcF1Smlmytb83UGQP2LlxE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cbsksW0ha0V/4ntN+j/BjZcF1Smlmytb83UGQP2LlxE="}]}, {"Route": "_framework/System.Runtime.Serialization.Primitives.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000184774575"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5411"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8iu/dV11RAYP4segQpv4V3+8LgUEi486WY/7UgoaQf0=\""}, {"Name": "ETag", "Value": "W/\"cbsksW0ha0V/4ntN+j/BjZcF1Smlmytb83UGQP2LlxE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cbsksW0ha0V/4ntN+j/BjZcF1Smlmytb83UGQP2LlxE="}]}, {"Route": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "AssetFile": "_framework/System.Runtime.Serialization.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5411"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"8iu/dV11RAYP4segQpv4V3+8LgUEi486WY/7UgoaQf0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8iu/dV11RAYP4segQpv4V3+8LgUEi486WY/7UgoaQf0="}]}, {"Route": "_framework/System.Runtime.Serialization.Xml.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Xml.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"4a2IDyy+r2/VVJGjcYiCC0RoBmsKXlbjWj/xJXnY5vg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4a2IDyy+r2/VVJGjcYiCC0RoBmsKXlbjWj/xJXnY5vg="}]}, {"Route": "_framework/System.Runtime.Serialization.Xml.wasm", "AssetFile": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000391542678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2553"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"otOgy1dVlxl7VfSohd3OFdqEoCPNPlElxW2FKW26W9o=\""}, {"Name": "ETag", "Value": "W/\"4a2IDyy+r2/VVJGjcYiCC0RoBmsKXlbjWj/xJXnY5vg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4a2IDyy+r2/VVJGjcYiCC0RoBmsKXlbjWj/xJXnY5vg="}]}, {"Route": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "AssetFile": "_framework/System.Runtime.Serialization.Xml.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2553"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"otOgy1dVlxl7VfSohd3OFdqEoCPNPlElxW2FKW26W9o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-otOgy1dVlxl7VfSohd3OFdqEoCPNPlElxW2FKW26W9o="}]}, {"Route": "_framework/System.Runtime.Serialization.wasm", "AssetFile": "_framework/System.Runtime.Serialization.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"6+Wuq<PERSON>hjaqD8lGllJN3JkAnjZdicQkKe/Fnp9QZCzg8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6+<PERSON><PERSON><PERSON><PERSON><PERSON>qD8lGllJN3JkAnjZdicQkKe/Fnp9QZCzg8="}]}, {"Route": "_framework/System.Runtime.Serialization.wasm", "AssetFile": "_framework/System.Runtime.Serialization.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000401284109"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2491"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ey7b6ivDC8X8cHEBjvSFs1JllCyxZanO5yjXM+adVMY=\""}, {"Name": "ETag", "Value": "W/\"6+Wuq<PERSON>hjaqD8lGllJN3JkAnjZdicQkKe/Fnp9QZCzg8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6+<PERSON><PERSON><PERSON><PERSON><PERSON>qD8lGllJN3JkAnjZdicQkKe/Fnp9QZCzg8="}]}, {"Route": "_framework/System.Runtime.Serialization.wasm.gz", "AssetFile": "_framework/System.Runtime.Serialization.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2491"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ey7b6ivDC8X8cHEBjvSFs1JllCyxZanO5yjXM+adVMY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ey7b6ivDC8X8cHEBjvSFs1JllCyxZanO5yjXM+adVMY="}]}, {"Route": "_framework/System.Runtime.wasm", "AssetFile": "_framework/System.Runtime.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33045"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Mb2ZrDytrkvrnH4GTwyATwniWvulN0XXM/C3II7us0s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mb2ZrDytrkvrnH4GTwyATwniWvulN0XXM/C3II7us0s="}]}, {"Route": "_framework/System.Runtime.wasm", "AssetFile": "_framework/System.Runtime.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000095757924"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10442"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gdOdkhZK1U26cYc5ufKaOP4zFqkq47jvhVbIwWM2Axo=\""}, {"Name": "ETag", "Value": "W/\"Mb2ZrDytrkvrnH4GTwyATwniWvulN0XXM/C3II7us0s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mb2ZrDytrkvrnH4GTwyATwniWvulN0XXM/C3II7us0s="}]}, {"Route": "_framework/System.Runtime.wasm.gz", "AssetFile": "_framework/System.Runtime.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10442"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gdOdkhZK1U26cYc5ufKaOP4zFqkq47jvhVbIwWM2Axo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gdOdkhZK1U26cYc5ufKaOP4zFqkq47jvhVbIwWM2Axo="}]}, {"Route": "_framework/System.Security.AccessControl.wasm", "AssetFile": "_framework/System.Security.AccessControl.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "45845"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"C//a5VwbqwV3RZJiJc3RXAqPBvo4Wiy/Aq96dwF15zg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C//a5VwbqwV3RZJiJc3RXAqPBvo4Wiy/Aq96dwF15zg="}]}, {"Route": "_framework/System.Security.AccessControl.wasm", "AssetFile": "_framework/System.Security.AccessControl.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000061527103"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16252"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"xV+rifgRaEy+ti+5Nmen21FOFtxFddltjAZud3ieWwE=\""}, {"Name": "ETag", "Value": "W/\"C//a5VwbqwV3RZJiJc3RXAqPBvo4Wiy/Aq96dwF15zg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C//a5VwbqwV3RZJiJc3RXAqPBvo4Wiy/Aq96dwF15zg="}]}, {"Route": "_framework/System.Security.AccessControl.wasm.gz", "AssetFile": "_framework/System.Security.AccessControl.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16252"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"xV+rifgRaEy+ti+5Nmen21FOFtxFddltjAZud3ieWwE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xV+rifgRaEy+ti+5Nmen21FOFtxFddltjAZud3ieWwE="}]}, {"Route": "_framework/System.Security.Claims.wasm", "AssetFile": "_framework/System.Security.Claims.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42261"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EathWqxVRyaV9D+q10lclIQWUCy+Uifwis38KxpXq+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EathWqxVRyaV9D+q10lclIQWUCy+Uifwis38KxpXq+Y="}]}, {"Route": "_framework/System.Security.Claims.wasm", "AssetFile": "_framework/System.Security.Claims.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000063669935"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15705"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"QtRCX/YFgC8S+4+yBiQVNLlCXyY6Xx59IAn8kPs6GpY=\""}, {"Name": "ETag", "Value": "W/\"EathWqxVRyaV9D+q10lclIQWUCy+Uifwis38KxpXq+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EathWqxVRyaV9D+q10lclIQWUCy+Uifwis38KxpXq+Y="}]}, {"Route": "_framework/System.Security.Claims.wasm.gz", "AssetFile": "_framework/System.Security.Claims.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15705"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"QtRCX/YFgC8S+4+yBiQVNLlCXyY6Xx59IAn8kPs6GpY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QtRCX/YFgC8S+4+yBiQVNLlCXyY6Xx59IAn8kPs6GpY="}]}, {"Route": "_framework/System.Security.Cryptography.Algorithms.wasm", "AssetFile": "_framework/System.Security.Cryptography.Algorithms.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6933"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"IvOdWtw7jBIH/yOJcCWL/yo8EWzv1WCCY126pf7kw3g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IvOdWtw7jBIH/yOJcCWL/yo8EWzv1WCCY126pf7kw3g="}]}, {"Route": "_framework/System.Security.Cryptography.Algorithms.wasm", "AssetFile": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000371333086"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2692"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"neO1GvmWYerHIuXQVpOAYnA/Zg4MLFlDFmRXFH9qUhk=\""}, {"Name": "ETag", "Value": "W/\"IvOdWtw7jBIH/yOJcCWL/yo8EWzv1WCCY126pf7kw3g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IvOdWtw7jBIH/yOJcCWL/yo8EWzv1WCCY126pf7kw3g="}]}, {"Route": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.Algorithms.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2692"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"neO1GvmWYerHIuXQVpOAYnA/Zg4MLFlDFmRXFH9qUhk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-neO1GvmWYerHIuXQVpOAYnA/Zg4MLFlDFmRXFH9qUhk="}]}, {"Route": "_framework/System.Security.Cryptography.Cng.wasm", "AssetFile": "_framework/System.Security.Cryptography.Cng.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XGJ4R6Yb4xAkrrJzgGCkQfcrefKuAsascsbgGroJUQA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XGJ4R6Yb4xAkrrJzgGCkQfcrefKuAsascsbgGroJUQA="}]}, {"Route": "_framework/System.Security.Cryptography.Cng.wasm", "AssetFile": "_framework/System.Security.Cryptography.Cng.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405844156"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2463"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9F25vShGuzEH8tRa62OfAVIyjSpcQHpKD8oB7EQCpsM=\""}, {"Name": "ETag", "Value": "W/\"XGJ4R6Yb4xAkrrJzgGCkQfcrefKuAsascsbgGroJUQA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XGJ4R6Yb4xAkrrJzgGCkQfcrefKuAsascsbgGroJUQA="}]}, {"Route": "_framework/System.Security.Cryptography.Cng.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.Cng.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2463"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9F25vShGuzEH8tRa62OfAVIyjSpcQHpKD8oB7EQCpsM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9F25vShGuzEH8tRa62OfAVIyjSpcQHpKD8oB7EQCpsM="}]}, {"Route": "_framework/System.Security.Cryptography.Csp.wasm", "AssetFile": "_framework/System.Security.Cryptography.Csp.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"lgfZdXORUhWkzCJUQdjoY1FpAwA39qbRYlGoBrp3qvM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lgfZdXORUhWkzCJUQdjoY1FpAwA39qbRYlGoBrp3qvM="}]}, {"Route": "_framework/System.Security.Cryptography.Csp.wasm", "AssetFile": "_framework/System.Security.Cryptography.Csp.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000430292599"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2323"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"87y8M539rYk6NLRwAiumX4XpIs94yQIiAgGibuRcNso=\""}, {"Name": "ETag", "Value": "W/\"lgfZdXORUhWkzCJUQdjoY1FpAwA39qbRYlGoBrp3qvM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lgfZdXORUhWkzCJUQdjoY1FpAwA39qbRYlGoBrp3qvM="}]}, {"Route": "_framework/System.Security.Cryptography.Csp.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.Csp.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2323"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"87y8M539rYk6NLRwAiumX4XpIs94yQIiAgGibuRcNso=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-87y8M539rYk6NLRwAiumX4XpIs94yQIiAgGibuRcNso="}]}, {"Route": "_framework/System.Security.Cryptography.Encoding.wasm", "AssetFile": "_framework/System.Security.Cryptography.Encoding.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"GfZajc0PDIaw5SMWhIQs+6g/da6XFzgIE8zopS+pQIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GfZajc0PDIaw5SMWhIQs+6g/da6XFzgIE8zopS+pQIM="}]}, {"Route": "_framework/System.Security.Cryptography.Encoding.wasm", "AssetFile": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000442282176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2260"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"RzoZ5L0tNSJidnBbkigVPLlG1Vx3oTsL4OCyJTTZYMo=\""}, {"Name": "ETag", "Value": "W/\"GfZajc0PDIaw5SMWhIQs+6g/da6XFzgIE8zopS+pQIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GfZajc0PDIaw5SMWhIQs+6g/da6XFzgIE8zopS+pQIM="}]}, {"Route": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.Encoding.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2260"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"RzoZ5L0tNSJidnBbkigVPLlG1Vx3oTsL4OCyJTTZYMo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RzoZ5L0tNSJidnBbkigVPLlG1Vx3oTsL4OCyJTTZYMo="}]}, {"Route": "_framework/System.Security.Cryptography.OpenSsl.wasm", "AssetFile": "_framework/System.Security.Cryptography.OpenSsl.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"0PRJQU20v0Lpcdptiera5Kmabxtn59wWKDlwL4i23F4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0PRJQU20v0Lpcdptiera5Kmabxtn59wWKDlwL4i23F4="}]}, {"Route": "_framework/System.Security.Cryptography.OpenSsl.wasm", "AssetFile": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000456204380"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2191"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+3Nt4BgEsOPQFUdBtI+44j21jxlC4W0gZwQJwNqXl1s=\""}, {"Name": "ETag", "Value": "W/\"0PRJQU20v0Lpcdptiera5Kmabxtn59wWKDlwL4i23F4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0PRJQU20v0Lpcdptiera5Kmabxtn59wWKDlwL4i23F4="}]}, {"Route": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.OpenSsl.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2191"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+3Nt4BgEsOPQFUdBtI+44j21jxlC4W0gZwQJwNqXl1s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+3Nt4BgEsOPQFUdBtI+44j21jxlC4W0gZwQJwNqXl1s="}]}, {"Route": "_framework/System.Security.Cryptography.Primitives.wasm", "AssetFile": "_framework/System.Security.Cryptography.Primitives.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"67hS5T8ThmqF1ybNAnEqtCPAHgCb3pv4zR4DUNJmZQg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-67hS5T8ThmqF1ybNAnEqtCPAHgCb3pv4zR4DUNJmZQg="}]}, {"Route": "_framework/System.Security.Cryptography.Primitives.wasm", "AssetFile": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000430848772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2320"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jK2QZDe3pYVXVvuGiC0xs0qGUIneoqZYEWzSm4WliZo=\""}, {"Name": "ETag", "Value": "W/\"67hS5T8ThmqF1ybNAnEqtCPAHgCb3pv4zR4DUNJmZQg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-67hS5T8ThmqF1ybNAnEqtCPAHgCb3pv4zR4DUNJmZQg="}]}, {"Route": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.Primitives.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2320"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jK2QZDe3pYVXVvuGiC0xs0qGUIneoqZYEWzSm4WliZo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jK2QZDe3pYVXVvuGiC0xs0qGUIneoqZYEWzSm4WliZo="}]}, {"Route": "_framework/System.Security.Cryptography.X509Certificates.wasm", "AssetFile": "_framework/System.Security.Cryptography.X509Certificates.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"HDzsHXDWkBktf++7M4QeAPNyY0lcHUnbiixmm73I1Q4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HDzsHXDWkBktf++7M4QeAPNyY0lcHUnbiixmm73I1Q4="}]}, {"Route": "_framework/System.Security.Cryptography.X509Certificates.wasm", "AssetFile": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000376222724"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2657"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5rHA4YyrGAXG04mRVJUFJ5j9OuKlVNSTzxq3/zxnC0A=\""}, {"Name": "ETag", "Value": "W/\"HDzsHXDWkBktf++7M4QeAPNyY0lcHUnbiixmm73I1Q4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HDzsHXDWkBktf++7M4QeAPNyY0lcHUnbiixmm73I1Q4="}]}, {"Route": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.X509Certificates.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2657"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5rHA4YyrGAXG04mRVJUFJ5j9OuKlVNSTzxq3/zxnC0A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5rHA4YyrGAXG04mRVJUFJ5j9OuKlVNSTzxq3/zxnC0A="}]}, {"Route": "_framework/System.Security.Cryptography.wasm", "AssetFile": "_framework/System.Security.Cryptography.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "469269"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"n9MRPEgVS+VtieuqMkvaPbpqg5gogW8/QUUxYQx3UFA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9MRPEgVS+VtieuqMkvaPbpqg5gogW8/QUUxYQx3UFA="}]}, {"Route": "_framework/System.Security.Cryptography.wasm", "AssetFile": "_framework/System.Security.Cryptography.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000005638981"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "177336"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"br04uczfoLOGoPhcHD5NPvuOMh1Ta3GxC3CPddsxDx8=\""}, {"Name": "ETag", "Value": "W/\"n9MRPEgVS+VtieuqMkvaPbpqg5gogW8/QUUxYQx3UFA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9MRPEgVS+VtieuqMkvaPbpqg5gogW8/QUUxYQx3UFA="}]}, {"Route": "_framework/System.Security.Cryptography.wasm.gz", "AssetFile": "_framework/System.Security.Cryptography.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "177336"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"br04uczfoLOGoPhcHD5NPvuOMh1Ta3GxC3CPddsxDx8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-br04uczfoLOGoPhcHD5NPvuOMh1Ta3GxC3CPddsxDx8="}]}, {"Route": "_framework/System.Security.Principal.Windows.wasm", "AssetFile": "_framework/System.Security.Principal.Windows.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "25877"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KCA3EJm225EPgsXCSmzpb9QlNnP15aoAOmZc1kuCtUI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KCA3EJm225EPgsXCSmzpb9QlNnP15aoAOmZc1kuCtUI="}]}, {"Route": "_framework/System.Security.Principal.Windows.wasm", "AssetFile": "_framework/System.Security.Principal.Windows.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000094930701"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10533"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2JG4I+sLuciIKlN4Wa6ENOTiZOPCbosveKn9m7km07c=\""}, {"Name": "ETag", "Value": "W/\"KCA3EJm225EPgsXCSmzpb9QlNnP15aoAOmZc1kuCtUI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KCA3EJm225EPgsXCSmzpb9QlNnP15aoAOmZc1kuCtUI="}]}, {"Route": "_framework/System.Security.Principal.Windows.wasm.gz", "AssetFile": "_framework/System.Security.Principal.Windows.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "10533"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"2JG4I+sLuciIKlN4Wa6ENOTiZOPCbosveKn9m7km07c=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2JG4I+sLuciIKlN4Wa6ENOTiZOPCbosveKn9m7km07c="}]}, {"Route": "_framework/System.Security.Principal.wasm", "AssetFile": "_framework/System.Security.Principal.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"HhxC96OlufTbtO/SQo7O639WBHeffDHV5MtaqpF6dEI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HhxC96OlufTbtO/SQo7O639WBHeffDHV5MtaqpF6dEI="}]}, {"Route": "_framework/System.Security.Principal.wasm", "AssetFile": "_framework/System.Security.Principal.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000465116279"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EX0hvKe0rHiVkgZs5xByEf+cC+kWITvSQSdU3Uz895Q=\""}, {"Name": "ETag", "Value": "W/\"HhxC96OlufTbtO/SQo7O639WBHeffDHV5MtaqpF6dEI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HhxC96OlufTbtO/SQo7O639WBHeffDHV5MtaqpF6dEI="}]}, {"Route": "_framework/System.Security.Principal.wasm.gz", "AssetFile": "_framework/System.Security.Principal.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2149"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"EX0hvKe0rHiVkgZs5xByEf+cC+kWITvSQSdU3Uz895Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EX0hvKe0rHiVkgZs5xByEf+cC+kWITvSQSdU3Uz895Q="}]}, {"Route": "_framework/System.Security.SecureString.wasm", "AssetFile": "_framework/System.Security.SecureString.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jk1ZGWoFtmTt0UnzjMF9VLaGLtPRzcPKxpucValfiDI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jk1ZGWoFtmTt0UnzjMF9VLaGLtPRzcPKxpucValfiDI="}]}, {"Route": "_framework/System.Security.SecureString.wasm", "AssetFile": "_framework/System.Security.SecureString.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000457247371"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2186"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"zZsvvV4gt6b5QVvkaehqHzohld2O5OexYay7mOEtzVg=\""}, {"Name": "ETag", "Value": "W/\"jk1ZGWoFtmTt0UnzjMF9VLaGLtPRzcPKxpucValfiDI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jk1ZGWoFtmTt0UnzjMF9VLaGLtPRzcPKxpucValfiDI="}]}, {"Route": "_framework/System.Security.SecureString.wasm.gz", "AssetFile": "_framework/System.Security.SecureString.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2186"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"zZsvvV4gt6b5QVvkaehqHzohld2O5OexYay7mOEtzVg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zZsvvV4gt6b5QVvkaehqHzohld2O5OexYay7mOEtzVg="}]}, {"Route": "_framework/System.Security.wasm", "AssetFile": "_framework/System.Security.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7957"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"aBqb92nn53q+ODIAHFmBeFd5SDNMQ99a1CePfdeoflY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBqb92nn53q+ODIAHFmBeFd5SDNMQ99a1CePfdeoflY="}]}, {"Route": "_framework/System.Security.wasm", "AssetFile": "_framework/System.Security.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000338294993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2955"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"h74f5tp3igMnXrKt6aXHYBLxXiIks9Ozqf4I+dcveak=\""}, {"Name": "ETag", "Value": "W/\"aBqb92nn53q+ODIAHFmBeFd5SDNMQ99a1CePfdeoflY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aBqb92nn53q+ODIAHFmBeFd5SDNMQ99a1CePfdeoflY="}]}, {"Route": "_framework/System.Security.wasm.gz", "AssetFile": "_framework/System.Security.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2955"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"h74f5tp3igMnXrKt6aXHYBLxXiIks9Ozqf4I+dcveak=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h74f5tp3igMnXrKt6aXHYBLxXiIks9Ozqf4I+dcveak="}]}, {"Route": "_framework/System.ServiceModel.Web.wasm", "AssetFile": "_framework/System.ServiceModel.Web.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"KZTgtkPbZ0EFEF8SCJzkGYtsJMuXpawAUycJnaiTdkA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KZTgtkPbZ0EFEF8SCJzkGYtsJMuXpawAUycJnaiTdkA="}]}, {"Route": "_framework/System.ServiceModel.Web.wasm", "AssetFile": "_framework/System.ServiceModel.Web.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000395100751"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2530"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OTxEhhyrRJxeShdzS4O/ixvngdasrT98WA8Ac/mi3+s=\""}, {"Name": "ETag", "Value": "W/\"KZTgtkPbZ0EFEF8SCJzkGYtsJMuXpawAUycJnaiTdkA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KZTgtkPbZ0EFEF8SCJzkGYtsJMuXpawAUycJnaiTdkA="}]}, {"Route": "_framework/System.ServiceModel.Web.wasm.gz", "AssetFile": "_framework/System.ServiceModel.Web.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2530"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OTxEhhyrRJxeShdzS4O/ixvngdasrT98WA8Ac/mi3+s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OTxEhhyrRJxeShdzS4O/ixvngdasrT98WA8Ac/mi3+s="}]}, {"Route": "_framework/System.ServiceProcess.wasm", "AssetFile": "_framework/System.ServiceProcess.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UR9dhORhiGT/VglWNJRopv6Ls4/+DnrVrWyB7yIOglE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UR9dhORhiGT/VglWNJRopv6Ls4/+DnrVrWyB7yIOglE="}]}, {"Route": "_framework/System.ServiceProcess.wasm", "AssetFile": "_framework/System.ServiceProcess.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000435919791"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2293"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hNopUwIvVbuH7523C5Sz9KcbnXYjhKQ9N5Iz0MVgfzY=\""}, {"Name": "ETag", "Value": "W/\"UR9dhORhiGT/VglWNJRopv6Ls4/+DnrVrWyB7yIOglE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UR9dhORhiGT/VglWNJRopv6Ls4/+DnrVrWyB7yIOglE="}]}, {"Route": "_framework/System.ServiceProcess.wasm.gz", "AssetFile": "_framework/System.ServiceProcess.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2293"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hNopUwIvVbuH7523C5Sz9KcbnXYjhKQ9N5Iz0MVgfzY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hNopUwIvVbuH7523C5Sz9KcbnXYjhKQ9N5Iz0MVgfzY="}]}, {"Route": "_framework/System.Text.Encoding.CodePages.wasm", "AssetFile": "_framework/System.Text.Encoding.CodePages.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "729877"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nDA1OGlGpx/RPrgjkp7Ik0G2WMJeIR4PuX2QehG9L+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nDA1OGlGpx/RPrgjkp7Ik0G2WMJeIR4PuX2QehG9L+Y="}]}, {"Route": "_framework/System.Text.Encoding.CodePages.wasm", "AssetFile": "_framework/System.Text.Encoding.CodePages.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000001943298"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "514588"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WwxLN6ykn0TuL/uyFlqkd+Y7y9APHZMZkrcjgY2cGYE=\""}, {"Name": "ETag", "Value": "W/\"nDA1OGlGpx/RPrgjkp7Ik0G2WMJeIR4PuX2QehG9L+Y=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nDA1OGlGpx/RPrgjkp7Ik0G2WMJeIR4PuX2QehG9L+Y="}]}, {"Route": "_framework/System.Text.Encoding.CodePages.wasm.gz", "AssetFile": "_framework/System.Text.Encoding.CodePages.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "514588"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WwxLN6ykn0TuL/uyFlqkd+Y7y9APHZMZkrcjgY2cGYE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WwxLN6ykn0TuL/uyFlqkd+Y7y9APHZMZkrcjgY2cGYE="}]}, {"Route": "_framework/System.Text.Encoding.Extensions.wasm", "AssetFile": "_framework/System.Text.Encoding.Extensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+A8smigNE6TN5uvvy090VZHhECNJRJVvYhamACCF0MY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+A8smigNE6TN5uvvy090VZHhECNJRJVvYhamACCF0MY="}]}, {"Route": "_framework/System.Text.Encoding.Extensions.wasm", "AssetFile": "_framework/System.Text.Encoding.Extensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000444642063"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2248"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Q7HTAxik7pLYSERIoC+5T6dkeKsNf5nZnZ3B+v631I4=\""}, {"Name": "ETag", "Value": "W/\"+A8smigNE6TN5uvvy090VZHhECNJRJVvYhamACCF0MY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+A8smigNE6TN5uvvy090VZHhECNJRJVvYhamACCF0MY="}]}, {"Route": "_framework/System.Text.Encoding.Extensions.wasm.gz", "AssetFile": "_framework/System.Text.Encoding.Extensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2248"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"Q7HTAxik7pLYSERIoC+5T6dkeKsNf5nZnZ3B+v631I4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q7HTAxik7pLYSERIoC+5T6dkeKsNf5nZnZ3B+v631I4="}]}, {"Route": "_framework/System.Text.Encoding.wasm", "AssetFile": "_framework/System.Text.Encoding.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"5AJdm12gyrAA/MCow6VUNeS1DwG27/sBDTFQdyghaGk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5AJdm12gyrAA/MCow6VUNeS1DwG27/sBDTFQdyghaGk="}]}, {"Route": "_framework/System.Text.Encoding.wasm", "AssetFile": "_framework/System.Text.Encoding.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000448833034"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2227"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nisRDlkzeoxiu2bwDueNQebhDWpxT+7G/xrL/ZkYMzs=\""}, {"Name": "ETag", "Value": "W/\"5AJdm12gyrAA/MCow6VUNeS1DwG27/sBDTFQdyghaGk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5AJdm12gyrAA/MCow6VUNeS1DwG27/sBDTFQdyghaGk="}]}, {"Route": "_framework/System.Text.Encoding.wasm.gz", "AssetFile": "_framework/System.Text.Encoding.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2227"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nisRDlkzeoxiu2bwDueNQebhDWpxT+7G/xrL/ZkYMzs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nisRDlkzeoxiu2bwDueNQebhDWpxT+7G/xrL/ZkYMzs="}]}, {"Route": "_framework/System.Text.Encodings.Web.wasm", "AssetFile": "_framework/System.Text.Encodings.Web.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60181"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"nNJ+fUG4RweLKeCz1Cu/dgzN38OQdO7wfZDDjmwNH70=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nNJ+fUG4RweLKeCz1Cu/dgzN38OQdO7wfZDDjmwNH70="}]}, {"Route": "_framework/System.Text.Encodings.Web.wasm", "AssetFile": "_framework/System.Text.Encodings.Web.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000042580370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23484"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"G5eAQVv89yCQMgZqPxDd0ALIhTU2v5vzMFv1ELlyRDA=\""}, {"Name": "ETag", "Value": "W/\"nNJ+fUG4RweLKeCz1Cu/dgzN38OQdO7wfZDDjmwNH70=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nNJ+fUG4RweLKeCz1Cu/dgzN38OQdO7wfZDDjmwNH70="}]}, {"Route": "_framework/System.Text.Encodings.Web.wasm.gz", "AssetFile": "_framework/System.Text.Encodings.Web.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23484"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"G5eAQVv89yCQMgZqPxDd0ALIhTU2v5vzMFv1ELlyRDA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G5eAQVv89yCQMgZqPxDd0ALIhTU2v5vzMFv1ELlyRDA="}]}, {"Route": "_framework/System.Text.Json.wasm", "AssetFile": "_framework/System.Text.Json.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "569621"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+IyGyxZv5W7dvLDoZIvFxPS/SRA0hXPUxFsmqJqqKrE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+IyGyxZv5W7dvLDoZIvFxPS/SRA0hXPUxFsmqJqqKrE="}]}, {"Route": "_framework/System.Text.Json.wasm", "AssetFile": "_framework/System.Text.Json.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000004977601"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "200899"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"0Bdpw931ifndR8qDUlY7IBpI0mJXMUP4mRWZ4Qb7Kvc=\""}, {"Name": "ETag", "Value": "W/\"+IyGyxZv5W7dvLDoZIvFxPS/SRA0hXPUxFsmqJqqKrE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+IyGyxZv5W7dvLDoZIvFxPS/SRA0hXPUxFsmqJqqKrE="}]}, {"Route": "_framework/System.Text.Json.wasm.gz", "AssetFile": "_framework/System.Text.Json.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "200899"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"0Bdpw931ifndR8qDUlY7IBpI0mJXMUP4mRWZ4Qb7Kvc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0Bdpw931ifndR8qDUlY7IBpI0mJXMUP4mRWZ4Qb7Kvc="}]}, {"Route": "_framework/System.Text.RegularExpressions.wasm", "AssetFile": "_framework/System.Text.RegularExpressions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "348949"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YNOeBgXLSOUl9vXtTmNkvDhyAxlta5Eaw3bxubrWLtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YNOeBgXLSOUl9vXtTmNkvDhyAxlta5Eaw3bxubrWLtY="}]}, {"Route": "_framework/System.Text.RegularExpressions.wasm", "AssetFile": "_framework/System.Text.RegularExpressions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000006688292"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "149514"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZWMGHh2UsJ+eGdsy6qOSF4Xy7VZrYd9akmzZ73g6o9I=\""}, {"Name": "ETag", "Value": "W/\"YNOeBgXLSOUl9vXtTmNkvDhyAxlta5Eaw3bxubrWLtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YNOeBgXLSOUl9vXtTmNkvDhyAxlta5Eaw3bxubrWLtY="}]}, {"Route": "_framework/System.Text.RegularExpressions.wasm.gz", "AssetFile": "_framework/System.Text.RegularExpressions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "149514"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ZWMGHh2UsJ+eGdsy6qOSF4Xy7VZrYd9akmzZ73g6o9I=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZWMGHh2UsJ+eGdsy6qOSF4Xy7VZrYd9akmzZ73g6o9I="}]}, {"Route": "_framework/System.Threading.Channels.wasm", "AssetFile": "_framework/System.Threading.Channels.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42261"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"XiQX0gdosjb6PD5jLWuAnCJysYb0/pKodRXmb0WA6Ww=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XiQX0gdosjb6PD5jLWuAnCJysYb0/pKodRXmb0WA6Ww="}]}, {"Route": "_framework/System.Threading.Channels.wasm", "AssetFile": "_framework/System.Threading.Channels.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000051985860"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19235"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"wLEbbou+b7gIAJnINmCN+SqNWt++RPZLY/sLqN4/LTk=\""}, {"Name": "ETag", "Value": "W/\"XiQX0gdosjb6PD5jLWuAnCJysYb0/pKodRXmb0WA6Ww=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XiQX0gdosjb6PD5jLWuAnCJysYb0/pKodRXmb0WA6Ww="}]}, {"Route": "_framework/System.Threading.Channels.wasm.gz", "AssetFile": "_framework/System.Threading.Channels.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19235"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"wLEbbou+b7gIAJnINmCN+SqNWt++RPZLY/sLqN4/LTk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wLE<PERSON><PERSON>+b7gIAJnINmCN+SqNWt++RPZLY/sLqN4/LTk="}]}, {"Route": "_framework/System.Threading.Overlapped.wasm", "AssetFile": "_framework/System.Threading.Overlapped.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"asQ5wmhvUfgsVsKfe2xTQ4n2t5VdFWeveFZnuRsV2/o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-asQ5wmhvUfgsVsKfe2xTQ4n2t5VdFWeveFZnuRsV2/o="}]}, {"Route": "_framework/System.Threading.Overlapped.wasm", "AssetFile": "_framework/System.Threading.Overlapped.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000434593655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2300"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ltXPx1A3Kb+2taWt5eXbWB3GrqQuclT+2CxUI43R4Pc=\""}, {"Name": "ETag", "Value": "W/\"asQ5wmhvUfgsVsKfe2xTQ4n2t5VdFWeveFZnuRsV2/o=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-asQ5wmhvUfgsVsKfe2xTQ4n2t5VdFWeveFZnuRsV2/o="}]}, {"Route": "_framework/System.Threading.Overlapped.wasm.gz", "AssetFile": "_framework/System.Threading.Overlapped.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2300"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ltXPx1A3Kb+2taWt5eXbWB3GrqQuclT+2CxUI43R4Pc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ltXPx1A3Kb+2taWt5eXbWB3GrqQuclT+2CxUI43R4Pc="}]}, {"Route": "_framework/System.Threading.Tasks.Dataflow.wasm", "AssetFile": "_framework/System.Threading.Tasks.Dataflow.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "175893"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mZufHaXQZ6yXlHobPGdD3augrBPoO9Jman+fM0ilEX8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mZufHaXQZ6yXlHobPGdD3augrBPoO9Jman+fM0ilEX8="}]}, {"Route": "_framework/System.Threading.Tasks.Dataflow.wasm", "AssetFile": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013554176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "73777"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PctQETfNvQcjK/7gObiO9kel1+uCrVFk6bcraI+Eqsg=\""}, {"Name": "ETag", "Value": "W/\"mZufHaXQZ6yXlHobPGdD3augrBPoO9Jman+fM0ilEX8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mZufHaXQZ6yXlHobPGdD3augrBPoO9Jman+fM0ilEX8="}]}, {"Route": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "AssetFile": "_framework/System.Threading.Tasks.Dataflow.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "73777"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PctQETfNvQcjK/7gObiO9kel1+uCrVFk6bcraI+Eqsg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PctQETfNvQcjK/7gObiO9kel1+uCrVFk6bcraI+Eqsg="}]}, {"Route": "_framework/System.Threading.Tasks.Extensions.wasm", "AssetFile": "_framework/System.Threading.Tasks.Extensions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YGsdSlL2bH8sSjW1qdT81aHMBx+pd2p7uk3fXbFPlko=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YGsdSlL2bH8sSjW1qdT81aHMBx+pd2p7uk3fXbFPlko="}]}, {"Route": "_framework/System.Threading.Tasks.Extensions.wasm", "AssetFile": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000437636761"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2284"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SrGr7BoHtUP0+VI3UmJNfDBhuG6p6K8G+b9DZr2cwPM=\""}, {"Name": "ETag", "Value": "W/\"YGsdSlL2bH8sSjW1qdT81aHMBx+pd2p7uk3fXbFPlko=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YGsdSlL2bH8sSjW1qdT81aHMBx+pd2p7uk3fXbFPlko="}]}, {"Route": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "AssetFile": "_framework/System.Threading.Tasks.Extensions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2284"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"SrGr7BoHtUP0+VI3UmJNfDBhuG6p6K8G+b9DZr2cwPM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SrGr7BoHtUP0+VI3UmJNfDBhuG6p6K8G+b9DZr2cwPM="}]}, {"Route": "_framework/System.Threading.Tasks.Parallel.wasm", "AssetFile": "_framework/System.Threading.Tasks.Parallel.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51989"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"n7yh3hS6dU0jtvE6jm9J7g+te6U2fthLDT+70A3tDOo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n7yh3hS6dU0jtvE6jm9J7g+te6U2fthLDT+70A3tDOo="}]}, {"Route": "_framework/System.Threading.Tasks.Parallel.wasm", "AssetFile": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000046356388"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21571"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dtM4sWaUsHCL55mlOZqmDGkY7gXiny40rUaqHjPzoxk=\""}, {"Name": "ETag", "Value": "W/\"n7yh3hS6dU0jtvE6jm9J7g+te6U2fthLDT+70A3tDOo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n7yh3hS6dU0jtvE6jm9J7g+te6U2fthLDT+70A3tDOo="}]}, {"Route": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "AssetFile": "_framework/System.Threading.Tasks.Parallel.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21571"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dtM4sWaUsHCL55mlOZqmDGkY7gXiny40rUaqHjPzoxk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dtM4sWaUsHCL55mlOZqmDGkY7gXiny40rUaqHjPzoxk="}]}, {"Route": "_framework/System.Threading.Tasks.wasm", "AssetFile": "_framework/System.Threading.Tasks.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3H7FuYrx8mWgl0lVlLoSlBBjD1D5v83i3Y2xkMdyq4g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3H7FuYrx8mWgl0lVlLoSlBBjD1D5v83i3Y2xkMdyq4g="}]}, {"Route": "_framework/System.Threading.Tasks.wasm", "AssetFile": "_framework/System.Threading.Tasks.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000391849530"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2551"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ikXWaAyF84wc+LTbnBgFQ0EVbmlDbagKxyUptuFaYOY=\""}, {"Name": "ETag", "Value": "W/\"3H7FuYrx8mWgl0lVlLoSlBBjD1D5v83i3Y2xkMdyq4g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3H7FuYrx8mWgl0lVlLoSlBBjD1D5v83i3Y2xkMdyq4g="}]}, {"Route": "_framework/System.Threading.Tasks.wasm.gz", "AssetFile": "_framework/System.Threading.Tasks.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2551"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ikXWaAyF84wc+LTbnBgFQ0EVbmlDbagKxyUptuFaYOY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ikXWaAyF84wc+LTbnBgFQ0EVbmlDbagKxyUptuFaYOY="}]}, {"Route": "_framework/System.Threading.Thread.wasm", "AssetFile": "_framework/System.Threading.Thread.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+BKDC0rEeHnF2uPG7rwR8qd8yd6LXDmEkj1VjXA3rbY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+BKDC0rEeHnF2uPG7rwR8qd8yd6LXDmEkj1VjXA3rbY="}]}, {"Route": "_framework/System.Threading.Thread.wasm", "AssetFile": "_framework/System.Threading.Thread.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000429737860"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2326"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+dBqDuqpgvrn6bUQmbY5PArYJyiaB8PyMLulpfIwXEY=\""}, {"Name": "ETag", "Value": "W/\"+BKDC0rEeHnF2uPG7rwR8qd8yd6LXDmEkj1VjXA3rbY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+BKDC0rEeHnF2uPG7rwR8qd8yd6LXDmEkj1VjXA3rbY="}]}, {"Route": "_framework/System.Threading.Thread.wasm.gz", "AssetFile": "_framework/System.Threading.Thread.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2326"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+dBqDuqpgvrn6bUQmbY5PArYJyiaB8PyMLulpfIwXEY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+dBqDuqpgvrn6bUQmbY5PArYJyiaB8PyMLulpfIwXEY="}]}, {"Route": "_framework/System.Threading.ThreadPool.wasm", "AssetFile": "_framework/System.Threading.ThreadPool.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"k3R9JhTP3cgUqIVkXqJVbOfS7aWxAbuEMr+5S35xuys=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k3R9JhTP3cgUqIVkXqJVbOfS7aWxAbuEMr+5S35xuys="}]}, {"Route": "_framework/System.Threading.ThreadPool.wasm", "AssetFile": "_framework/System.Threading.ThreadPool.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000446229362"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2240"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PGeQ7fAhpIMJut8F3XqeIsHvJhrX37Hj1tDxut/mBCM=\""}, {"Name": "ETag", "Value": "W/\"k3R9JhTP3cgUqIVkXqJVbOfS7aWxAbuEMr+5S35xuys=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k3R9JhTP3cgUqIVkXqJVbOfS7aWxAbuEMr+5S35xuys="}]}, {"Route": "_framework/System.Threading.ThreadPool.wasm.gz", "AssetFile": "_framework/System.Threading.ThreadPool.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2240"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"PGeQ7fAhpIMJut8F3XqeIsHvJhrX37Hj1tDxut/mBCM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PGeQ7fAhpIMJut8F3XqeIsHvJhrX37Hj1tDxut/mBCM="}]}, {"Route": "_framework/System.Threading.Timer.wasm", "AssetFile": "_framework/System.Threading.Timer.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"o6sTf6vsZjwK0/CP3AMXbEBrCHwnrnNbiUqXcAvq1u4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o6sTf6vsZjwK0/CP3AMXbEBrCHwnrnNbiUqXcAvq1u4="}]}, {"Route": "_framework/System.Threading.Timer.wasm", "AssetFile": "_framework/System.Threading.Timer.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000471031559"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2122"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YsTdfPuE+I26sJC+PETDK7LlyKgoHs4jnpEk+puh6sI=\""}, {"Name": "ETag", "Value": "W/\"o6sTf6vsZjwK0/CP3AMXbEBrCHwnrnNbiUqXcAvq1u4=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o6sTf6vsZjwK0/CP3AMXbEBrCHwnrnNbiUqXcAvq1u4="}]}, {"Route": "_framework/System.Threading.Timer.wasm.gz", "AssetFile": "_framework/System.Threading.Timer.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2122"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"YsTdfPuE+I26sJC+PETDK7LlyKgoHs4jnpEk+puh6sI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YsTdfPuE+I26sJC+PETDK7LlyKgoHs4jnpEk+puh6sI="}]}, {"Route": "_framework/System.Threading.wasm", "AssetFile": "_framework/System.Threading.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33557"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"4uYGFQKN3BlGM0EP32/Bt8Y9EKHZKnVZckcENhq7kzY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uYGFQKN3BlGM0EP32/Bt8Y9EKHZKnVZckcENhq7kzY="}]}, {"Route": "_framework/System.Threading.wasm", "AssetFile": "_framework/System.Threading.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070081996"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14268"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NH8OpPuZpQkKRE1HQsKKwOSdlbmuXDcyusF7gfvRVnk=\""}, {"Name": "ETag", "Value": "W/\"4uYGFQKN3BlGM0EP32/Bt8Y9EKHZKnVZckcENhq7kzY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uYGFQKN3BlGM0EP32/Bt8Y9EKHZKnVZckcENhq7kzY="}]}, {"Route": "_framework/System.Threading.wasm.gz", "AssetFile": "_framework/System.Threading.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14268"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"NH8OpPuZpQkKRE1HQsKKwOSdlbmuXDcyusF7gfvRVnk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NH8OpPuZpQkKRE1HQsKKwOSdlbmuXDcyusF7gfvRVnk="}]}, {"Route": "_framework/System.Transactions.Local.wasm", "AssetFile": "_framework/System.Transactions.Local.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "164117"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+iXYbpxrc6wABk4G5H5qbjSaeVn7kCLlKfdq2zUKNTU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+iXYbpxrc6wABk4G5H5qbjSaeVn7kCLlKfdq2zUKNTU="}]}, {"Route": "_framework/System.Transactions.Local.wasm", "AssetFile": "_framework/System.Transactions.Local.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000019564877"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "51111"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gURcH9IbhO1iuiVdSzW5QwShab7Yf/eBGIH0qQROOmE=\""}, {"Name": "ETag", "Value": "W/\"+iXYbpxrc6wABk4G5H5qbjSaeVn7kCLlKfdq2zUKNTU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+iXYbpxrc6wABk4G5H5qbjSaeVn7kCLlKfdq2zUKNTU="}]}, {"Route": "_framework/System.Transactions.Local.wasm.gz", "AssetFile": "_framework/System.Transactions.Local.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "51111"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"gURcH9IbhO1iuiVdSzW5QwShab7Yf/eBGIH0qQROOmE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gURcH9IbhO1iuiVdSzW5QwShab7Yf/eBGIH0qQROOmE="}]}, {"Route": "_framework/System.Transactions.wasm", "AssetFile": "_framework/System.Transactions.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+i0mTxFj4cXE3yFOxqp001KYxyC6nSIB7k12B6N+It0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+i0mTxFj4cXE3yFOxqp001KYxyC6nSIB7k12B6N+It0="}]}, {"Route": "_framework/System.Transactions.wasm", "AssetFile": "_framework/System.Transactions.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000423011844"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2363"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ncXct4NvStxLfuGEH31hrHBZACBT0p7A+4I1jOF+qSk=\""}, {"Name": "ETag", "Value": "W/\"+i0mTxFj4cXE3yFOxqp001KYxyC6nSIB7k12B6N+It0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+i0mTxFj4cXE3yFOxqp001KYxyC6nSIB7k12B6N+It0="}]}, {"Route": "_framework/System.Transactions.wasm.gz", "AssetFile": "_framework/System.Transactions.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2363"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ncXct4NvStxLfuGEH31hrHBZACBT0p7A+4I1jOF+qSk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ncXct4NvStxLfuGEH31hrHBZACBT0p7A+4I1jOF+qSk="}]}, {"Route": "_framework/System.ValueTuple.wasm", "AssetFile": "_framework/System.ValueTuple.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"eLqnCqRYhzcJ58lX7yw5sXIZRnormagQGMF9l494K1A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eLqnCqRYhzcJ58lX7yw5sXIZRnormagQGMF9l494K1A="}]}, {"Route": "_framework/System.ValueTuple.wasm", "AssetFile": "_framework/System.ValueTuple.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461041955"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"wND/Q8O4WfOzbBQ3CfCA/FYZciR9/6oV9tdXT8fvoww=\""}, {"Name": "ETag", "Value": "W/\"eLqnCqRYhzcJ58lX7yw5sXIZRnormagQGMF9l494K1A=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eLqnCqRYhzcJ58lX7yw5sXIZRnormagQGMF9l494K1A="}]}, {"Route": "_framework/System.ValueTuple.wasm.gz", "AssetFile": "_framework/System.ValueTuple.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"wND/Q8O4WfOzbBQ3CfCA/FYZciR9/6oV9tdXT8fvoww=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wND/Q8O4WfOzbBQ3CfCA/FYZciR9/6oV9tdXT8fvoww="}]}, {"Route": "_framework/System.Web.HttpUtility.wasm", "AssetFile": "_framework/System.Web.HttpUtility.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17173"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UKjP8gb7gx+zTsa1bsFoFSA5+P6qIt5/36pK9v9uoLU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKjP8gb7gx+zTsa1bsFoFSA5+P6qIt5/36pK9v9uoLU="}]}, {"Route": "_framework/System.Web.HttpUtility.wasm", "AssetFile": "_framework/System.Web.HttpUtility.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000119545726"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8364"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1l6SFCrd7omEBMOUNd3pqqnvJ9TTG0PClsOyeGTcRHc=\""}, {"Name": "ETag", "Value": "W/\"UKjP8gb7gx+zTsa1bsFoFSA5+P6qIt5/36pK9v9uoLU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UKjP8gb7gx+zTsa1bsFoFSA5+P6qIt5/36pK9v9uoLU="}]}, {"Route": "_framework/System.Web.HttpUtility.wasm.gz", "AssetFile": "_framework/System.Web.HttpUtility.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8364"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"1l6SFCrd7omEBMOUNd3pqqnvJ9TTG0PClsOyeGTcRHc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1l6SFCrd7omEBMOUNd3pqqnvJ9TTG0PClsOyeGTcRHc="}]}, {"Route": "_framework/System.Web.wasm", "AssetFile": "_framework/System.Web.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4885"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"JVwcP8jT44uNJHimNKHdVW6iuxDqhXfirZ1meOruesg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVwcP8jT44uNJHimNKHdVW6iuxDqhXfirZ1meOruesg="}]}, {"Route": "_framework/System.Web.wasm", "AssetFile": "_framework/System.Web.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000473933649"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2109"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ysADCJ/alSGg+ZI60io3jr2BgL2MQBJ1EtZU6we37ko=\""}, {"Name": "ETag", "Value": "W/\"JVwcP8jT44uNJHimNKHdVW6iuxDqhXfirZ1meOruesg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JVwcP8jT44uNJHimNKHdVW6iuxDqhXfirZ1meOruesg="}]}, {"Route": "_framework/System.Web.wasm.gz", "AssetFile": "_framework/System.Web.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2109"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"ysADCJ/alSGg+ZI60io3jr2BgL2MQBJ1EtZU6we37ko=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysADCJ/alSGg+ZI60io3jr2BgL2MQBJ1EtZU6we37ko="}]}, {"Route": "_framework/System.Windows.wasm", "AssetFile": "_framework/System.Windows.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"WkZ+LxX7O+XdcOQc4I880DKZHm07RnwcDUniWJuwv+M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WkZ+LxX7O+XdcOQc4I880DKZHm07RnwcDUniWJuwv+M="}]}, {"Route": "_framework/System.Windows.wasm", "AssetFile": "_framework/System.Windows.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000440917108"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2267"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+7PT2U+Q8e2hY8GEqlhgwkhWRMLzlGalnI3VvuZ6aSI=\""}, {"Name": "ETag", "Value": "W/\"WkZ+LxX7O+XdcOQc4I880DKZHm07RnwcDUniWJuwv+M=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WkZ+LxX7O+XdcOQc4I880DKZHm07RnwcDUniWJuwv+M="}]}, {"Route": "_framework/System.Windows.wasm.gz", "AssetFile": "_framework/System.Windows.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2267"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+7PT2U+Q8e2hY8GEqlhgwkhWRMLzlGalnI3VvuZ6aSI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+7PT2U+Q8e2hY8GEqlhgwkhWRMLzlGalnI3VvuZ6aSI="}]}, {"Route": "_framework/System.Xml.Linq.wasm", "AssetFile": "_framework/System.Xml.Linq.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"afKWKg3I1tBL50BALQOCihqhZVwTsaJX3682HNbCUPU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-afKWKg3I1tBL50BALQOCihqhZVwTsaJX3682HNbCUPU="}]}, {"Route": "_framework/System.Xml.Linq.wasm", "AssetFile": "_framework/System.Xml.Linq.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000455580866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2194"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hn73JIW3W7WPdP/OuRonX0GjuhAAOkDJHt5V3NgUMpQ=\""}, {"Name": "ETag", "Value": "W/\"afKWKg3I1tBL50BALQOCihqhZVwTsaJX3682HNbCUPU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-afKWKg3I1tBL50BALQOCihqhZVwTsaJX3682HNbCUPU="}]}, {"Route": "_framework/System.Xml.Linq.wasm.gz", "AssetFile": "_framework/System.Xml.Linq.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2194"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"hn73JIW3W7WPdP/OuRonX0GjuhAAOkDJHt5V3NgUMpQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hn73JIW3W7WPdP/OuRonX0GjuhAAOkDJHt5V3NgUMpQ="}]}, {"Route": "_framework/System.Xml.ReaderWriter.wasm", "AssetFile": "_framework/System.Xml.ReaderWriter.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11541"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"4MitaDlkMxWjgk1/FUh4K1U4ZW81dtfOEMIxTocvD/Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4MitaDlkMxWjgk1/FUh4K1U4ZW81dtfOEMIxTocvD/Q="}]}, {"Route": "_framework/System.Xml.ReaderWriter.wasm", "AssetFile": "_framework/System.Xml.ReaderWriter.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000249563264"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9HHszKTm6dhmjaf7JacDsRPp4mnhgltGweuAfqNP1gg=\""}, {"Name": "ETag", "Value": "W/\"4MitaDlkMxWjgk1/FUh4K1U4ZW81dtfOEMIxTocvD/Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4MitaDlkMxWjgk1/FUh4K1U4ZW81dtfOEMIxTocvD/Q="}]}, {"Route": "_framework/System.Xml.ReaderWriter.wasm.gz", "AssetFile": "_framework/System.Xml.ReaderWriter.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4006"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"9HHszKTm6dhmjaf7JacDsRPp4mnhgltGweuAfqNP1gg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9HHszKTm6dhmjaf7JacDsRPp4mnhgltGweuAfqNP1gg="}]}, {"Route": "_framework/System.Xml.Serialization.wasm", "AssetFile": "_framework/System.Xml.Serialization.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"+Lhh+xLYP06JCxH4UW1YPraL5PbU+oVszfovCOy38n0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Lhh+xLYP06JCxH4UW1YPraL5PbU+oVszfovCOy38n0="}]}, {"Route": "_framework/System.Xml.Serialization.wasm", "AssetFile": "_framework/System.Xml.Serialization.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000448430493"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2229"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s2xQu75S6USYIN7blrOY4Eq9a1NO1nDrzyMWgyHXPDU=\""}, {"Name": "ETag", "Value": "W/\"+Lhh+xLYP06JCxH4UW1YPraL5PbU+oVszfovCOy38n0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Lhh+xLYP06JCxH4UW1YPraL5PbU+oVszfovCOy38n0="}]}, {"Route": "_framework/System.Xml.Serialization.wasm.gz", "AssetFile": "_framework/System.Xml.Serialization.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2229"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s2xQu75S6USYIN7blrOY4Eq9a1NO1nDrzyMWgyHXPDU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s2xQu75S6USYIN7blrOY4Eq9a1NO1nDrzyMWgyHXPDU="}]}, {"Route": "_framework/System.Xml.XDocument.wasm", "AssetFile": "_framework/System.Xml.XDocument.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"qj5eknfd7lsF/LnjfB+4qzHBxlTQ8nxqVUZ0PeMPXzc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qj5eknfd7lsF/LnjfB+4qzHBxlTQ8nxqVUZ0PeMPXzc="}]}, {"Route": "_framework/System.Xml.XDocument.wasm", "AssetFile": "_framework/System.Xml.XDocument.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000419287212"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2384"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mJ6PIEXjkORUn99uSeHYgqSclNyUYkoeAhccdMQ+CLE=\""}, {"Name": "ETag", "Value": "W/\"qj5eknfd7lsF/LnjfB+4qzHBxlTQ8nxqVUZ0PeMPXzc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qj5eknfd7lsF/LnjfB+4qzHBxlTQ8nxqVUZ0PeMPXzc="}]}, {"Route": "_framework/System.Xml.XDocument.wasm.gz", "AssetFile": "_framework/System.Xml.XDocument.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2384"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"mJ6PIEXjkORUn99uSeHYgqSclNyUYkoeAhccdMQ+CLE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mJ6PIEXjkORUn99uSeHYgqSclNyUYkoeAhccdMQ+CLE="}]}, {"Route": "_framework/System.Xml.XPath.XDocument.wasm", "AssetFile": "_framework/System.Xml.XPath.XDocument.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DasLcwuC0wzI6vJ0ujX5Tv9zsFUxk2Igm+VKMKjzVpA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DasLcwuC0wzI6vJ0ujX5Tv9zsFUxk2Igm+VKMKjzVpA="}]}, {"Route": "_framework/System.Xml.XPath.XDocument.wasm", "AssetFile": "_framework/System.Xml.XPath.XDocument.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000403877221"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2475"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3Qq+9EAXly+jArLRmkh5t65p9BRXeF1vivhwwiMcJCc=\""}, {"Name": "ETag", "Value": "W/\"DasLcwuC0wzI6vJ0ujX5Tv9zsFUxk2Igm+VKMKjzVpA=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DasLcwuC0wzI6vJ0ujX5Tv9zsFUxk2Igm+VKMKjzVpA="}]}, {"Route": "_framework/System.Xml.XPath.XDocument.wasm.gz", "AssetFile": "_framework/System.Xml.XPath.XDocument.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2475"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"3Qq+9EAXly+jArLRmkh5t65p9BRXeF1vivhwwiMcJCc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3Qq+9EAXly+jArLRmkh5t65p9BRXeF1vivhwwiMcJCc="}]}, {"Route": "_framework/System.Xml.XPath.wasm", "AssetFile": "_framework/System.Xml.XPath.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"OGo60KkChO7wzxQq5piTL0YDiNeaH+VEhdsS5bX0edY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OGo60KkChO7wzxQq5piTL0YDiNeaH+VEhdsS5bX0edY="}]}, {"Route": "_framework/System.Xml.XPath.wasm", "AssetFile": "_framework/System.Xml.XPath.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000432338954"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2312"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rpLEJSkMYJL5MJk/BdY6HMXauCUPnv38NuOm12ivQ64=\""}, {"Name": "ETag", "Value": "W/\"OGo60KkChO7wzxQq5piTL0YDiNeaH+VEhdsS5bX0edY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OGo60KkChO7wzxQq5piTL0YDiNeaH+VEhdsS5bX0edY="}]}, {"Route": "_framework/System.Xml.XPath.wasm.gz", "AssetFile": "_framework/System.Xml.XPath.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2312"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"rpLEJSkMYJL5MJk/BdY6HMXauCUPnv38NuOm12ivQ64=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rpLEJSkMYJL5MJk/BdY6HMXauCUPnv38NuOm12ivQ64="}]}, {"Route": "_framework/System.Xml.XmlDocument.wasm", "AssetFile": "_framework/System.Xml.XmlDocument.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5397"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/zllBRuMt2vrmhEET4rh7Yed8w+Z0oaKk95b/icxRAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/zllBRuMt2vrmhEET4rh7Yed8w+Z0oaKk95b/icxRAY="}]}, {"Route": "_framework/System.Xml.XmlDocument.wasm", "AssetFile": "_framework/System.Xml.XmlDocument.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000425894378"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2347"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DewXljqINJgZkuiRM17FRtKR8UWNeqy2HMQ3GbzHqKI=\""}, {"Name": "ETag", "Value": "W/\"/zllBRuMt2vrmhEET4rh7Yed8w+Z0oaKk95b/icxRAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/zllBRuMt2vrmhEET4rh7Yed8w+Z0oaKk95b/icxRAY="}]}, {"Route": "_framework/System.Xml.XmlDocument.wasm.gz", "AssetFile": "_framework/System.Xml.XmlDocument.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2347"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"DewXljqINJgZkuiRM17FRtKR8UWNeqy2HMQ3GbzHqKI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DewXljqINJgZkuiRM17FRtKR8UWNeqy2HMQ3GbzHqKI="}]}, {"Route": "_framework/System.Xml.XmlSerializer.wasm", "AssetFile": "_framework/System.Xml.XmlSerializer.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7445"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"cAnvxaJ7s3ORcl93NpSgQJMGcM7ThJn3SwPqppSLLLg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cAnvxaJ7s3ORcl93NpSgQJMGcM7ThJn3SwPqppSLLLg="}]}, {"Route": "_framework/System.Xml.XmlSerializer.wasm", "AssetFile": "_framework/System.Xml.XmlSerializer.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000350877193"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2849"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dZDl9sZSkiREVBu0m0FW5taTql5PSDwXPuSMS6I9iAo=\""}, {"Name": "ETag", "Value": "W/\"cAnvxaJ7s3ORcl93NpSgQJMGcM7ThJn3SwPqppSLLLg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cAnvxaJ7s3ORcl93NpSgQJMGcM7ThJn3SwPqppSLLLg="}]}, {"Route": "_framework/System.Xml.XmlSerializer.wasm.gz", "AssetFile": "_framework/System.Xml.XmlSerializer.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2849"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"dZDl9sZSkiREVBu0m0FW5taTql5PSDwXPuSMS6I9iAo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dZDl9sZSkiREVBu0m0FW5taTql5PSDwXPuSMS6I9iAo="}]}, {"Route": "_framework/System.Xml.wasm", "AssetFile": "_framework/System.Xml.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13077"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"y3rnbUnNvGaleCSyCoPGtBe6goDy6aKCF3LVTLNuB3s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y3rnbUnNvGaleCSyCoPGtBe6goDy6aKCF3LVTLNuB3s="}]}, {"Route": "_framework/System.Xml.wasm", "AssetFile": "_framework/System.Xml.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000237868696"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4203"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"7IPXMAZiR+5byxDeJbsyEaNl7uofrJalVyYufn6J5xI=\""}, {"Name": "ETag", "Value": "W/\"y3rnbUnNvGaleCSyCoPGtBe6goDy6aKCF3LVTLNuB3s=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y3rnbUnNvGaleCSyCoPGtBe6goDy6aKCF3LVTLNuB3s="}]}, {"Route": "_framework/System.Xml.wasm.gz", "AssetFile": "_framework/System.Xml.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4203"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"7IPXMAZiR+5byxDeJbsyEaNl7uofrJalVyYufn6J5xI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7IPXMAZiR+5byxDeJbsyEaNl7uofrJalVyYufn6J5xI="}]}, {"Route": "_framework/System.wasm", "AssetFile": "_framework/System.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39701"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"LBDJCJw31LseOAfVkuXRfuwsxleWafHTVnotlum0HJ8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBDJCJw31LseOAfVkuXRfuwsxleWafHTVnotlum0HJ8="}]}, {"Route": "_framework/System.wasm", "AssetFile": "_framework/System.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085157115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11742"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"S4IKFw92CyHIgaEHdv813q588Dj2qmX3HU/YM78aRdI=\""}, {"Name": "ETag", "Value": "W/\"LBDJCJw31LseOAfVkuXRfuwsxleWafHTVnotlum0HJ8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBDJCJw31LseOAfVkuXRfuwsxleWafHTVnotlum0HJ8="}]}, {"Route": "_framework/System.wasm.gz", "AssetFile": "_framework/System.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11742"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"S4IKFw92CyHIgaEHdv813q588Dj2qmX3HU/YM78aRdI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S4IKFw92CyHIgaEHdv813q588Dj2qmX3HU/YM78aRdI="}]}, {"Route": "_framework/WindowsBase.wasm", "AssetFile": "_framework/WindowsBase.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5909"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"yzKS6C73fTVwWhmFWbQTop3g56sZFiRZkc5qNctgIs8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yzKS6C73fTVwWhmFWbQTop3g56sZFiRZkc5qNctgIs8="}]}, {"Route": "_framework/WindowsBase.wasm", "AssetFile": "_framework/WindowsBase.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000398724083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2507"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"lpvEUXdbKWNKikGgIS9fPnPTuavZ3w1XitqeI/eO7O0=\""}, {"Name": "ETag", "Value": "W/\"yzKS6C73fTVwWhmFWbQTop3g56sZFiRZkc5qNctgIs8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yzKS6C73fTVwWhmFWbQTop3g56sZFiRZkc5qNctgIs8="}]}, {"Route": "_framework/WindowsBase.wasm.gz", "AssetFile": "_framework/WindowsBase.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2507"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"lpvEUXdbKWNKikGgIS9fPnPTuavZ3w1XitqeI/eO7O0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lpvEUXdbKWNKikGgIS9fPnPTuavZ3w1XitqeI/eO7O0="}]}, {"Route": "_framework/blazor.boot.json", "AssetFile": "_framework/blazor.boot.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19668"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"1eCPgzApK96AVNdbbU6WHAtKy4w7j79h+CdLdB7h6PU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:54:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1eCPgzApK96AVNdbbU6WHAtKy4w7j79h+CdLdB7h6PU="}]}, {"Route": "_framework/blazor.boot.json", "AssetFile": "_framework/blazor.boot.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000114902907"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8702"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"IKRcpmmfiREyaeAgE2GDso7rGesSbAfSJAC+9vnTeyo=\""}, {"Name": "ETag", "Value": "W/\"1eCPgzApK96AVNdbbU6WHAtKy4w7j79h+CdLdB7h6PU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:54:19 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1eCPgzApK96AVNdbbU6WHAtKy4w7j79h+CdLdB7h6PU="}]}, {"Route": "_framework/blazor.boot.json.gz", "AssetFile": "_framework/blazor.boot.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8702"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"IKRcpmmfiREyaeAgE2GDso7rGesSbAfSJAC+9vnTeyo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 07:54:19 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IKRcpmmfiREyaeAgE2GDso7rGesSbAfSJAC+9vnTeyo="}]}, {"Route": "_framework/blazor.webassembly.js", "AssetFile": "_framework/blazor.webassembly.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60300"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gvHfnndfEu1tRf0rFb5988rWq7ITIotOaE8+AMbKYbc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 18:38:30 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gvHfnndfEu1tRf0rFb5988rWq7ITIotOaE8+AMbKYbc="}]}, {"Route": "_framework/blazor.webassembly.js", "AssetFile": "_framework/blazor.webassembly.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000052559655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19025"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=\""}, {"Name": "ETag", "Value": "W/\"gvHfnndfEu1tRf0rFb5988rWq7ITIotOaE8+AMbKYbc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gvHfnndfEu1tRf0rFb5988rWq7ITIotOaE8+AMbKYbc="}]}, {"Route": "_framework/blazor.webassembly.js.gz", "AssetFile": "_framework/blazor.webassembly.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19025"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+V2Lg1Jy/5iLhIkZHMvZU1uFPbr2UWCvWIR1xl5s8EE="}]}, {"Route": "_framework/dotnet.js", "AssetFile": "_framework/dotnet.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35803"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4BM/t8p0DnD4qMI6D0uGoZUfeuUpEY8MsJd0i5FO/DY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:21:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4BM/t8p0DnD4qMI6D0uGoZUfeuUpEY8MsJd0i5FO/DY="}]}, {"Route": "_framework/dotnet.js", "AssetFile": "_framework/dotnet.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000087374399"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11444"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+FjKEmCl722cfzxfUP3BEu6OxdvdSFAqAbFBPEAU3JM=\""}, {"Name": "ETag", "Value": "W/\"4BM/t8p0DnD4qMI6D0uGoZUfeuUpEY8MsJd0i5FO/DY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4BM/t8p0DnD4qMI6D0uGoZUfeuUpEY8MsJd0i5FO/DY="}]}, {"Route": "_framework/dotnet.js.gz", "AssetFile": "_framework/dotnet.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11444"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+FjKEmCl722cfzxfUP3BEu6OxdvdSFAqAbFBPEAU3JM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+FjKEmCl722cfzxfUP3BEu6OxdvdSFAqAbFBPEAU3JM="}]}, {"Route": "_framework/dotnet.js.map", "AssetFile": "_framework/dotnet.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49719"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NNvqsrDPhWgHk1gcb8A9roL8dhlXoJy44IEzal1DPRQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:21:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NNvqsrDPhWgHk1gcb8A9roL8dhlXoJy44IEzal1DPRQ="}]}, {"Route": "_framework/dotnet.js.map", "AssetFile": "_framework/dotnet.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053398836"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HfTT+J7xkdjQWt3mXnN1g5tPs0syGMKpYFOvlGJ9PwI=\""}, {"Name": "ETag", "Value": "W/\"NNvqsrDPhWgHk1gcb8A9roL8dhlXoJy44IEzal1DPRQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NNvqsrDPhWgHk1gcb8A9roL8dhlXoJy44IEzal1DPRQ="}]}, {"Route": "_framework/dotnet.js.map.gz", "AssetFile": "_framework/dotnet.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"HfTT+J7xkdjQWt3mXnN1g5tPs0syGMKpYFOvlGJ9PwI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HfTT+J7xkdjQWt3mXnN1g5tPs0syGMKpYFOvlGJ9PwI="}]}, {"Route": "_framework/dotnet.native.js", "AssetFile": "_framework/dotnet.native.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "160089"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1QpKPnxEuRCElzcp+e3MrovJeAJkuzqKmRQay/6Aa08=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:22:22 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1QpKPnxEuRCElzcp+e3MrovJeAJkuzqKmRQay/6Aa08="}]}, {"Route": "_framework/dotnet.native.js", "AssetFile": "_framework/dotnet.native.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000027654867"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "36159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iFSf7EZCTIMZ9pI4EpWksQPtJ3a5mpmXjOsWtmw7cZY=\""}, {"Name": "ETag", "Value": "W/\"1QpKPnxEuRCElzcp+e3MrovJeAJkuzqKmRQay/6Aa08=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1QpKPnxEuRCElzcp+e3MrovJeAJkuzqKmRQay/6Aa08="}]}, {"Route": "_framework/dotnet.native.js.gz", "AssetFile": "_framework/dotnet.native.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "36159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"iFSf7EZCTIMZ9pI4EpWksQPtJ3a5mpmXjOsWtmw7cZY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iFSf7EZCTIMZ9pI4EpWksQPtJ3a5mpmXjOsWtmw7cZY="}]}, {"Route": "_framework/dotnet.native.wasm", "AssetFile": "_framework/dotnet.native.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2919938"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"0icL1rktur02b0t8WzUUQh+w3sa0rLR7YIla31PAEHM=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:22:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0icL1rktur02b0t8WzUUQh+w3sa0rLR7YIla31PAEHM="}]}, {"Route": "_framework/dotnet.native.wasm", "AssetFile": "_framework/dotnet.native.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000000866234"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1154421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"v/+o5EoupyvDzqHEvV2fysejXQGNtcDXEODUxmX7ouQ=\""}, {"Name": "ETag", "Value": "W/\"0icL1rktur02b0t8WzUUQh+w3sa0rLR7YIla31PAEHM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0icL1rktur02b0t8WzUUQh+w3sa0rLR7YIla31PAEHM="}]}, {"Route": "_framework/dotnet.native.wasm.gz", "AssetFile": "_framework/dotnet.native.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1154421"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"v/+o5EoupyvDzqHEvV2fysejXQGNtcDXEODUxmX7ouQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v/+o5EoupyvDzqHEvV2fysejXQGNtcDXEODUxmX7ouQ="}]}, {"Route": "_framework/dotnet.runtime.js", "AssetFile": "_framework/dotnet.runtime.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "223202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QVLJL2PFoMyGwMOZIRShe4/0TJQfK2WMqoIYdVaI+yM=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:21:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QVLJL2PFoMyGwMOZIRShe4/0TJQfK2WMqoIYdVaI+yM="}]}, {"Route": "_framework/dotnet.runtime.js", "AssetFile": "_framework/dotnet.runtime.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015390772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64973"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RMfkUitlqIAuOW3sKnS1ufX3Rvj1H2q2E1C6G6Kie8g=\""}, {"Name": "ETag", "Value": "W/\"QVLJL2PFoMyGwMOZIRShe4/0TJQfK2WMqoIYdVaI+yM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QVLJL2PFoMyGwMOZIRShe4/0TJQfK2WMqoIYdVaI+yM="}]}, {"Route": "_framework/dotnet.runtime.js.gz", "AssetFile": "_framework/dotnet.runtime.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64973"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RMfkUitlqIAuOW3sKnS1ufX3Rvj1H2q2E1C6G6Kie8g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RMfkUitlqIAuOW3sKnS1ufX3Rvj1H2q2E1C6G6Kie8g="}]}, {"Route": "_framework/dotnet.runtime.js.map", "AssetFile": "_framework/dotnet.runtime.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "318294"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J/XqcY5bnoWuyFAfmMdbFTWao/ObV6zL9Vj1ekF8lEM=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:21:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J/XqcY5bnoWuyFAfmMdbFTWao/ObV6zL9Vj1ekF8lEM="}]}, {"Route": "_framework/dotnet.runtime.js.map", "AssetFile": "_framework/dotnet.runtime.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009970686"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "100293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"F4YKJR+EQCIfHPdWnXu4tvjiqdF48evG9I47BRu5Nt8=\""}, {"Name": "ETag", "Value": "W/\"J/XqcY5bnoWuyFAfmMdbFTWao/ObV6zL9Vj1ekF8lEM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J/XqcY5bnoWuyFAfmMdbFTWao/ObV6zL9Vj1ekF8lEM="}]}, {"Route": "_framework/dotnet.runtime.js.map.gz", "AssetFile": "_framework/dotnet.runtime.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "100293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"F4YKJR+EQCIfHPdWnXu4tvjiqdF48evG9I47BRu5Nt8=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F4YKJR+EQCIfHPdWnXu4tvjiqdF48evG9I47BRu5Nt8="}]}, {"Route": "_framework/emcc-props.json", "AssetFile": "_framework/emcc-props.json", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"EDndcUl06UUgbB5x0DTQqfgwLv4TZo64Ka6aUqdbGTs=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 00:20:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EDndcUl06UUgbB5x0DTQqfgwLv4TZo64Ka6aUqdbGTs="}]}, {"Route": "_framework/emcc-props.json", "AssetFile": "_framework/emcc-props.json.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001686340641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=\""}, {"Name": "ETag", "Value": "W/\"EDndcUl06UUgbB5x0DTQqfgwLv4TZo64Ka6aUqdbGTs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EDndcUl06UUgbB5x0DTQqfgwLv4TZo64Ka6aUqdbGTs="}]}, {"Route": "_framework/emcc-props.json.gz", "AssetFile": "_framework/emcc-props.json.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "application/json"}, {"Name": "ETag", "Value": "\"7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7t6AVk6lvrWEqY7hRavzlgS107PQ4doQEFxFK3dDtRQ="}]}, {"Route": "_framework/icudt_CJK.dat", "AssetFile": "_framework/icudt_CJK.dat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "956416"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 14:14:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk="}]}, {"Route": "_framework/icudt_CJK.dat", "AssetFile": "_framework/icudt_CJK.dat.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003002002"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "333110"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=\""}, {"Name": "ETag", "Value": "W/\"SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk="}]}, {"Route": "_framework/icudt_CJK.dat.gz", "AssetFile": "_framework/icudt_CJK.dat.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "333110"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g="}]}, {"Route": "_framework/icudt_EFIGS.dat", "AssetFile": "_framework/icudt_EFIGS.dat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "550832"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 14:14:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc="}]}, {"Route": "_framework/icudt_EFIGS.dat", "AssetFile": "_framework/icudt_EFIGS.dat.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000005101052"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "196037"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=\""}, {"Name": "ETag", "Value": "W/\"8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc="}]}, {"Route": "_framework/icudt_EFIGS.dat.gz", "AssetFile": "_framework/icudt_EFIGS.dat.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "196037"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE="}]}, {"Route": "_framework/icudt_no_CJK.dat", "AssetFile": "_framework/icudt_no_CJK.dat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1107168"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Oct 2023 14:14:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}]}, {"Route": "_framework/icudt_no_CJK.dat", "AssetFile": "_framework/icudt_no_CJK.dat.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000003148426"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317618"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=\""}, {"Name": "ETag", "Value": "W/\"L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}]}, {"Route": "_framework/icudt_no_CJK.dat.gz", "AssetFile": "_framework/icudt_no_CJK.dat.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "317618"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk="}]}, {"Route": "_framework/mscorlib.wasm", "AssetFile": "_framework/mscorlib.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48917"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"UnQd0YPXNpwbXsKqO8RAqgUVuwZsSpprfAIPpdV/y1Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UnQd0YPXNpwbXsKqO8RAqgUVuwZsSpprfAIPpdV/y1Q="}]}, {"Route": "_framework/mscorlib.wasm", "AssetFile": "_framework/mscorlib.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000067828800"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14742"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s4panyVntSh+m2Psx4lw+ZfbTCOkX1XcUjeab3CvYQI=\""}, {"Name": "ETag", "Value": "W/\"UnQd0YPXNpwbXsKqO8RAqgUVuwZsSpprfAIPpdV/y1Q=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UnQd0YPXNpwbXsKqO8RAqgUVuwZsSpprfAIPpdV/y1Q="}]}, {"Route": "_framework/mscorlib.wasm.gz", "AssetFile": "_framework/mscorlib.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14742"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"s4panyVntSh+m2Psx4lw+ZfbTCOkX1XcUjeab3CvYQI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-s4panyVntSh+m2Psx4lw+ZfbTCOkX1XcUjeab3CvYQI="}]}, {"Route": "_framework/netstandard.wasm", "AssetFile": "_framework/netstandard.wasm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90389"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"/QbD8JDZyYwfyk89W3qJL3HTAINDCVziZIySHIZcDhw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/QbD8JDZyYwfyk89W3qJL3HTAINDCVziZIySHIZcDhw="}]}, {"Route": "_framework/netstandard.wasm", "AssetFile": "_framework/netstandard.wasm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038461538"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25999"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jkO8ofRAsXoMRgK/yLKb+ZSxQmj2PQ4hHgh6T/un0xo=\""}, {"Name": "ETag", "Value": "W/\"/QbD8JDZyYwfyk89W3qJL3HTAINDCVziZIySHIZcDhw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/QbD8JDZyYwfyk89W3qJL3HTAINDCVziZIySHIZcDhw="}]}, {"Route": "_framework/netstandard.wasm.gz", "AssetFile": "_framework/netstandard.wasm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25999"}, {"Name": "Content-Type", "Value": "application/wasm"}, {"Name": "ETag", "Value": "\"jkO8ofRAsXoMRgK/yLKb+ZSxQmj2PQ4hHgh6T/un0xo=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 06:20:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jkO8ofRAsXoMRgK/yLKb+ZSxQmj2PQ4hHgh6T/un0xo="}]}]}