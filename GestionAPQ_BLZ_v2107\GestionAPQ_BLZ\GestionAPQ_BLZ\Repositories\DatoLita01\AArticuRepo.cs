﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using System.Linq.Expressions;
using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : Repository<AArticu, DatoLita01Context>, IAArticuRepo
{
    private readonly IAEntdiaRepo _aEntdiaRepo;
    private readonly IALotessRepo _aLotessRepo;
    private readonly IAViscosRepo _aViscosRepo;

    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public AArticuRepo(DatoLita01Context dbContext, IALotessRepo aLotessRepo, IAEntdiaRepo aEntdiaRepo,
        IAViscosRepo aViscosRepo) : base(dbContext)
    {
        _aLotessRepo = aLotessRepo;
        _aEntdiaRepo = aEntdiaRepo;
        _aViscosRepo = aViscosRepo;
    }

    public IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        query = query.Where(i => i.Estadis.StartsWith("BABA"));
        query = !string.IsNullOrEmpty(idBarniz) ? query.Where(i => i.Codigo.Equals(idBarniz)) : query;

        return query;
    }

    public async Task<List<AArticu>> GetBarnices(string? idBarniz, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await GetIQueryableBarnices(idBarniz, asNoTracking).ToListAsync();
        return listaBarnices;
    }

    public async Task<List<(AArticu, ALotess)>> GetBarnicesInnerJoinLotesConStock(string? idBarniz,
        bool asNoTracking,
        CancellationToken cancellationToken)
    {
        // query de barnices
        var queryBarnices = GetIQueryableBarnices(idBarniz, asNoTracking);

        // query de lotes con stock
        var queryLotes = _aLotessRepo.GetIQueryableLotesConStock(idBarniz, asNoTracking);

        // ejecutamos la query con tupla
        var query = queryBarnices
            .Join(
                queryLotes,
                barniz => barniz.Codigo,
                lote => lote.Codigo,
                (barniz, lote) => new ValueTuple<AArticu, ALotess>(barniz, lote)
            );
        var resultados = await query.ToListAsync(cancellationToken);

        return resultados;
    }

    public async Task<List<(AArticu, ALotess, AEntdia)>> GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
        string? idBarniz,
        bool asNoTracking,
        Expression<Func<(AArticu, ALotess, AEntdia), object>>? groupBy,
        CancellationToken cancellationToken)
    {
        // query de barnices
        var queryBarnices = GetIQueryableBarnices(idBarniz, asNoTracking);

        // query de lotes con stock
        var queryLotes = _aLotessRepo.GetIQueryableLotesConStock(idBarniz, asNoTracking);

        // query entradas. en el join abajo, cogemos el más reciente, la primera
        var queryEntradas = _aEntdiaRepo.GetIQueryableEntradas(idBarniz, asNoTracking);

        // ejecutamos la query y agrupamos si fuera necesario
        var query = queryBarnices // innerjoin con lotes
            .Join(
                queryLotes,
                Barniz => Barniz.Codigo,
                Lote => Lote.Codigo,
                (Barniz, Lote) => new { Barniz, Lote }
            )
            .SelectMany(bl => // LEFT JOIN con a_entdia
                    queryEntradas
                        .Where(e =>
                            e.Codigo == (bl.Lote.Codigo ?? "")
                            && e.Lote.ToUpper() == (bl.Lote.Lote ?? "").ToUpper()
                            && e.Fecent.HasValue)
                        .OrderBy(e => e.Fecent)
                        .Take(1)
                        .DefaultIfEmpty(), // obligatorio para LEFT JOIN
                (bl, entrada) => new ValueTuple<AArticu, ALotess, AEntdia>(bl.Barniz, bl.Lote, entrada)
            );

        var resultados = await query.ToListAsync(cancellationToken);

        // hacemos el groupby postquery porque si lo hago vía ef me falla
        // por tanto, almacenamos resultados en memoria y agrupamos ahí
        if (groupBy != null)
        {
            var compiledGroupBy = groupBy.Compile();
            resultados = resultados
                .GroupBy(compiledGroupBy)
                .Select(g => g.FirstOrDefault())
                .ToList();
        }

        return resultados;
    }

    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        bool asNoTracking, CancellationToken cancellationToken)
    {
        var queryBarnices = GetIQueryableBarnices(idBarniz, asNoTracking);
        var queryViscosidades = _aViscosRepo.GetIQueryableViscosidades(idBarniz, asNoTracking);

        var query = queryBarnices
            .GroupJoin(
                queryViscosidades,
                barniz => barniz.Codigo,
                viscos => viscos.Codigo,
                (barniz, viscosGroup) => new { barniz, viscosGroup }
            )
            .SelectMany(
                bg => bg.viscosGroup.DefaultIfEmpty(),
                (bg, viscos) => new ValueTuple<AArticu, AViscos>(bg.barniz, viscos)
            );

        var resultados = await query.ToListAsync(cancellationToken);

        return resultados;
    }
}