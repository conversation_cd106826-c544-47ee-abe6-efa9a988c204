﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable di**ble
using System;
using System.Collections.Generic;
using GestionNoConformidades.Server.Data.Entities.CalidadLital**;
using Microsoft.EntityFrameworkCore;

namespace GestionNoConformidades.Server.Data.DbContexts.CalidadLital**;

public partial class CalidadLital**Context : DbContext
{
    public CalidadLital**Context()
    {
    }

    public CalidadLital**Context(DbContextOptions<CalidadLital**Context> options)
        : base(options)
    {
    }

    public virtual DbSet<Configuracion> Configuracion { get; set; }

    public virtual DbSet<Maquinas> Ma<PERSON><PERSON> { get; set; }

    public virtual DbSet<Muestras> Muestras { get; set; }

    public virtual DbSet<NoConformidades> NoConformidades { get; set; }

    public virtual DbSet<TipoNoCo> TipoNoCo { get; set; }

    public virtual DbSet<TipoPedido> TipoPedido { get; set; }

    public virtual DbSet<TipoSituacion> TipoSituacion { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Data Source=QPLANT1;Initial Catalog=CalidadLital**;Persist Security Info=True;User ID=**;Password=**;Encrypt=False");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Configuracion>(entity =>
        {
            entity.Property(e => e.Descripcion).IsRequired();
            entity.Property(e => e.Propiedad)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Valor).IsRequired();
        });

        modelBuilder.Entity<Maquinas>(entity =>
        {
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.IdErp)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<Muestras>(entity =>
        {
            entity.HasIndex(e => e.IdMuestra, "IX_Muestras").IsUnique();

            entity.Property(e => e.DatosCliente)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FechaRecepcion).HasColumnType("datetime");
            entity.Property(e => e.IdMuestra).HasMaxLength(50);
            entity.Property(e => e.Ubicacion).HasMaxLength(150);

            entity.HasOne(d => d.IdNoCoNavigation).WithMany(p => p.Muestras)
                .HasForeignKey(d => d.IdNoCo)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Muestras_NoConformidades");
        });

        modelBuilder.Entity<NoConformidades>(entity =>
        {
            entity.HasIndex(e => e.IdNoConformidad, "UK_NoConformidades").IsUnique();

            entity.Property(e => e.CosteFactura).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CosteHojasAchatarrar).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CosteManoObra).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CosteNoCalidad).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CosteReproceso).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CosteTransporte).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.DatosCliente).HasMaxLength(150);
            entity.Property(e => e.FechaCierre).HasColumnType("datetime");
            entity.Property(e => e.FechaComprobacionEficacia).HasColumnType("datetime");
            entity.Property(e => e.FechaContestacion).HasColumnType("datetime");
            entity.Property(e => e.FechaCreacion).HasColumnType("datetime");
            entity.Property(e => e.FechaFinContingencia).HasColumnType("datetime");
            entity.Property(e => e.IdNoCoCliente).HasMaxLength(150);
            entity.Property(e => e.IdNoConformidad)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.IdPedidoAfectado).HasMaxLength(50);
            entity.Property(e => e.IdPedidoClienteAfectado).HasMaxLength(150);
            entity.Property(e => e.IdReproceso).HasMaxLength(50);
            entity.Property(e => e.NumFactura).HasMaxLength(50);
            entity.Property(e => e.TituloNoCo).HasMaxLength(150);

            entity.HasOne(d => d.IdTipoNoCoNavigation).WithMany(p => p.NoConformidades)
                .HasForeignKey(d => d.IdTipoNoCo)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_NoConformidades_TipoNoCo");

            entity.HasOne(d => d.IdTipoPedidoNavigation).WithMany(p => p.NoConformidades)
                .HasForeignKey(d => d.IdTipoPedido)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_NoConformidades_TipoPedido");

            entity.HasOne(d => d.IdTipoSituacionNavigation).WithMany(p => p.NoConformidades)
                .HasForeignKey(d => d.IdTipoSituacion)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_NoConformidades_TipoSituacion");
        });

        modelBuilder.Entity<TipoNoCo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_TipoReclamacion");

            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<TipoPedido>(entity =>
        {
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<TipoSituacion>(entity =>
        {
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}