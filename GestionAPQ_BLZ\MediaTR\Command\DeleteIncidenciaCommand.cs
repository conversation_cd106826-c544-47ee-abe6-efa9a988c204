namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Repositories;
using Repositories.APQLitalsa.Base;

public record DeleteIncidenciaCommand(int IdIncidencia) : IRequest<SingleResult<int>>;

public class DeleteIncidenciaCommandHandler : IRequestHandler<DeleteIncidenciaCommand, SingleResult<int>>
{
    private readonly IIncidenciasRepo _incidenciasRepo;

    public DeleteIncidenciaCommandHandler(IIncidenciasRepo incidenciasRepo)
    {
        _incidenciasRepo = incidenciasRepo;
    }

    public async Task<SingleResult<int>> Handle(DeleteIncidenciaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        try
        {
            // Validaciones
            var validationErrors = ValidateDeleteIncidencia(request.IdIncidencia);
            if (validationErrors.Any())
            {
                foreach (var error in validationErrors)
                    result.Errors.Add(error);
                return result;
            }

            var incidencia = await _incidenciasRepo.GetById(request.IdIncidencia, new IncidenciasQueryOptions(), cancellationToken);
            if (incidencia == null)
            {
                result.Errors.Add("No se ha encontrado la incidencia en la base de datos.");
                return result;
            }

            await _incidenciasRepo.Delete(incidencia, cancellationToken);
            result.Data = request.IdIncidencia;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private static List<string> ValidateDeleteIncidencia(int idIncidencia)
    {
        var errors = new List<string>();

        if (idIncidencia <= 0)
            errors.Add("El ID de la incidencia debe ser un valor válido mayor a 0");

        return errors;
    }
}