namespace GestionAPQ_BLZ.DTO.Genericos;

public class InspeccionDTO
{
    public int? Id { get; set; }

    public int? IdProducto { get; set; }

    public string IdLote { get; set; } = string.Empty;

    public decimal? Viscosidad { get; set; }

    public decimal? Solidos { get; set; }

    public string ObservacionesAplicacion { get; set; }

    public bool? Inspeccion { get; set; }

    public int? IdOperario { get; set; }

    public DateTime? Fecha { get; set; }

    public decimal? TemperaturaViscosidad { get; set; }

    public string ObservacionesQ { get; set; }

    public DateTime? FechaCreacion { get; set; }

    public DateTime? FechaModificacion { get; set; }

    // Navigation properties
    public OperarioDTO? IdOperarioNavigation { get; set; }
}