use Apq<PERSON><PERSON><PERSON>; 

  

TRUNCATE TABLE [dbo].[Incidencias]; 

TRUNCATE TABLE [dbo].LOTESNODRIZAS; 

TRUNCATE TABLE [dbo].[Switchboard Items]; 

TRUNCATE TABLE [dbo].TablaAPQ; 

TRUNCATE TABLE [dbo].TablaInspecciones; 

TRUNCATE TABLE [dbo].TablaOperarios; 

TRUNCATE TABLE [dbo].TablaPesosEnvases; 

TRUNCATE TABLE [dbo].TablaProductosNodrizas; 

  

  

  

SET IDENTITY_INSERT lotesnodrizas ON; 

WITH LOTESNODRIZAS_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [LotesNodrizas]' 

    ) 

) 

INSERT INTO LOTESNODRIZAS([Id] 

      ,[FEC<PERSON>] 

      ,[<PERSON><PERSON><PERSON>] 

      ,[<PERSON><PERSON><PERSON>Z<PERSON>] 

      ,[ID<PERSON>OD<PERSON><PERSON>] 

      ,[<PERSON>OT<PERSON>] 

      ,[<PERSON><PERSON><PERSON><PERSON><PERSON>] 

      ,[ubicacion]) 

SELECT ID, FECHA, HORA, NODRIZA, IDPRODUCTO, LOTE, OPERARIO, UBICACIÓN FROM LOTESNODRIZAS_ACC 

SET IDENTITY_INSERT lotesnodrizas OFF; 

  

WITH SWITCHBOARDITEMS_ACC AS 

( 

SELECT *  

FROM OPENROWSET( 

'Microsoft.ACE.OLEDB.12.0',  

'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

'SELECT * FROM [Switchboard Items]' 

) 

) 

INSERT INTO [Switchboard Items]([SwitchboardID] 

      ,[ItemNumber] 

      ,[ItemText] 

      ,[Command] 

      ,[Argument]) 

SELECT [Switchboardid], ITEMNUMBER, ITEMTEXT, COMMAND, ARGUMENT FROM SWITCHBOARDITEMS_ACC; 

  

SET IDENTITY_INSERT TablaAPQ ON; 

WITH TablaAPQ_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [TablaAPQ]' 

    ) 

) 

INSERT INTO TablaAPQ([Id] 

      ,[IdPosición] 

      ,[Observacion]) 

SELECT ID, IdPosición, Observacion FROM TablaAPQ_ACC 

SET IDENTITY_INSERT TablaAPQ OFF; 

  

SET IDENTITY_INSERT TablaInspecciones ON; 

WITH TablaInspecciones_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [TablaInspecciones]' 

    ) 

) 

INSERT INTO TablaInspecciones([IdInspecciones] 

      ,[Idproducto] 

      ,[Idlote] 

      ,[Viscosidad] 

      ,[Solidos] 

      ,[ObservacionesAplicacion] 

      ,[Inspeccion] 

      ,[RealizadoPor] 

      ,[Fecha] 

      ,[TemperaturaViscosidad] 

      ,[ObservacionesQ]) 

SELECT IdInspecciones, IdProducto, IdLote, Viscosidad, Solidos, ObservacionesAplicación, [Inspección OK/NO OK], RealizadoPor, Fecha, TemperaturaViscosidad, ObservacionesQ FROM TablaInspecciones_ACC 

SET IDENTITY_INSERT TablaInspecciones OFF; 

  

SET IDENTITY_INSERT TablaOperarios ON; 

WITH TablaOperarios_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [TablaOperarios]' 

    ) 

) 

INSERT INTO TablaOperarios([Id] 

      ,[Operario]) 

SELECT Id, Operario FROM TablaOperarios_ACC 

SET IDENTITY_INSERT TablaOperarios OFF; 

  

SET IDENTITY_INSERT TablaPesosEnvases ON; 

WITH TablaPesosEnvases_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [TablaPesosEnvases]' 

    ) 

) 

INSERT INTO TablaPesosEnvases([Id] 

      ,[NUMENVASE] 

      ,[Idproducto] 

      ,[Fecha] 

      --,[FechaCaducidad] 

      ,[PESOTEORICOENTRADA] 

      ,[PESOREALENTRADA] 

      ,[DIFERENCIA] 

      ,[PESOVACIO] 

      ,[PESOENVASE] 

      ,[RealizadoPor] 

      ,[FechaFin] 

      ,[Ubicacion] 

      ,[Lote] 

      ,[StockActual] 

      --,[Observaciones] 

  ) 

SELECT Id, NUMENVASE, Idproducto, Fecha, PESOTEORICOENTRADA, PESOREALENTRADA, DIFERENCIA, PESOVACIO, PESOENVASE, RealizadoPor, FechaFin, Ubicacion, Lote, StockActual FROM TablaPesosEnvases_ACC 

SET IDENTITY_INSERT TablaPesosEnvases OFF; 

  

SET IDENTITY_INSERT TablaProductosNodrizas ON; 

WITH TablaProductosNodrizas_ACC AS 

( 

    SELECT * 

    FROM OPENROWSET( 

        'Microsoft.ACE.OLEDB.12.0',  

        'MS Access;Database=\\dc1\basesdatos\APQ\Tablas_GestionAPQ.accdb',  

        'SELECT * FROM [TablaProductosNodrizas]' 

    ) 

) 

INSERT INTO TablaProductosNodrizas([Id] 

      ,[IdNodriza] 

      ,[Idproducto] 

      ,[Obsoleto] 

      ,[FechaRetirada] 

      ,[Activo] 

  ) 

SELECT Id, IdNodriza, IdProducto, Obsoleto, FechaRetirada, Activo FROM TablaProductosNodrizas_ACC 

SET IDENTITY_INSERT TablaProductosNodrizas OFF; 