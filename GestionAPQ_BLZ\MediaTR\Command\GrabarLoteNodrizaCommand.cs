namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.APQLitalsa.Base;

// Command para crear nuevo lote de nodriza
public record CrearLoteNodrizaCommand(LoteNodrizaDTO Lote) : IRequest<SingleResult<int>>;

public class CrearLoteNodrizaCommandHandler : IRequestHandler<CrearLoteNodrizaCommand, SingleResult<int>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public CrearLoteNodrizaCommandHandler(ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<int>> Handle(CrearLoteNodrizaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        try
        {
            // Validaciones
            var validationErrors = ValidateCrearLoteNodriza(request.Lote);
            if (validationErrors.Any())
            {
                foreach (var error in validationErrors)
                    result.Errors.Add(error);
                return result;
            }

            var loteDb = TinyMapper.Map<LotesNodrizas>(request.Lote);

            // Establecer fechas de creación
            loteDb.Id = 0; // Asegurar que es nuevo
            loteDb.FechaCreacion = DateTime.Now;
            loteDb.FechaModificacion = DateTime.Now;

            await _lotesNodrizasRepo.Add(loteDb, cancellationToken);
            result.Data = loteDb.Id;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private static List<string> ValidateCrearLoteNodriza(LoteNodrizaDTO lote)
    {
        var errors = new List<string>();

        if (lote == null)
        {
            errors.Add("El lote de nodriza no puede ser nulo");
            return errors;
        }

        if (string.IsNullOrWhiteSpace(lote.Lote))
            errors.Add("El lote es obligatorio");
        else if (lote.Lote.Length > 50)
            errors.Add("El lote no puede exceder 50 caracteres");

        if (!lote.IdNodriza.HasValue || lote.IdNodriza.Value <= 0)
            errors.Add("Debe seleccionar una nodriza válida");

        if (lote.IdProducto <= 0)
            errors.Add("Debe seleccionar un producto válido");

        if (!lote.IdOperario.HasValue || lote.IdOperario.Value <= 0)
            errors.Add("Debe seleccionar un operario válido");

        if (string.IsNullOrWhiteSpace(lote.Ubicacion))
            errors.Add("La ubicación es obligatoria");
        else if (lote.Ubicacion.Length > 50)
            errors.Add("La ubicación no puede exceder 50 caracteres");

        if (lote.Id > 0)
            errors.Add("No debe especificar ID para crear un nuevo lote");

        return errors;
    }
}

// Command para actualizar lote de nodriza existente
public record ActualizarLoteNodrizaCommand(LoteNodrizaDTO Lote) : IRequest<SingleResult<int>>;

public class ActualizarLoteNodrizaCommandHandler : IRequestHandler<ActualizarLoteNodrizaCommand, SingleResult<int>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public ActualizarLoteNodrizaCommandHandler(ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<int>> Handle(ActualizarLoteNodrizaCommand request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        try
        {
            // Validaciones
            var validationErrors = ValidateActualizarLoteNodriza(request.Lote);
            if (validationErrors.Any())
            {
                foreach (var error in validationErrors)
                    result.Errors.Add(error);
                return result;
            }

            var loteDb = TinyMapper.Map<LotesNodrizas>(request.Lote);

            // Solo actualizar fecha de modificación
            loteDb.FechaModificacion = DateTime.Now;

            await _lotesNodrizasRepo.Update(loteDb, cancellationToken);
            result.Data = loteDb.Id;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private static List<string> ValidateActualizarLoteNodriza(LoteNodrizaDTO lote)
    {
        var errors = new List<string>();

        if (lote == null)
        {
            errors.Add("El lote de nodriza no puede ser nulo");
            return errors;
        }

        if (lote.Id <= 0)
            errors.Add("El ID es obligatorio para actualizar un lote");

        if (string.IsNullOrWhiteSpace(lote.Lote))
            errors.Add("El lote es obligatorio");
        else if (lote.Lote.Length > 50)
            errors.Add("El lote no puede exceder 50 caracteres");

        if (!lote.IdNodriza.HasValue || lote.IdNodriza.Value <= 0)
            errors.Add("Debe seleccionar una nodriza válida");

        if (lote.IdProducto <= 0)
            errors.Add("Debe seleccionar un producto válido");

        if (!lote.IdOperario.HasValue || lote.IdOperario.Value <= 0)
            errors.Add("Debe seleccionar un operario válido");

        if (string.IsNullOrWhiteSpace(lote.Ubicacion))
            errors.Add("La ubicación es obligatoria");
        else if (lote.Ubicacion.Length > 50)
            errors.Add("La ubicación no puede exceder 50 caracteres");

        return errors;
    }
}