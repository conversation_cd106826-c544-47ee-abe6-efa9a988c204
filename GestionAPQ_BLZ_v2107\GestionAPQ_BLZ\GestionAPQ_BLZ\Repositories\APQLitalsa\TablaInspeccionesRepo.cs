﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class TablaInspeccionesRepo : Repository<TablaInspecciones, APQLitalsaContext>, ITablaInspeccionesRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public TablaInspeccionesRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<TablaInspecciones?> GetInspeccionPorId(int idInspeccion,
        bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(
            false,
            cancellationToken,
            i => i.IdInspecciones == idInspeccion);
    }

    public async Task<List<TablaInspecciones>> GetInspeccionesPasadasProductos(int? idProducto, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        if (idProducto != null)
            return await GetList(asNoTracking, cancellationToken,
                i => i.Idproducto.HasValue && i.Idproducto.Value == idProducto && i.Inspeccion);
        return await GetList(asNoTracking, cancellationToken,
            i => i.Idproducto.HasValue && i.Inspeccion);
    }

    public async Task<TablaInspecciones?> GetInspeccionByProductoLote(int idProducto, string lote,
        bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken,
            i => i.Idproducto == idProducto && i.Idlote.Equals(lote));
    }
}