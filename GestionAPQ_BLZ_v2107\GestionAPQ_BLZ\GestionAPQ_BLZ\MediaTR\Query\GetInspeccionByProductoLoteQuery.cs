namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GetInspeccionByProductoLoteQuery(int IdProducto, string Lote)
    : IRequest<SingleResult<TablaInspeccionesDTO>>;

public class
    GetInspeccionByProductoLoteQueryHandler : IRequestHandler<GetInspeccionByProductoLoteQuery,
    SingleResult<TablaInspeccionesDTO>>
{
    private readonly ITablaInspeccionesRepo _tablaInspeccionesRepo;

    public GetInspeccionByProductoLoteQueryHandler(ITablaInspeccionesRepo tablaInspeccionesRepo)
    {
        _tablaInspeccionesRepo = tablaInspeccionesRepo;
    }

    public async Task<SingleResult<TablaInspeccionesDTO>> Handle(GetInspeccionByProductoLoteQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<TablaInspeccionesDTO>
        {
            Data = null,
            Errors = []
        };

        var inspeccion = await _tablaInspeccionesRepo.GetInspeccionByProductoLote(
            request.IdProducto,
            request.Lote,
            true,
            cancellationToken);

        result.Data = inspeccion != null ? TinyMapper.Map<TablaInspeccionesDTO>(inspeccion) : null;
        return result;
    }
}