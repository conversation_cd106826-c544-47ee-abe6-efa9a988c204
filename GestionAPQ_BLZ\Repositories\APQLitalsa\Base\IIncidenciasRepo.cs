﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa.Base;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface IIncidenciasRepo
{
    Task<List<Incidencias>> GetAll(IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default);

    Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(
        string codigo,
        string lote,
        string ubicacion,
        IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default);

    Task<Incidencias?> GetById(int id, IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default);

    Task Add(Incidencias incidencia, CancellationToken cancellationToken = default);

    Task Update(Incidencias incidencia, CancellationToken cancellationToken = default);

    Task Delete(Incidencias incidencia, CancellationToken cancellationToken = default);
}