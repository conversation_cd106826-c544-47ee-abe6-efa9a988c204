﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files\DevExpress 24.2\Components\Offline Packages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages;C:\Program Files\dotnet\sdk\NuGetFallbackFolder</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="C:\Program Files\dotnet\sdk\NuGetFallbackFolder\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.15\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.15\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.themes\24.2.8\buildTransitive\DevExpress.Blazor.Themes.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.themes\24.2.8\buildTransitive\DevExpress.Blazor.Themes.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.resources\24.2.8\buildTransitive\DevExpress.Blazor.Resources.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.resources\24.2.8\buildTransitive\DevExpress.Blazor.Resources.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.reporting.jsbasedcontrols.common\24.2.8\buildTransitive\DevExpress.Blazor.Reporting.JSBasedControls.Common.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.reporting.jsbasedcontrols.common\24.2.8\buildTransitive\DevExpress.Blazor.Reporting.JSBasedControls.Common.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.reporting.jsbasedcontrols\24.2.8\buildTransitive\DevExpress.Blazor.Reporting.JSBasedControls.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor.reporting.jsbasedcontrols\24.2.8\buildTransitive\DevExpress.Blazor.Reporting.JSBasedControls.props')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor\24.2.8\buildTransitive\net5.0\DevExpress.Blazor.props" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor\24.2.8\buildTransitive\net5.0\DevExpress.Blazor.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_AspNetCore_Components_WebAssembly_Server Condition=" '$(PkgMicrosoft_AspNetCore_Components_WebAssembly_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webassembly.server\8.0.15</PkgMicrosoft_AspNetCore_Components_WebAssembly_Server>
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.data\24.2.8</PkgDevExpress_Data>
    <PkgDevExpress_Xpo Condition=" '$(PkgDevExpress_Xpo)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.xpo\24.2.8</PkgDevExpress_Xpo>
    <PkgDevExpress_DataAccess Condition=" '$(PkgDevExpress_DataAccess)' == '' ">C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.dataaccess\24.2.8</PkgDevExpress_DataAccess>
  </PropertyGroup>
</Project>