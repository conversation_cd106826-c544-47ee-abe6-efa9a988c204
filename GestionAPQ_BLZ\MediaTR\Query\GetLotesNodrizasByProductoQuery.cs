namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetLotesNodrizasByProductoQuery(int IdProducto)
    : IRequest<ListResult<LoteNodrizaDTO>>;

public class GetLotesNodrizasByProductoQueryHandler : IRequestHandler<GetLotesNodrizasByProductoQuery,
    ListResult<LoteNodrizaDTO>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public GetLotesNodrizasByProductoQueryHandler(ILotesNodrizasRepo lotesNodrizasRepo, IASalidaRepo aSalidaRepo)
    {
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<ListResult<LoteNodrizaDTO>> Handle(
        GetLotesNodrizasByProductoQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<LoteNodrizaDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var lotesNodrizas =
                await _lotesNodrizasRepo.GetLotesByProducto(request.IdProducto, new LotesNodrizasQueryOptions
                    {
                        IncludeNodriza = true
                    },
                    cancellationToken);

            result.Data = TinyMapper.Map<List<LoteNodrizaDTO>>(lotesNodrizas);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}