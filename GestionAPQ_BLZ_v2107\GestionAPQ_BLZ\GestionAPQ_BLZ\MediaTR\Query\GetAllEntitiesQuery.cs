namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base;

public record GetAllEntitiesQuery<TEntity, TDto>(bool AsNoTracking = true)
    : IRequest<ListResult<TDto>> where TEntity : class;

public class
    GetAllEntitiesQueryHandler<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery<TEntity, TDto>, ListResult<TDto>>
    where TEntity : class
{
    private readonly IRepository<TEntity> _repository;

    public GetAllEntitiesQueryHandler(IRepository<TEntity> repository)
    {
        _repository = repository;
    }

    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery<TEntity, TDto> request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<TDto>
        {
            Data = [],
            Errors = []
        };

        var entities = await _repository.GetAll(request.AsNoTracking, cancellationToken);
        result.Data = TinyMapper.Map<List<TDto>>(entities);

        return result;
    }
}

//public record GetAllEntitiesQuery_<TEntity, TDto>(Expression<Func<TEntity, bool>> Where, bool AsNoTracking = true)
//    : IRequest<ListResult<TDto>> where TEntity : class;

//public class
//    GetAllEntitiesQueryHandler_<TEntity, TDto> : IRequestHandler<GetAllEntitiesQuery_<TEntity, TDto>, ListResult<TDto>>
//    where TEntity : class
//{
//    private readonly IRepository<TEntity> _repository;

//    public GetAllEntitiesQueryHandler_(IRepository<TEntity> repository)
//    {
//        _repository = repository;
//    }

//    public async Task<ListResult<TDto>> Handle(GetAllEntitiesQuery_<TEntity, TDto> request,
//        CancellationToken cancellationToken)
//    {
//        var result = new ListResult<TDto>
//        {
//            Data = [],
//            Errors = []
//        };

//        var entities = await _repository.GetList(request.AsNoTracking, cancellationToken, request.Where);
//        result.Data = TinyMapper.Map<List<TDto>>(entities);

//        return result;
//    }
//}