﻿@page "/"
@inject IMediator Mediator
@inject ICustomToastService ToastService
@inject ILocalStorageService LocalStorageService
@inject ICustomDialogService DialogService
@inject NavigationManager Navigation

<PageTitle>APQ - Inicio</PageTitle>
<CustomLoadingPanel @bind-Visible="Cargando"/>
<DxDialogProvider/>

<div class="grid-container">
<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded shadow-sm w-100 container">
	<div class="d-flex align-items-center flex-grow-1">
		<h1 class="h5 fw-bold mb-0 text-primary me-2 flex-shrink-0">
			<i class="bi bi-person-gear me-1"></i>
			OPERARIO:
		</h1>
		<div class="flex-grow-1 pe-3">
			<DxComboBox Data="_ddOperarios"
			            NullText="Selecciona un operario..."
			            @bind-Value="@OperarioSelected"
			            @bind-Value:after="@(async () => await HandleUpdateOperarioSeleccionado())"
			            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
			            ValueFieldName="@nameof(TablaOperariosDTO.Id)"
			            TextFieldName="@nameof(TablaOperariosDTO.Operario)"
			            SearchFilterCondition="ListSearchFilterCondition.Contains"
			            SearchMode="ListSearchMode.AutoSearch"
			            CssClass="w-100">
			</DxComboBox>
		</div>
		<div class="flex-shrink-0">
			<DxButton CssClass="btn-outline-primary d-flex align-items-center p-2"
			          title="@TxtModo"
			          Click="@HandleClickCambiarModo">
				<i class="bi bi-arrow-repeat me-2" style="font-size: 19px;"></i> @TxtModo
			</DxButton>
		</div>
	</div>
</div>
<DxFormLayout CssClass="w-100 p-4 mt-3 pb-0">
<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="p-0 m-0" Visible="!EsModoMaquina">
	<div class="border rounded shadow-sm overflow-auto p-3 mb-3 w-100">
		<h1 class="h5 fw-bold mb-0 text-primary me-4">
			<i class="bi bi-archive-fill me-2"></i>
			@(NodrizaSelected is null ? "NODRIZAS DISPONIBLES:" : $"NODRIZA SELECCIONADA: {NodrizaSelected.IdNodriza?.ToString("D2")}")
		</h1>
		<div class="d-grid gap-3 mt-4" style="grid-template-columns: repeat(@(_listaNodrizas?.Count()), 1fr);">
			@* tantas columnas como nodrizas hay *@
			@foreach (var nodriza in _listaNodrizas)
			{
				<div>
					<DxButton CssClass="@($"btn p-0 card h-100 border-0 shadow-sm position-relative {(NodrizaSelected?.IdNodriza == nodriza.IdNodriza ? "bg-info bg-opacity-50 text-white" : nodriza.Activo ? "bg-light" : "bg-light opacity-50")}")"
					          Title="@($"Nodriza {nodriza.IdNodriza?.ToString("D2")}")"
					          RenderStyle="ButtonRenderStyle.None"
					          Enabled="nodriza.Activo"
					          Click="@(async () => await HandleClickCambioNodriza(nodriza))">
						<div class="position-absolute top-0 start-100 translate-middle">
							<span class="badge @(nodriza.Activo ? "bg-success" : "bg-danger") rounded-circle p-1">
								<i class="@(nodriza.Activo ? "bi bi-check" : "bi bi-x")"></i>
							</span>
						</div>
						<div class="card-body p-2 text-center d-flex flex-column justify-content-center h-100">
							<div class="tb-icon-nodriza mx-auto mb-1 @(NodrizaSelected?.IdNodriza == nodriza.IdNodriza ? "opacity-100" : nodriza.Activo ? "opacity-100" : "opacity-50")"></div>
							<div class="fw-bold small @(NodrizaSelected?.IdNodriza == nodriza.IdNodriza ? "text-white" : nodriza.Activo ? "text-primary" : "text-muted")">
								@(nodriza.IdNodriza?.ToString("D2") ?? "00")
							</div>
						</div>
					</DxButton>
				</div>
			}
		</div>
	</div>
</DxFormLayoutGroup>
<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="p-0 m-0 my-3 w-100 w-100" Visible="!EsModoMaquina">
	<DxFormLayoutItem Caption="Producto:" ColSpanMd="6" CaptionCssClass="w-auto" CssClass="p-0 m-0 mt-sm-0" CaptionPosition="CaptionPosition.Horizontal">
		<DxComboBox Data="_ddProductos"
		            SearchFilterCondition="ListSearchFilterCondition.Contains"
		            DropDownWidthMode="DropDownWidthMode.ContentOrEditorWidth"
		            ReadOnly="true"
		            Value="@(_barnizCargadoNodriza)"
		            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
		            EditFormat="{0}, {1}">
			<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Codigo))" Caption="Producto"/>
			<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Descrip))" Caption="Descripción"/>
		</DxComboBox>
	</DxFormLayoutItem>
	<DxFormLayoutItem ColSpanXs="6" ColSpanMd="3" ColSpanSm="6" Caption="Último Lote:" CaptionCssClass="w-auto" CssClass="p-0 m-0 px-md-2 pe-sm-1" CaptionPosition="CaptionPosition.Horizontal">
		<DxTextBox Text="@(_ultimoLoteCargadoNodriza?.Lote ?? null)" ReadOnly="true"/>
	</DxFormLayoutItem>
	<DxFormLayoutItem ColSpanXs="6" ColSpanMd="3" ColSpanSm="6" Caption="Stock (Kg):" CaptionCssClass="w-auto" CssClass="p-0 m-0 ps-sm-1" CaptionPosition="CaptionPosition.Horizontal">
		<DxSpinEdit @bind-Value="@_stockBarnizCargadoNodriza"
		            Mask="F2"
		            ReadOnly="true"
		            ShowSpinButtons="false"
		            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
		            DisplayFormat="{0:N2} Kg">
			<DxNumericMaskProperties Culture="CultureInfo.CurrentCulture"/>
		</DxSpinEdit>
	</DxFormLayoutItem>
</DxFormLayoutGroup>
<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="p-0 m-0 my-3 w-100 w-100" Visible="EsModoMaquina">
	<DxFormLayoutItem Caption="Producto Máquina:" ColSpanMd="6" ColSpanSm="9" CaptionCssClass="w-auto" CssClass="p-0 m-0 mt-sm-0 pe-sm-1" CaptionPosition="CaptionPosition.Horizontal">
		<DxComboBox Data="_ddProductos"
		            NullText="Selecciona un producto..."
		            @bind-Value="@BarnizMaquinaSelected"
		            @bind-Value:after="async () => await HandleUpdateBarnizMaquina(BarnizMaquinaSelected)"
		            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
		            EditFormat="{0}, {1}"
		            SearchFilterCondition="ListSearchFilterCondition.Contains"
		            SearchMode="ListSearchMode.AutoSearch"
		            CssClass="w-100">
			<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Codigo))" Caption="Producto"/>
			<DxListEditorColumn FieldName="@(nameof(AArticuDTO.Descrip))" Caption="Descripción"/>
		</DxComboBox>
	</DxFormLayoutItem>
	<DxFormLayoutItem ColSpanXs="6" ColSpanMd="6" ColSpanSm="3" Caption="Cantidad (Kg):" CaptionCssClass="w-auto" CssClass="p-0 m-0 ps-sm-1" CaptionPosition="CaptionPosition.Horizontal">
		<DxSpinEdit @ref="DxSpinCantidadNecesaria"
		            @bind-Value="@_cantidadNecesariaModoMaquina"
		            NullText="Indica la cantidad necesaria en Kg"
		            ShowSpinButtons="false"
		            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto">
			<DxNumericMaskProperties Culture="CultureInfo.CurrentCulture"/>
		</DxSpinEdit>
	</DxFormLayoutItem>
</DxFormLayoutGroup>
<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None"
                   BeginRow="true"
                   CssClass="p-0 m-0 my-3">
	<DxFormLayoutItem Caption="Lotes para utilización CON inspección pasada:"
	                  CssClass="p-0 m-0"
	                  CaptionCssClass="pt-0"
	                  CaptionPosition="CaptionPosition.Vertical"
	                  ColSpanMd="6">
		<DxGrid @bind-SelectedDataItem="@SelectedDataItemLoteConInspeccion"
		        Data="@ListaLotesConStockConInspeccion"
		        SizeMode="SizeMode.Small"
		        CssClass="m-0 ch-320"
		        PageSize="100"
		        SelectionMode="GridSelectionMode.Single"
		        AllowSelectRowByClick="true"
		        Context="ContextGridLotesConStockConInspeccion"
		        AllowSort="true"
		        ShowFilterRow="true"
		        EditMode="GridEditMode.EditRow"
		        CustomizeEditModel="GridLotesConInspeccion_CustomizeEditModel"
		        EditModelSaving="GridLotesConInspeccion_EditModelSaving">
			<Columns>
				<CustomDxGridCommandColumn CustomFieldName="CommandColumnIncidencia"
				                           TooltipBtnEditar="Editar Incidencia"
				                           TooltipBtnEliminar="Eliminar Incidencia"
				                           TooltipBtnGuardar="Guardar Incidencia"
				                           NewButtonVisible="false"
				                           OnDeleteButtonClicked="async e => await GridLotesConInspeccion_HandleDeleteEvt((DetalleLoteBarnizDTO)e.DataItem)"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Codigo)}")"
				                  Caption="Producto" ReadOnly="true"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Lote)}")"
				                  Caption="Lote" Width="100px" ReadOnly="true" SortIndex="2" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Fechalote)}")"
				                  Caption="Fecha Lote" Width="100px" SortIndex="0" SortOrder="GridColumnSortOrder.Ascending" ReadOnly="true"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Stock)}")"
				                  Caption="Stock" TextAlignment="GridTextAlignment.Center" Width="60px" ReadOnly="false"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Ubicacion)}")"
				                  Caption="Ubicación" TextAlignment="GridTextAlignment.Center" Width="60px" ReadOnly="false"
				                  SortIndex="1" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Incidencia)}.{nameof(DetalleLoteBarnizDTO.Incidencia.Incidencia)}")"
				                  Caption="Incidencia" Width="400px"/>
			</Columns>
			<TotalSummary>
				<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}"/>
			</TotalSummary>
		</DxGrid>
	</DxFormLayoutItem>
	<DxFormLayoutItem ColSpanMd="1" CssClass="m-0 p-0 my-md-2 my-sm-2">
		<div class="d-flex flex-column justify-content-center height-responsive-botonera m-md-2">
			<DxButton RenderStyle="ButtonRenderStyle.Success"
			          CssClass="w-100 p-2 mb-2"
			          SizeMode="SizeMode.Large"
			          Text="Buscar Lote a Cargar"
			          Click="async () => await HandleClickBuscarLote()"/>
			<DxButton RenderStyle="ButtonRenderStyle.Success"
			          CssClass="w-100 p-2"
			          SizeMode="SizeMode.Large"
			          Text="Cargar Lote"
			          Click="async () => await HandleClickCargarLote()"/>
		</div>
	</DxFormLayoutItem>
	<DxFormLayoutItem Caption=@($"Últimos lotes introducidos en las {(EsModoMaquina ? "Máquinas" : "Nodrizas")}:")
	                  CssClass="p-0 m-0"
	                  CaptionCssClass="pt-0"
	                  CaptionPosition="CaptionPosition.Vertical"
	                  ColSpanMd="5">
		<DxGrid Data="ListaLotesNodriza"
		        SizeMode="SizeMode.Small"
		        CssClass="m-0 ch-320"
		        PageSize="100"
		        Context="ContextGridUltimosLotes"
		        AllowSort="true"
		        ShowFilterRow="true">
			<Columns>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Fecha))"
				                  Width="100px"
				                  SortIndex="0"
				                  SortOrder="GridColumnSortOrder.Descending"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Hora))"
				                  SortIndex="1"
				                  SortOrder="GridColumnSortOrder.Descending"
				                  TextAlignment="GridTextAlignment.Center"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Nodriza))" TextAlignment="GridTextAlignment.Center"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Idproducto))" Caption="Producto" DisplayFormat="F0"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Lote))"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Operario))" Width="200px"/>
				<DxGridDataColumn FieldName="@(nameof(LotesNodrizasDTO.Ubicacion))"
				                  Caption="Ubicación"
				                  TextAlignment="GridTextAlignment.Center"/>
			</Columns>
			<TotalSummary>
				<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}" FooterColumnName="@nameof(LotesNodrizasDTO.Fecha)"/>
			</TotalSummary>
		</DxGrid>
	</DxFormLayoutItem>
</DxFormLayoutGroup>
<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None"
                   BeginRow="true"
                   CssClass="p-0 m-0 my-3">
	<DxFormLayoutItem Caption="Lotes para utilización SIN inspección pasada:"
	                  ColSpanMd="6"
	                  CssClass="p-0 m-0"
	                  CaptionCssClass="pt-0"
	                  CaptionPosition="CaptionPosition.Vertical">
		<DxGrid Data="@ListaLotesConStockSinInspeccion"
		        SizeMode="SizeMode.Small"
		        PageSize="100"
		        Context="ContextGridLotesConStockSinInspeccion"
		        CssClass="m-0 ch-320"
		        AllowSort="true"
		        ShowFilterRow="true">
			<Columns>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Codigo)}")"
				                  Caption="Producto" Width="100px"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Lote)}")"
				                  Caption="Lote" SortIndex="2" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Fechalote)}")"
				                  Caption="Fecha Lote" SortIndex="0" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Stock)}")"
				                  Caption="Stock" TextAlignment="GridTextAlignment.Center"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Ubicacion)}")"
				                  Caption="Ubicación" TextAlignment="GridTextAlignment.Center" SortIndex="1" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@($"{nameof(DetalleLoteBarnizDTO.PrimeraEntrada)}.{nameof(DetalleLoteBarnizDTO.PrimeraEntrada.Fecent)}")"
				                  DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
				                  Caption="Primera Entrada" TextAlignment="GridTextAlignment.Center"/>
			</Columns>
			<TotalSummary>
				<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}" FooterColumnName="@($"{nameof(DetalleLoteBarnizDTO.Lote)}.{nameof(DetalleLoteBarnizDTO.Lote.Codigo)}")"/>
			</TotalSummary>
		</DxGrid>
	</DxFormLayoutItem>
	<DxFormLayoutItem ColSpanMd="1" CssClass="m-0 p-0 my-md-2 my-sm-2">
		<div class="d-flex flex-column justify-content-center height-responsive-botonera m-md-2">
			<DxButton RenderStyle="ButtonRenderStyle.Success"
			          CssClass="w-100 p-2 mb-2"
			          SizeMode="SizeMode.Large"
			          Text="Actualizar"
			          Click="async () => await HandleClickActualizar()"/>
			<DxButton RenderStyle="ButtonRenderStyle.Success"
			          CssClass="w-100 p-2"
			          SizeMode="SizeMode.Large"
			          Text="Estado Inspecciones"
			          @onclick="() => HandleClickEstadoInspecciones()"/>
		</div>
	</DxFormLayoutItem>
	<DxFormLayoutItem Caption="Todos los lotes (NODRIZAS + MÁQUINA):"
	                  CssClass="p-0 m-0"
	                  CaptionCssClass="pt-0"
	                  CaptionPosition="CaptionPosition.Vertical"
	                  ColSpanMd="5">
		<DxGrid Data="@ListaSalidas"
		        SizeMode="SizeMode.Small"
		        CssClass="m-0 ch-320"
		        PageSize="100"
		        Context="ContextGridTodosLotes"
		        AllowSort="true"
		        ShowFilterRow="true">
			<Columns>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Fecsal)" Caption="Fecha" SortIndex="0" SortOrder="GridColumnSortOrder.Descending"/>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Codigo)" Caption="Producto"/>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Lote)" SortIndex="2" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Cantidad)" TextAlignment="GridTextAlignment.Center"/>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Ubicacion)" Caption="Ubicación" TextAlignment="GridTextAlignment.Center" SortIndex="1" SortOrder="GridColumnSortOrder.Ascending"/>
				<DxGridDataColumn FieldName="@nameof(ASalidaDTO.Descri6)" Caption="Observaciones" Width="200px"/>
			</Columns>
			<TotalSummary>
				<DxGridSummaryItem SummaryType="GridSummaryItemType.Count" DisplayText="Total: {0}" FooterColumnName="@nameof(ASalidaDTO.Fecsal)"/>
			</TotalSummary>
		</DxGrid>
	</DxFormLayoutItem>
</DxFormLayoutGroup>
</DxFormLayout>
</div>

@code {

	// ============= VARIABLES Y PROPIEDADES COMUNES =============
	private List<TablaOperariosDTO> _ddOperarios = [];
	private List<AArticuDTO> _ddProductos = [];
	private bool Cargando { get; set; }
	private TablaOperariosDTO? OperarioSelected { get; set; }

	// ============= VARIABLES Y PROPIEDADES MODO NODRIZA =============
	private List<TablaProductosNodrizasDTO> _listaNodrizas = [];
	private TablaProductosNodrizasDTO? NodrizaSelected { get; set; }
	private AArticuDTO? _barnizCargadoNodriza;
	private LotesNodrizasDTO? _ultimoLoteCargadoNodriza;
	private double? _stockBarnizCargadoNodriza;
	private List<DetalleLoteBarnizDTO> _listaLotesConInspeccionModoNodriza = [];
	private List<DetalleLoteBarnizDTO> _listaLotesSinInspeccionModoNodriza = [];
	private List<LotesNodrizasDTO> _listaLotesNodrizaModoNodriza = [];
	private List<ASalidaDTO> _listaSalidasModoNodriza = [];
	private object? _selectedDataItemLoteConInspeccionModoNodriza;

	// ============= VARIABLES Y PROPIEDADES MODO MÁQUINA =============
	private DxSpinEdit<int?> DxSpinCantidadNecesaria { get; set; }
	private AArticuDTO? BarnizMaquinaSelected { get; set; }
	private int? _cantidadNecesariaModoMaquina;
	private List<DetalleLoteBarnizDTO> _listaLotesConStockConInspeccionModoMaquina = [];
	private List<DetalleLoteBarnizDTO> _listaLotesConStockSinInspeccionModoMaquina = [];
	private List<LotesNodrizasDTO> _listaLotesNodrizaModoMaquina = [];
	private List<ASalidaDTO> _listaSalidasModoMaquina = [];
	private object? _selectedDataItemLoteConInspeccionModoMaquina;

	// ============= PROPIEDADES CALCULADAS =============
	private bool EsModoMaquina { get; set; }
	private string TxtModo => $"Pasar a {(EsModoMaquina ? "Nodrizas" : "Máquinas")}";

	private int? IdProductoActual => EsModoMaquina
		? BarnizMaquinaSelected?.Codigo != null ? Convert.ToInt32(BarnizMaquinaSelected.Codigo) : null
		: NodrizaSelected?.Idproducto;

	private bool TieneOperarioSeleccionado => OperarioSelected != null;
	private bool TieneProductoSeleccionado => IdProductoActual.HasValue;

	private object? SelectedDataItemLoteConInspeccion
	{
		get =>
			EsModoMaquina
				? _selectedDataItemLoteConInspeccionModoMaquina
				: _selectedDataItemLoteConInspeccionModoNodriza;
		set
		{
			if (EsModoMaquina)
				_selectedDataItemLoteConInspeccionModoMaquina = value;
			else
				_selectedDataItemLoteConInspeccionModoNodriza = value;
		}
	}

	private List<DetalleLoteBarnizDTO> ListaLotesConStockConInspeccion => EsModoMaquina
		? _listaLotesConStockConInspeccionModoMaquina
		: _listaLotesConInspeccionModoNodriza;

	private List<DetalleLoteBarnizDTO> ListaLotesConStockSinInspeccion => EsModoMaquina
		? _listaLotesConStockSinInspeccionModoMaquina
		: _listaLotesSinInspeccionModoNodriza;

	private List<LotesNodrizasDTO> ListaLotesNodriza => EsModoMaquina
		? _listaLotesNodrizaModoMaquina
		: _listaLotesNodrizaModoNodriza;

	private List<ASalidaDTO> ListaSalidas => EsModoMaquina
		? _listaSalidasModoMaquina
		: _listaSalidasModoNodriza;

	// ============= MÉTODOS PRINCIPALES =============
	protected override async Task OnInitializedAsync()
	{
		await EjecutarConCarga(async () =>
		{
			await CargarDatosInicio();
			await ResetValoresPorDefecto(true);
		});
	}

	private async Task CargarDatosInicio()
	{
		// ejecutamos querys
		var resultOperarios = await Mediator.Send(new GetAllEntitiesQuery<TablaOperarios, TablaOperariosDTO>());
		var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(null, false));
		var resultProdsEnNodrizas = await Mediator.Send(new GetAllEntitiesQuery<TablaProductosNodrizas, TablaProductosNodrizasDTO>());

		// comprobamos si hay errores y mostramos dado el caso
		var listaErrores = resultOperarios.Errors.Concat(resultProductos.Errors).Concat(resultProdsEnNodrizas.Errors).ToArray();
		if (listaErrores.Any())
		{
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// asignamos valor a variables
		_ddOperarios = resultOperarios.Data;
		_ddProductos = resultProductos.Data.Select(i => i.Articulo).OrderBy(i => i.Codigo).ToList();
		_listaNodrizas = resultProdsEnNodrizas.Data.OrderBy(i => i.IdNodriza).ToList();
	}

	private async Task CargarDatosPostSeleccion()
	{
		if (!TieneProductoSeleccionado) return;

		if (!EsModoMaquina)
			await CargarDatos_DetallesNodriza();
		await CargarDatos_DetalleLotesInspeccion();
		await CargarDatos_DetalleLotesArticulo();
	}

	private async Task CargarDatos_DetallesNodriza()
	{
		if (NodrizaSelected?.IdNodriza == null)
			return;

		var numNodriza = NodrizaSelected.IdNodriza.Value;

		// ejecutamos query y verificamos errores
		var result = await Mediator.Send(new GetDetalleProductosNodrizaQuery(numNodriza));
		if (result.Errors.Any())
		{
			ToastService.MostrarError(result.Errors.First());
			return;
		}

		// almacenamos en variables la resp
		if (result.Data?.TablaProductoNodriza?.Idproducto == null)
		{
			ToastService.MostrarError($"No se ha encontrado el barniz de la nodriza {numNodriza}.");
			return;
		}

		var detallesTablaProdNodriza = result.Data;
		var tablaProdNodriza = detallesTablaProdNodriza.TablaProductoNodriza;
		var ultimoLoteNodriza = detallesTablaProdNodriza.UltimoLote;

		// verificamos que el producto de la nodriza se encuentra en nuestra lista de productos
		var barniz = _ddProductos.FirstOrDefault(o => o.Codigo.Equals(tablaProdNodriza.Idproducto.ToString()));
		if (barniz == null || string.IsNullOrEmpty(barniz.Codigo))
		{
			ToastService.MostrarError($"No se ha encontrado el  código {tablaProdNodriza.Idproducto} entre el listado de productos.");
			return;
		}

		// actualizamos variables de ui
		_barnizCargadoNodriza = barniz;
		_ultimoLoteCargadoNodriza = ultimoLoteNodriza;
		_stockBarnizCargadoNodriza = _ddProductos.Where(o => o.Codigo.Equals(tablaProdNodriza.Idproducto.ToString())).Sum(o => o.Stock) ?? 0;
	}

	private async Task CargarDatos_DetalleLotesInspeccion()
	{
		var result = await Mediator.Send(new GetDetallesLotesQuery(IdProductoActual.Value.ToString(), false));
		if (result.Errors.Any())
		{
			ToastService.MostrarError(result.Errors.First());
			return;
		}

		// como es una consulta que se usa también en otras vistas, devuelve una lista artículos con sus lotes
		// en este caso, como pasamos elproducto por parámetro, sólo nos devuelve una lista
		// seleccinoamos los lotes así, que debería ser lo mismo que pillar el primer elemento de la listaLotesPorArticulo
		var listaLotesPorArticulo = result.Data.Select(i => i.ListaDetallesLotes).ToList();
		var listaLotesConStock = listaLotesPorArticulo
			.Where(i => i != null && i.Any())
			.SelectMany(i => i)
			.ToList()
			.Where(i => i.Lote.Stock.HasValue && i.Lote.Stock > 0);
		var lotesConStockConInspeccion = listaLotesConStock.Where(i => i.Inspeccion != null);
		var lotesConStockSinInspeccion = listaLotesConStock.Where(i => i.Inspeccion == null);

		if (EsModoMaquina)
		{
			_listaLotesConStockConInspeccionModoMaquina = lotesConStockConInspeccion.ToList();
			_listaLotesConStockSinInspeccionModoMaquina = lotesConStockSinInspeccion.ToList();
		}
		else
		{
			_listaLotesConInspeccionModoNodriza = lotesConStockConInspeccion.ToList();
			_listaLotesSinInspeccionModoNodriza = lotesConStockSinInspeccion.ToList();
		}
	}

	private async Task CargarDatos_DetalleLotesArticulo()
	{
		var result = await Mediator.Send(new GetLotesNodrizasySalidasProductoQuery(IdProductoActual.Value));
		if (result.Errors.Any())
		{
			ToastService.MostrarError(result.Errors.First());
			return;
		}

		if (EsModoMaquina)
		{
			_listaLotesNodrizaModoMaquina = result.Data.ListaLotesNodrizas;
			_listaSalidasModoMaquina = result.Data.ListaSalidas;
		}
		else
		{
			_listaLotesNodrizaModoNodriza = result.Data.ListaLotesNodrizas;
			_listaSalidasModoNodriza = result.Data.ListaSalidas;
		}
	}

	private async Task ResetValoresPorDefecto(bool resetVariablesModo)
	{
		// variables globales de la página
		var ultOperarioSelected = await LocalStorageService.GetItemAsync<TablaOperariosDTO>(EnumKeysLocalStorage.Operario);
		if (ultOperarioSelected is not null && _ddOperarios.Any())
			ultOperarioSelected = _ddOperarios.FirstOrDefault(i => i.Id == ultOperarioSelected.Id && i.Operario == ultOperarioSelected.Operario);
		OperarioSelected = ultOperarioSelected;

		// reset de todas las variables específicas para modo nodriza y modo máquina
		if (resetVariablesModo)
		{
			if (EsModoMaquina)
				ResetVariablesModoMaquina();
			else
				ResetVariablesModoNodriza();
		}
		else
		{
			if (EsModoMaquina)
				await HandleUpdateBarnizMaquina(BarnizMaquinaSelected);
			else
				await HandleClickCambioNodriza(NodrizaSelected);
		}
	}

	private void ResetVariablesModoNodriza()
	{
		NodrizaSelected = null;
		_selectedDataItemLoteConInspeccionModoNodriza = null;
		_barnizCargadoNodriza = null;
		_ultimoLoteCargadoNodriza = null;
		_stockBarnizCargadoNodriza = null;
		_listaLotesConInspeccionModoNodriza = [];
		_listaLotesSinInspeccionModoNodriza = [];
		_listaLotesNodrizaModoNodriza = [];
		_listaSalidasModoNodriza = [];
	}

	private void ResetVariablesModoMaquina()
	{
		BarnizMaquinaSelected = null;
		_selectedDataItemLoteConInspeccionModoMaquina = null;
		_cantidadNecesariaModoMaquina = null;
		_listaLotesConStockConInspeccionModoMaquina = [];
		_listaLotesConStockSinInspeccionModoMaquina = [];
		_listaLotesNodrizaModoMaquina = [];
		_listaSalidasModoMaquina = [];
	}

	private void ResetListasLotesInspeccion()
	{
		if (EsModoMaquina)
		{
			_listaLotesConStockConInspeccionModoMaquina = [];
			_listaLotesConStockSinInspeccionModoMaquina = [];
			_selectedDataItemLoteConInspeccionModoMaquina = null;
		}
		else
		{
			_listaLotesConInspeccionModoNodriza = [];
			_listaLotesSinInspeccionModoNodriza = [];
			_selectedDataItemLoteConInspeccionModoNodriza = null;
		}
	}

	private void ResetDatosPostCargaLote()
	{
		if (EsModoMaquina)
		{
			var barnizSelected = BarnizMaquinaSelected;
			ResetVariablesModoMaquina();
			BarnizMaquinaSelected = barnizSelected;
		}
		else
		{
			var nodrizaSelected = NodrizaSelected;
			ResetVariablesModoNodriza();
			NodrizaSelected = nodrizaSelected;
		}
	}

	private async Task HandleClickCambiarModo()
	{
		EsModoMaquina = !EsModoMaquina;
		await ResetValoresPorDefecto(false);
	}

	private async Task HandleUpdateOperarioSeleccionado()
	{
		if (OperarioSelected is not null)
			await LocalStorageService.SetItemAsync(EnumKeysLocalStorage.Operario, OperarioSelected);
		else
			await LocalStorageService.RemoveItemAsync(EnumKeysLocalStorage.Operario);
	}

	private async Task HandleClickCambioNodriza(TablaProductosNodrizasDTO? nuevaNodrizaSelected)
	{
		await ResetValoresPorDefecto(true);
		NodrizaSelected = nuevaNodrizaSelected;
		await EjecutarConCarga(async () => await CargarDatosPostSeleccion());
	}

	private async Task HandleUpdateBarnizMaquina(AArticuDTO? nuevoBarnizMaquinaSelected)
	{
		await ResetValoresPorDefecto(true);
		BarnizMaquinaSelected = nuevoBarnizMaquinaSelected;

		if (TieneProductoSeleccionado)
			await EjecutarConCarga(async () => await CargarDatosPostSeleccion());
	}

	private async Task HandleClickActualizar()
	{
		if (!TieneProductoSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto." : "una nodriza con un producto cargado."));
			return;
		}

		if (EsModoMaquina)
			await HandleUpdateBarnizMaquina(BarnizMaquinaSelected);
		else
			await HandleClickCambioNodriza(NodrizaSelected);
	}

	private async Task HandleClickBuscarLote()
	{
		if (!TieneOperarioSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar un operario.");
			return;
		}

		if (!TieneProductoSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto" : "una nodriza con un producto cargado"));
			return;
		}

		if (EsModoMaquina && _cantidadNecesariaModoMaquina is null or <= 0)
		{
			ToastService.MostrarError("Debes indicar una cantidad en positivo para llevar a máquina.");
			if (EsModoMaquina && _cantidadNecesariaModoMaquina == null)
			{
				_cantidadNecesariaModoMaquina = null;
				await InvokeAsync(() => StateHasChanged());
				await DxSpinCantidadNecesaria.FocusAsync();
			}

			return;
		}

		await EjecutarConCarga(async () =>
		{
			if (!EsModoMaquina)
			{
				// ejecutamos query para obtener siguiente lote hipotético a cargar
				var result = await Mediator.Send(new GetSiguienteLoteNodrizaQuery(NodrizaSelected.IdNodriza.Value));
				var detalleLote = result.Data;
				var lote = detalleLote?.Lote;

				if (result.Errors.Any())
				{
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoNodriza = new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoNodriza = null;
					ToastService.MostrarError(result.Errors.First());
				}
				else if (lote == null)
				{
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoNodriza = new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoNodriza = null;
					ToastService.MostrarError("No se ha podido encontrar el siguiente lote a cargar.");
				}
				else
				{
					// ya con el lote, marcamos la línea correspondiente en el grid y sacamos lote y ubicación
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoNodriza = _listaLotesConInspeccionModoNodriza
						.FirstOrDefault(i => i.Lote.Codigo.Equals(lote.Codigo) && i.Lote.Lote.Equals(lote.Lote) && i.Lote.Ubicacion.Equals(lote.Ubicacion)) ?? new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoNodriza ??= null;
					// como está embebida en EjecutarConCarga y el dialogservice no termina hasta que se pulsa un botón, la ejecutamos como una task sin esperarla
					Task.Run(async () => await InvokeAsync(async () => { await DialogService.MostrarInfo("Resultado", $"LOTE: {lote.Lote} / UBICACIÓN: {lote.Ubicacion}", "Ok"); }));
				}
			}
			else
			{
				// lanzamos query para modo máquina
				var result = await Mediator.Send(new GetSiguientesLotesMaquinaQuery(_cantidadNecesariaModoMaquina.Value, Convert.ToInt32(BarnizMaquinaSelected.Codigo)));
				var listaSiguientesDetallesLote = result.Data;

				if (result.Errors.Any())
				{
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoMaquina = new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoMaquina = null;
					ToastService.MostrarError(result.Errors.First());
				}
				else if (listaSiguientesDetallesLote == null || listaSiguientesDetallesLote.Count == 0)
				{
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoNodriza = new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoNodriza = null;
					ToastService.MostrarError("No se han podido encontrar los siguientes lotes a cargar.");
				}
				else
				{
					var listaSiguientesLotes = result.Data;
					// hay que hacer el truco con el selecteddataitem porque pilla bien la instrucción sino
					_selectedDataItemLoteConInspeccionModoMaquina = new object();
					await InvokeAsync(() => StateHasChanged());
					_selectedDataItemLoteConInspeccionModoMaquina = null;
					var listaLotesString = listaSiguientesLotes.Select(i => $"LOTE: {i.Lote.Lote} / UBICACIÓN: {i.Lote.Ubicacion} / CANTIDAD: {i.Lote.Stock}Kg / STOCK PENDIENTE: {i.CantidadPendienteLote} KG").ToList();
					// como está embebida en EjecutarConCarga y el dialogservice no termina hasta que se pulsa un botón, la ejecutamos como una task sin esperarla
					Task.Run(async () => await InvokeAsync(async () => { await DialogService.MostrarInfo("Resultado", string.Join("\r\n", listaLotesString), "Ok", "700"); }));
				}
			}
		});
	}

	private async Task HandleClickCargarLote()
	{
		// comprobaciones básicas
		if (!TieneOperarioSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar un operario.");
			return;
		}

		if (!TieneProductoSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto" : "una nodriza con un producto cargado"));
			return;
		}

		var loteSeleccionado = ((DetalleLoteBarnizDTO?)SelectedDataItemLoteConInspeccion)?.Lote;
		if (loteSeleccionado == null)
		{
			ToastService.MostrarError("Debes seleccionar un lote previamente.");
			return;
		}

		// comprobaciones sobre la fecha de caducidad del barniz
		if (loteSeleccionado.Fechalote <= DateTime.Now)
		{
			var grabar = await DialogService.MostrarConfirmacionEstiloWarning(
				"Cargar Lote - Chequeo de fecha",
				$"El barniz {loteSeleccionado.Codigo}, lote {loteSeleccionado.Lote} está caducado.\r\nConsulta con Calidad.", "Grabar", "Cancelar");
			if (!grabar)
				return;
		}

		// si hemos llegado aquí, podemos cargar el lote
		var numNodriza = EsModoMaquina ? 0 : NodrizaSelected.IdNodriza.Value;
		await EjecutarConCarga(async () =>
		{
			var loteBarniz = new LotesNodrizasDTO
			{
				Fecha = DateTime.Now,
				Hora = DateTime.Now.TimeOfDay,
				Nodriza = numNodriza,
				Idproducto = int.Parse(loteSeleccionado.Codigo),
				Lote = loteSeleccionado.Lote,
				Operario = OperarioSelected?.Operario ?? "N/A",
				Ubicacion = loteSeleccionado.Ubicacion
			};
			var result = await Mediator.Send(new GrabarLotesNodrizasCommand(loteBarniz));
			if (result.Errors.Any())
			{
				ToastService.MostrarError($"{result.Errors.First()}");
			}
			else
			{
				ResetDatosPostCargaLote();
				ToastService.MostrarOk($"Lote {loteBarniz.Lote} registrado. Recuerda dar consumo en el ERP a este material");
				await CargarDatosPostSeleccion();
			}
		});
	}

	private void HandleClickEstadoInspecciones()
	{
		if (!TieneProductoSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar " + (EsModoMaquina ? "un producto." : "una nodriza con un producto cargado."));
			return;
		}

		Navigation.NavigateTo($"EstadoInspecciones/{IdProductoActual}");
	}

	private void GridLotesConInspeccion_CustomizeEditModel(GridCustomizeEditModelEventArgs e)
	{
		if (!TieneOperarioSeleccionado)
		{
			ToastService.MostrarError("Tienes que seleccionar un operario.");
			return;
		}

		var detalleALoteOriginalDto = (DetalleLoteBarnizDTO)e.DataItem;
		var detalleALoteEditDto = (DetalleLoteBarnizDTO)e.EditModel;
		detalleALoteOriginalDto.Incidencia ??= new IncidenciaDTO(); // inicializamos la incidencia para que nos deje editarla, si esta fuera null.
		if (detalleALoteOriginalDto.Incidencia.Id != null)
		{
			detalleALoteEditDto.Incidencia = new IncidenciaDTO
			{
				Id = detalleALoteOriginalDto.Incidencia.Id,
				Incidencia = detalleALoteOriginalDto.Incidencia.Incidencia
			};
		}
		else
		{
			detalleALoteEditDto.Incidencia = new IncidenciaDTO
			{
				Id = detalleALoteOriginalDto.Incidencia.Id,
				Incidencia = detalleALoteOriginalDto.Incidencia.Incidencia,
				CreadoPor = detalleALoteOriginalDto.Incidencia.CreadoPor,
				Codigo = detalleALoteOriginalDto.Lote.Codigo,
				Lote = detalleALoteOriginalDto.Lote.Lote,
				Ubicacion = detalleALoteOriginalDto.Lote.Ubicacion
			};
		}
	}

	private async Task GridLotesConInspeccion_EditModelSaving(GridEditModelSavingEventArgs e)
	{
		var detalleALoteOriginalDto = (DetalleLoteBarnizDTO)e.DataItem;
		var detalleALoteEditDto = (DetalleLoteBarnizDTO)e.EditModel;
		var incidenciaOriginalDto = detalleALoteOriginalDto.Incidencia;
		var incidenciaEditDto = detalleALoteEditDto.Incidencia;
		var incidenciaOriginalStr = incidenciaOriginalDto?.Incidencia?.Trim() ?? string.Empty;
		var incidenciaEditStr = incidenciaEditDto?.Incidencia?.Trim() ?? string.Empty;

		// comprobamos si hay cambios en la incidencia
		if (string.IsNullOrEmpty(incidenciaOriginalStr) && string.IsNullOrEmpty(incidenciaEditStr))
		{
			ToastService.MostrarWarning("Para registrar una incidencia, no puedes dejarla vacía.");
			return;
		}

		if (incidenciaOriginalStr.Equals(incidenciaEditStr))
		{
			ToastService.MostrarWarning("No ha habido cambios en la incidencia, por tanto se mantiene igual.");
			return;
		}

		// comprobamos si hay que eliminar de bbdd
		if (string.IsNullOrEmpty(incidenciaEditStr))
		{
			await GridLotesConInspeccion_HandleDeleteEvt(detalleALoteOriginalDto);
			return;
		}

		// añadimos / actualizamos la incidencia
		await EjecutarConCarga(async () =>
		{
			if (!incidenciaEditDto.Id.HasValue)
				incidenciaEditDto.CreadoPor = OperarioSelected?.Operario;
			var result = await Mediator.Send(new GrabarIncidenciaCommand(incidenciaEditDto));
			if (result.Errors.Any())
			{
				ToastService.MostrarError(result.Errors.First());
				e.Cancel = true;
				return;
			}

			ResetListasLotesInspeccion();
			await CargarDatos_DetalleLotesInspeccion();
			ToastService.MostrarOk($"Incidencia {(string.IsNullOrEmpty(incidenciaOriginalStr) ? "añadida" : "editada")} con éxito.");
		});
	}

	private async Task GridLotesConInspeccion_HandleDeleteEvt(DetalleLoteBarnizDTO detalleALote)
	{
		var incidenciaDto = detalleALote.Incidencia;
		var idIncidencia = incidenciaDto?.Id;

		// comprobaciones básicas
		if (idIncidencia == null)
		{
			ToastService.MostrarWarning("No puedes eliminar una incidencia que todavía no ha sido registrada en el sistema.");
			return;
		}

		var resp = await DialogService.MostrarConfirmacionEstiloDanger("Atención",
			@"¿Seguro que deseas eliminar esta incidencia?",
			"Sí",
			"No");
		if (!resp)
			return;

		// empezamos mediatr
		await EjecutarConCarga(async () =>
		{
			var result = await Mediator.Send(new DeleteIncidenciaCommand(idIncidencia.Value));
			if (result.Errors.Any())
				ToastService.MostrarError(result.Errors.First());
			else
			{
				ResetListasLotesInspeccion();
				await CargarDatos_DetalleLotesInspeccion();
				ToastService.MostrarOk("Incidencia eliminada con éxito.");
			}
		});
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}

}