﻿namespace GestionAPQ_BLZ.Services;

using Base;
using DevExpress.Blazor;
using DevExpress.Blazor.Internal;

public class CustomDialogService : DialogService, ICustomDialogService
{
    public CustomDialogService(IDialogManageService dialogManageService) : base(dialogManageService)
    {
    }

    public async Task<bool> MostrarConfirmacionEstiloSuccess(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null)
    {
        return await Confirmar(titulo, mensaje, MessageBoxRenderStyle.Success, txtBtnOk, txtBtnCancel, width);
    }

    public async Task<bool> MostrarConfirmacionEstiloDanger(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null)
    {
        return await Confirmar(titulo, mensaje, MessageBoxRenderStyle.Danger, txtBtnOk, txtBtnCancel, width);
    }

    public async Task<bool> MostrarConfirmacionEstiloWarning(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null)
    {
        return await Confirmar(titulo, mensaje, MessageBoxRenderStyle.Warning, txtBtnOk, txtBtnCancel, width);
    }

    public async Task MostrarInfo(string titulo, string mensaje, string txtBtnOk, string? width = null)
    {
        await Alerta(titulo, mensaje, MessageBoxRenderStyle.Info, txtBtnOk, width);
    }

    public async Task MostrarError(string titulo, string mensaje, string txtBtnOk, string? width = null)
    {
        await Alerta(titulo, mensaje, MessageBoxRenderStyle.Danger, txtBtnOk, width);
    }

    public async Task MostrarWarning(string titulo, string mensaje, string txtBtnOk, string? width = null)
    {
        await Alerta(titulo, mensaje, MessageBoxRenderStyle.Warning, txtBtnOk, width);
    }

    private async Task<bool> Confirmar(string titulo, string mensaje, MessageBoxRenderStyle estiloMensaje,
        string txtBtnOk,
        string txtBtnCancel, string? width)
    {
        var result = await ConfirmAsync(new MessageBoxOptions
        {
            Title = titulo,
            Text = mensaje,
            RenderStyle = estiloMensaje,
            OkButtonText = txtBtnOk,
            CancelButtonText = txtBtnCancel,
            Width = width,
            CssClass = "my-messagebox"
        });

        return result;
    }

    private async Task Alerta(string titulo, string mensaje, MessageBoxRenderStyle estiloMensaje, string txtBtnOk,
        string? width)
    {
        await AlertAsync(new MessageBoxOptions
        {
            Title = titulo,
            Text = mensaje,
            RenderStyle = estiloMensaje,
            OkButtonText = txtBtnOk,
            Width = width,
            CssClass = "my-messagebox"
        });
    }
}