﻿namespace GestionAPQ_BLZ.Repositories.Dato01Lita.Base;

using System.Linq.Expressions;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public interface IAArticuRepo
{
    IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, QueryOptions options);

    Task<List<AArticu>> GetBarnices(string? idBarniz, QueryOptions options,
        CancellationToken cancellationToken = default);

    Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        IAViscosRepo aViscosRepo,
        QueryOptions options,
        CancellationToken cancellationToken = default);

    public Task<List<(AArticu, ALotess)>> GetBarnicesInnerJoinLotesConStock(
        string? idBarniz,
        IALotessRepo aLotessRepo,
        QueryOptions options,
        CancellationToken cancellationToken);

    public Task<List<(AArticu, ALotess, AEntdia)>> GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
        string? idBarniz,
        IALotessRepo aLotessRepo,
        IAEntdiaRepo aEntdiaRepo,
        QueryOptions options,
        Expression<Func<(AArticu, ALotess, AEntdia), object>>? groupBy,
        CancellationToken cancellationToken);
}