namespace GestionAPQ_BLZ.Repositories;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class QueryOptions
{
    public bool AsNoTracking { get; set; } = true;

    public virtual IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        return AsNoTracking ? query.AsNoTracking() : query;
    }
}

public class LotesNodrizasQueryOptions : QueryOptions
{
    public bool IncludeNodriza { get; set; } = false;
    public bool IncludeOperario { get; set; } = false;

    public override IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        var result = base.SetOptions(query);

        if (typeof(T) == typeof(LotesNodrizas))
        {
            var lotesQuery = result as IQueryable<LotesNodrizas>;

            if (IncludeNodriza)
                lotesQuery = lotesQuery.Include(l => l.IdNodrizaNavigation);

            if (IncludeOperario)
                lotesQuery = lotesQuery.Include(l => l.IdOperarioNavigation);

            result = lotesQuery as IQueryable<T>;
        }

        return result;
    }
}

public class NodrizasQueryOptions : QueryOptions
{
    public bool IncludeLotes { get; set; } = false;

    public override IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        var result = base.SetOptions(query);

        if (typeof(T) == typeof(Nodrizas))
        {
            var nodrizasQuery = result as IQueryable<Nodrizas>;

            if (IncludeLotes)
                nodrizasQuery = nodrizasQuery.Include(n => n.LotesNodrizas);

            result = nodrizasQuery as IQueryable<T>;
        }

        return result;
    }
}

public class OperariosQueryOptions : QueryOptions
{
    public bool IncludeLotes { get; set; } = false;
    public bool IncludeIncidencias { get; set; } = false;
    public bool IncludeInspecciones { get; set; } = false;

    public override IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        var result = base.SetOptions(query);

        if (typeof(T) == typeof(Operarios))
        {
            var operariosQuery = result as IQueryable<Operarios>;

            if (IncludeLotes)
                operariosQuery = operariosQuery.Include(o => o.LotesNodrizas);

            if (IncludeIncidencias)
                operariosQuery = operariosQuery.Include(o => o.Incidencias);

            if (IncludeInspecciones)
                operariosQuery = operariosQuery.Include(o => o.Inspecciones);

            result = operariosQuery as IQueryable<T>;
        }

        return result;
    }
}

public class IncidenciasQueryOptions : QueryOptions
{
    public bool IncludeOperario { get; set; } = false;

    public override IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        var result = base.SetOptions(query);

        if (typeof(T) == typeof(Incidencias))
        {
            var incidenciasQuery = result as IQueryable<Incidencias>;

            if (IncludeOperario)
                incidenciasQuery = incidenciasQuery.Include(i => i.IdOperarioNavigation);

            result = incidenciasQuery as IQueryable<T>;
        }

        return result;
    }
}

public class InspeccionesQueryOptions : QueryOptions
{
    public bool IncludeOperario { get; set; } = false;

    public override IQueryable<T> SetOptions<T>(IQueryable<T> query) where T : class
    {
        var result = base.SetOptions(query);

        if (typeof(T) == typeof(Inspecciones))
        {
            var inspeccionesQuery = result as IQueryable<Inspecciones>;

            if (IncludeOperario)
                inspeccionesQuery = inspeccionesQuery.Include(i => i.IdOperarioNavigation);

            result = inspeccionesQuery as IQueryable<T>;
        }

        return result;
    }
}