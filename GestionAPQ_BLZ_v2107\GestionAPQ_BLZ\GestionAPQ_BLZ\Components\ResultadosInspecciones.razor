@inject IMediator Mediator
@inject ICustomToastService ToastService

<CustomLoadingPanel @bind-Visible="Cargando" />

<DxGrid Data="@_listaInspecciones"
		SizeMode="SizeMode.Small"
		CssClass="h-100 w-100"
		PageSize="50"
		SelectionMode="GridSelectionMode.Single"
		AllowSelectRowByClick="true"
		AllowSort="true"
		ShowFilterRow="true"
		CustomizeCellDisplayText="@GridInspecciones_CustomizeCellDisplayText"
		UnboundColumnData="@GridInspecciones_UnboundColumnData">
	<Columns>
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.IdInspecciones)"
						  Caption="ID"
						  Width="80px"
						  DisplayFormat="F0"
						  Visible="false" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Fecha)"
		                  Caption="Fecha"
		                  Width="150px"
		                  DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
		                  SortIndex="0"
		                  SortOrder="GridColumnSortOrder.Descending" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Inspeccion)"
						  Caption="Realizada"
						  Width="100px" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Idproducto)"
						  Caption="Producto"
						  Width="450px"
						  DisplayFormat="F0"
						  TextAlignment="GridTextAlignment.Left" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Idlote)"
						  Caption="Lote"
						  Width="180px" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Viscosidad)"
						  Caption="Viscosidad"
						  Width="100px"
						  DisplayFormat="F0" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.TemperaturaViscosidad)"
						  Caption="Temp. Viscosidad"
						  Width="130px"
						  DisplayFormat="F2" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.Solidos)"
						  Caption="Sólidos"
						  Width="100px"
						  DisplayFormat="F2" />
		<DxGridDataColumn FieldName="SolidosMax"
						  Caption="Sólidos MÁX"
						  Width="100px"
						  DisplayFormat="F2"
						  UnboundType="GridUnboundColumnType.Decimal" />
		<DxGridDataColumn FieldName="SolidosMin"
		                  Caption="Sólidos MÍN"
		                  Width="100px"
						  DisplayFormat="F2"
						  UnboundType="GridUnboundColumnType.Decimal" />
		<DxGridDataColumn FieldName="EstadoSolidos"
						  Caption="Sólidos ESTATUS"
						  Width="120px"
						  UnboundType="GridUnboundColumnType.String" />
		<DxGridDataColumn FieldName="ViscosidadMax"
		                  Caption="Viscosidad MÁX"
		                  Width="100px"
						  DisplayFormat="F2"
						  UnboundType="GridUnboundColumnType.Decimal" />
		<DxGridDataColumn FieldName="ViscosidadMin"
						  Caption="Viscosidad MÍN"
						  Width="100px"
						  DisplayFormat="F2"
						  UnboundType="GridUnboundColumnType.Decimal" />
		<DxGridDataColumn FieldName="EstadoViscosidad"
		                  Caption="Viscosidad ESTATUS"
		                  Width="120px"
						  UnboundType="GridUnboundColumnType.String" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.RealizadoPor)"
						  Caption="Realizada Por"
						  Width="150px" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.ObservacionesQ)"
						  Caption="Obs. Calidad"
						  Width="200px" />
		<DxGridDataColumn FieldName="@nameof(TablaInspeccionesDTO.ObservacionesAplicacion)"
						  Caption="Obs. Aplicación"
						  Width="200px" />
	</Columns>
</DxGrid>

@code {
	private List<TablaInspeccionesDTO> _listaInspecciones = [];
	private List<DetalleBarnizDTO> _ddDetalleBarnices = [];
	private bool Cargando { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await EjecutarConCarga(async () => await CargarDatos());
	}

	private async Task CargarDatos()
	{
		// ejecutamos querys
		var resultInspecciones = await Mediator.Send(new GetAllEntitiesQuery<TablaInspecciones, TablaInspeccionesDTO>());
		var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(null, true));

		// comprobamos si hay errores y mostramos dado el caso
		var listaErrores = resultInspecciones.Errors.Concat(resultProductos.Errors).ToArray();
		if (listaErrores.Any())
		{
			_listaInspecciones = [];
			_ddDetalleBarnices = [];
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// asignamos datos
		_listaInspecciones = resultInspecciones.Data ?? [];
		_ddDetalleBarnices = resultProductos.Data?.OrderBy(i => i.Articulo.Codigo).ToList() ?? [];
	}

	private void GridInspecciones_CustomizeCellDisplayText(GridCustomizeCellDisplayTextEventArgs e)
	{
		var inspeccion = (TablaInspeccionesDTO)e.DataItem;
		var detalleBarniz = _ddDetalleBarnices.FirstOrDefault(d => d.Articulo.Codigo == inspeccion.Idproducto?.ToString());

		switch (e.FieldName)
		{
			case nameof(TablaInspeccionesDTO.Idproducto):
				if (detalleBarniz?.Articulo != null)
					e.DisplayText = $"{detalleBarniz.Articulo.Codigo}, {detalleBarniz.Articulo.Descrip}";
				else
					e.DisplayText = inspeccion.Idproducto?.ToString() ?? "";
				break;
		}
	}

	private void GridInspecciones_UnboundColumnData(GridUnboundColumnDataEventArgs e)
	{
		var inspeccion = (TablaInspeccionesDTO)e.DataItem;
		var detalleBarniz = _ddDetalleBarnices.FirstOrDefault(d => d.Articulo.Codigo == inspeccion.Idproducto?.ToString());
		if (detalleBarniz != null)
		{
			switch (e.FieldName)
			{
				case "SolidosMax":
					e.Value = detalleBarniz?.GetSolidosMax();
					break;
				case "SolidosMin":
					e.Value = detalleBarniz?.GetSolidosMin();
					break;
				case "EstadoSolidos":
					e.Value = detalleBarniz?.GetEstadoSolidos() ?? "";
					break;
				case "ViscosidadMax":
					e.Value = detalleBarniz?.GetViscosidadMax((double?)inspeccion.TemperaturaViscosidad);
					break;
				case "ViscosidadMin":
					e.Value = detalleBarniz?.GetViscosidadMin((double?)inspeccion.TemperaturaViscosidad);
					break;
				case "EstadoViscosidad":
					e.Value = detalleBarniz?.GetEstadoViscosidad((double?)inspeccion.TemperaturaViscosidad, (int?)inspeccion.Viscosidad) ?? "";
					break;
			}
		}
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}
}
