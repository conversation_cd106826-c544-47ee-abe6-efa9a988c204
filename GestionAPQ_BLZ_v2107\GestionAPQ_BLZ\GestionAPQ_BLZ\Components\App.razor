﻿@inject IConfiguration Configuration

<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>@Configuration["AppMetadata:ProjectNameDisplay"]</title>
	<base href="/" />
	<link href=@AppendVersion("app.css") rel="stylesheet" />
	<link href=@(AppendVersion($"{Configuration["AppMetadata:ProjectName"]}.styles.css")) rel="stylesheet" />
	@* FÁBRICA NO TIENE ACCESO A ESTA RUTA PORQUE ESTÁ CAPADA. SE IMPORTAN LOS ICONOS AL PROYECTO *@
	@* <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"> *@
	<link href=@AppendVersion("bootstrap-icons-1.13.1/bootstrap-icons.min.css") rel="stylesheet" />
	<link href=@AppendVersion("_content/DevExpress.Blazor.Themes/blazing-berry.bs5.css") rel="stylesheet" />
	<link rel="icon" type="image/png" href="favicon.png" />

	@* Creo que no es necesario indicar el rendermode *@
	@* <HeadOutlet @rendermode="InteractiveServer" /> *@
	@* <HeadOutlet/> *@
	@DxResourceManager.RegisterScripts()
</head>

<body>
	<Routes @rendermode="InteractiveServer" />
	<script src="_framework/blazor.web.js"></script>
	<script src=@AppendVersion("scripts/localStorageService.js")></script>
	<script src=@AppendVersion("scripts/documentService.js")></script>
	<script src=@AppendVersion("scripts/reportViewer.js")></script>
</body>

</html>

@code {
	private string AppendVersion(string path) => $"{path}?v={typeof(DevExpress.Blazor.ResourcesConfigurator).Assembly.GetName().Version}";
}