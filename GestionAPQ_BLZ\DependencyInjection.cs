namespace GestionAPQ_BLZ;

using Blazr.RenderState.Server;
using DevExpress.AspNetCore.Reporting;
using DevExpress.Blazor;
using DevExpress.Blazor.Reporting;
using DevExpress.XtraReports.Services;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using Repositories.APQLitalsa;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita;
using Repositories.Dato01Lita.Base;
using Services;
using Services.Base;

public static class DependencyInjection
{
    public static WebApplicationBuilder CompleteWebUiConfig(this WebApplicationBuilder builder)
    {
        // CONVENCIÓN BLAZOR
        builder.Services.AddRazorComponents()
            .AddInteractiveServerComponents()
            .AddInteractiveWebAssemblyComponents();

        // BLAZRRENDERSTATE
        builder.AddBlazrRenderStateServerServices();


        // DEVEXPRESS
        builder.Services.AddDevExpressBlazor(options =>
        {
            options.BootstrapVersion = BootstrapVersion.v5;
            options.SizeMode = SizeMode.Medium;
        });
        builder.Services.AddDevExpressBlazorReporting();
        builder.Services.ConfigureReportingServices(configurator =>
        {
            configurator.ConfigureReportDesigner(designerConfigurator => { });
            configurator.UseAsyncEngine();
        });
        builder.Services.AddRazorPages(); // necesario tmbn para que funcione dxdocumentviewer
        builder.Services.AddScoped<IReportProviderAsync, CustomReportProviderAsync>();


        // SCOPES
        builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
        builder.Services.AddScoped<ICustomToastService, CustomToastService>();
        builder.Services.AddScoped<ICustomDialogService, CustomDialogService>();
        builder.Services.AddScoped<IDocumentService, DocumentService>();

        return builder;
    }

    public static WebApplicationBuilder CompleteApplicationConfig(this WebApplicationBuilder builder)
    {
        // TINYMAPPER
        SetMapeos();

        // MEDIATR
        builder.Services.AddMediatR(
            cfg => { cfg.RegisterServicesFromAssemblies(typeof(DependencyInjection).Assembly); });

        return builder;
    }

    public static WebApplicationBuilder CompleteInfraestructureConfig(this WebApplicationBuilder builder)
    {
        // IN2CONTEXT
        builder.Services.AddDbContext<Dato01LitaContext>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionDatoLita01"));
        });
        builder.Services.AddScoped<IAArticuRepo, AArticuRepo>();
        builder.Services.AddScoped<IAViscosRepo, AViscosRepo>();
        builder.Services.AddScoped<IALotessRepo, ALotessRepo>();
        builder.Services.AddScoped<IAEntdiaRepo, AEntdiaRepo>();
        builder.Services.AddScoped<IASalidaRepo, ASalidaRepo>();
        builder.Services.AddScoped<ILotesNodrizasRepo, LotesNodrizasRepo>();

        // APQLITALSACONTEXT
        builder.Services.AddDbContext<APQLitalsaContext>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionApqLitalsa"));
        });
        builder.Services.AddScoped<IOperariosRepo, OperariosRepo>();
        builder.Services.AddScoped<INodrizasRepo, NodrizasRepo>();
        builder.Services.AddScoped<ILotesNodrizasRepo, LotesNodrizasRepo>();
        builder.Services.AddScoped<IIncidenciasRepo, IncidenciasRepo>();
        builder.Services.AddScoped<IInspeccionesRepo, InspeccionesRepo>();


        return builder;
    }

    private static void SetMapeos()
    {
        // Entidades sin claves foráneas (mapeo estándar)
        SetMapeoEstandar<AArticu, AArticuDTO>();
        SetMapeoEstandar<AViscos, AViscosDTO>();
        SetMapeoEstandar<ALotess, ALotessDTO>();
        SetMapeoEstandar<AEntdia, AEntdiaDTO>();
        SetMapeoEstandar<ASalida, ASalidaDTO>();

        // Entidades con claves foráneas (mapeo específico para evitar referencias circulares)
        SetMapeoOperarioDTO();
        SetMapeoIncidenciaDTO();
        SetMapeoInspeccionDTO();
        SetMapeoNodrizaDTO();
        SetMapeoLoteNodrizaDTO();
    }

    private static void SetMapeoEstandar<TEntity, TDto>()
    {
        TinyMapper.Bind<TEntity, TDto>();
        TinyMapper.Bind<TDto, TEntity>();
        TinyMapper.Bind<List<TEntity>, List<TDto>>();
        TinyMapper.Bind<List<TDto>, List<TEntity>>();
    }

    private static void SetMapeoOperarioDTO()
    {
        TinyMapper.Bind<Operarios, OperarioDTO>(config =>
        {
            config.Ignore(source => source.Incidencias);
            config.Ignore(source => source.Inspecciones);
            config.Ignore(source => source.LotesNodrizas);
        });
        TinyMapper.Bind<OperarioDTO, Operarios>();
        TinyMapper.Bind<List<Operarios>, List<OperarioDTO>>();
        TinyMapper.Bind<List<OperarioDTO>, List<Operarios>>();
    }

    private static void SetMapeoIncidenciaDTO()
    {
        TinyMapper.Bind<Incidencias, IncidenciaDTO>(config =>
        {
            config.Ignore(source => source.IdOperarioNavigation);
        });
        TinyMapper.Bind<IncidenciaDTO, Incidencias>();
        TinyMapper.Bind<List<Incidencias>, List<IncidenciaDTO>>();
        TinyMapper.Bind<List<IncidenciaDTO>, List<Incidencias>>();
    }

    private static void SetMapeoInspeccionDTO()
    {
        TinyMapper.Bind<Inspecciones, InspeccionDTO>(config =>
        {
            config.Ignore(source => source.IdOperarioNavigation);
        });
        TinyMapper.Bind<InspeccionDTO, Inspecciones>();
        TinyMapper.Bind<List<Inspecciones>, List<InspeccionDTO>>();
        TinyMapper.Bind<List<InspeccionDTO>, List<Inspecciones>>();
    }

    private static void SetMapeoNodrizaDTO()
    {
        TinyMapper.Bind<Nodrizas, NodrizaDTO>(config =>
        {
            config.Ignore(source => source.LotesNodrizas);
        });
        TinyMapper.Bind<NodrizaDTO, Nodrizas>();
        TinyMapper.Bind<List<Nodrizas>, List<NodrizaDTO>>();
        TinyMapper.Bind<List<NodrizaDTO>, List<Nodrizas>>();
    }

    private static void SetMapeoLoteNodrizaDTO()
    {
        TinyMapper.Bind<LotesNodrizas, LoteNodrizaDTO>(config =>
        {
            config.Ignore(source => source.IdNodrizaNavigation);
            config.Ignore(source => source.IdOperarioNavigation);
        });
        TinyMapper.Bind<LoteNodrizaDTO, LotesNodrizas>();
        TinyMapper.Bind<List<LotesNodrizas>, List<LoteNodrizaDTO>>();
        TinyMapper.Bind<List<LoteNodrizaDTO>, List<LotesNodrizas>>();
    }
}