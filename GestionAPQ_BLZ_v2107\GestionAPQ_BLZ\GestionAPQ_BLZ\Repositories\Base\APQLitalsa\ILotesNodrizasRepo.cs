﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ILotesNodrizasRepo : IRepository<Lotesnodrizas>
{
    public Task<Lotesnodrizas?> GetUltimoLoteNodrizaByIdProducto(int numNodriza,
        int idProducto, bool asNoTracking, CancellationToken cancellationToken);

    public Task<List<Lotesnodrizas>> GetLotesNodrizaByIdProducto(int idProducto, bool asNoTracking,
        CancellationToken cancellationToken);
}