namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetSiguienteLoteNodrizaQuery(int IdNodriza) : IRequest<SingleResult<DetalleLoteBarnizDTO>>;

public class
    GetSiguienteLoteNodrizaQueryHandler : IRequestHandler<GetSiguienteLoteNodrizaQuery,
    SingleResult<DetalleLoteBarnizDTO>>
{
    private readonly IALotessRepo _aLotessRepo;
    private readonly IAArticuRepo _aArticuRepo;
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;
    private readonly INodrizasRepo _nodrizasRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetSiguienteLoteNodrizaQueryHandler(
        IALotessRepo aLotessRepo,
        IAArticuRepo aArticuRepo,
        IAEntdiaRepo aEntdiaRepo,
        ILotesNodrizasRepo lotesNodrizasRepo,
        INodrizasRepo nodrizasRepo,
        IInspeccionesRepo inspeccionesRepo)
    {
        _aLotessRepo = aLotessRepo;
        _aArticuRepo = aArticuRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
        _nodrizasRepo = nodrizasRepo;
        _inspeccionesRepo = inspeccionesRepo;
    }

    public async Task<SingleResult<DetalleLoteBarnizDTO>> Handle(GetSiguienteLoteNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleLoteBarnizDTO> { Data = null, Errors = [] };

        try
        {
            // con el id de la nodriza, recuperamos el idproducto de tablaproductosnodrizas
            var nodriza =
                await _nodrizasRepo.GetById(
                    request.IdNodriza,
                    new NodrizasQueryOptions(),
                    cancellationToken);
            var idProducto = nodriza?.IdProducto;
            if (!idProducto.HasValue)
            {
                result.Errors.Add("No se ha encontrado el producto de la nodriza indicada.");
                return result;
            }

            // recuperamos el último lote de lotesnodrizas
            var loteNodriza =
                await _lotesNodrizasRepo.GetUltimoLoteByNodrizaYProducto(request.IdNodriza,
                    idProducto.Value,
                    new LotesNodrizasQueryOptions(),
                    cancellationToken);
            var ultimoLote = loteNodriza?.Lote;
            if (ultimoLote == null)
            {
                result.Errors.Add("No se ha encontrado el último lote del producto registrado en la nodriza.");
                return result;
            }

            // recuperamos el artículo barniz y los lotes del mismo con stock
            var lotesConStockPorBarniz = await _aArticuRepo.GetBarnicesInnerJoinLotesConStock(
                Convert.ToString(idProducto),
                _aLotessRepo,
                new QueryOptions(),
                cancellationToken);
            var barniz = lotesConStockPorBarniz.FirstOrDefault().Item1;
            if (barniz == null)
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            var lotesBarniz = lotesConStockPorBarniz.Select(i => i.Item2) ?? [];
            if (!lotesBarniz.Any())
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            // comprobamos que haya inspecciones pasadas por ese idProducto
            var listaInspeccionesAprobadasPorBarniz =
                await _inspeccionesRepo.GetInspeccionesPasadasProductos(idProducto.Value,
                    new InspeccionesQueryOptions(),
                    cancellationToken);
            if (!listaInspeccionesAprobadasPorBarniz.Any())
            {
                result.Errors.Add($"No hay inspecciones pasadas para el producto {idProducto}.");
                return result;
            }

            // ahora, sabiendo
            // - que el producto es un barniz
            // - que tiene lotes con stock
            // - que hay lotes de ese barniz con inspecciones pasadas
            // estamos en posición de saber el siguiente lote a usar:
            // 1- primero, comprobamos si hay algún lote como el metido en la nodriza con inspección pasada y con stock > 0
            // 2 -si no lo hubiera, buscar el lote completo (> 800 de stock) que esté más cerca de caducar

            // 1- primero, comprobamos si hay lote del mismo que último metido, con inspección pasada y con stock > 0
            ALotess? siguienteLote = null;
            if (listaInspeccionesAprobadasPorBarniz.Where(i => i.IdLote.Equals(ultimoLote)).Any())
                siguienteLote = lotesBarniz
                    .FirstOrDefault(i =>
                        i.Lote == ultimoLote
                        && i.Stock > 800);

            // 2 -si no lo hubiera, buscar el lote completo (> 800 de stock) que esté más cerca de caducar con ese código de prod
            siguienteLote ??= lotesBarniz
                .Where(i => i.Stock > 800)
                .OrderBy(b => b.Fechalote)
                .FirstOrDefault();

            if (siguienteLote == null)
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
            else
                result.Data = new DetalleLoteBarnizDTO
                {
                    Lote = TinyMapper.Map<ALotessDTO>(siguienteLote)
                };
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}