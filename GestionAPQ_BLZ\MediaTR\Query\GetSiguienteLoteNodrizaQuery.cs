namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetSiguienteLoteNodrizaQuery(int IdNodriza) : IRequest<SingleResult<ALotessDTO>>;

public class
    GetSiguienteLoteNodrizaQueryHandler : IRequestHandler<GetSiguienteLoteNodrizaQuery, SingleResult<ALotessDTO>>
{
    private readonly IALotessRepo _aLotessRepo;
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAEntdiaRepo _aEntdiaRepo;
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;
    private readonly INodrizasRepo _nodrizasRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetSiguienteLoteNodrizaQueryHandler(
        IALotessRepo aLotessRepo,
        IAArticuRepo aArticuRepo,
        IAEntdiaRepo aEntdiaRepo,
        ILotesNodrizasRepo lotesNodrizasRepo,
        INodrizasRepo nodrizasRepo,
        IInspeccionesRepo inspeccionesRepo)
    {
        _aLotessRepo = aLotessRepo;
        _aArticuRepo = aArticuRepo;
        _aEntdiaRepo = aEntdiaRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
        _nodrizasRepo = nodrizasRepo;
        _inspeccionesRepo = inspeccionesRepo;
    }

    public async Task<SingleResult<ALotessDTO>> Handle(GetSiguienteLoteNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<ALotessDTO> { Data = null, Errors = [] };

        try
        {
            // 1. Obtener nodriza y validar
            var nodriza = await _nodrizasRepo.GetById(request.IdNodriza, new NodrizasQueryOptions(), cancellationToken);
            if (nodriza?.IdProducto == null)
            {
                result.Errors.Add("No se ha encontrado el producto de la nodriza indicada.");
                return result;
            }

            var idProducto = nodriza.IdProducto.Value;

            // 2. Ejecutar consultas de forma secuencial
            var ultimoLoteNodriza = await _lotesNodrizasRepo.GetUltimoLoteByNodrizaYProducto(
                request.IdNodriza, idProducto, new LotesNodrizasQueryOptions(), cancellationToken);

            var lotesConStock = await _aArticuRepo.GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
                idProducto.ToString(), _aLotessRepo, _aEntdiaRepo, new QueryOptions(), null, cancellationToken);

            var inspecciones = await _inspeccionesRepo.GetInspeccionesPasadasProductos(
                idProducto, new InspeccionesQueryOptions(), cancellationToken);

            if (!lotesConStock.Any())
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            // 3. Crear índices optimizados para búsquedas rápidas
            var ultimoLote = ultimoLoteNodriza?.Lote;
            var lotesAprobados = inspecciones.Select(i => i.IdLote).ToHashSet();
            var lotesDisponibles = lotesConStock.Select(x => x.Item2)
                .Where(l => l.Stock > 800 && lotesAprobados.Contains(l.Lote))
                .OrderBy(l => l.Fechalote)
                .ToList();

            // 4. Seleccionar siguiente lote con estrategia optimizada
            ALotess? siguienteLote = null;

            // Estrategia 1: Reutilizar el mismo lote si está disponible
            if (!string.IsNullOrEmpty(ultimoLote))
                siguienteLote = lotesDisponibles.FirstOrDefault(l => l.Lote == ultimoLote);

            // Estrategia 2: Seleccionar el lote más antiguo con stock suficiente
            siguienteLote ??= lotesDisponibles.FirstOrDefault();

            if (siguienteLote == null)
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
            }
            else
            {
                result.Data = TinyMapper.Map<ALotessDTO>(siguienteLote);
            }
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}