﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;

public record GetUltimoLoteNodrizaQuery(int IdNodriza)
    : IRequest<SingleResult<LoteNodrizaDTO>>;

public class
    GetUltimoLoteNodrizaQueryHandler : IRequestHandler<GetUltimoLoteNodrizaQuery,
    SingleResult<LoteNodrizaDTO>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;
    private readonly INodrizasRepo _nodrizasRepo;

    public GetUltimoLoteNodrizaQueryHandler(INodrizasRepo nodrizasRepo, ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<LoteNodrizaDTO>> Handle(
        GetUltimoLoteNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<LoteNodrizaDTO>
        {
            Data = null,
            Errors = []
        };

        try
        {
            // Paso 1: Obtener solo la nodriza
            var nodriza =
                await _nodrizasRepo.GetById(request.IdNodriza, new NodrizasQueryOptions(), cancellationToken);

            if (nodriza == null)
            {
                result.Errors.Add($"No se ha encontrado la nodriza con ID {request.IdNodriza}.");
                return result;
            }

            if (nodriza.IdProducto == null)
            {
                result.Errors.Add($"La nodriza {nodriza.NumNodriza} no tiene un producto asignado.");
                return result;
            }

            if (!nodriza.Activo)
            {
                result.Errors.Add($"La nodriza {nodriza.NumNodriza} no está activada.");
                return result;
            }

            // Paso 2: Obtener SOLO el último lote específico
            var ultimoLote = await _lotesNodrizasRepo.GetUltimoLoteByNodrizaYProducto(
                nodriza.Id,
                nodriza.IdProducto.Value,
                new LotesNodrizasQueryOptions(),
                cancellationToken);

            // Paso 3: Mapear a DTOs
            var ultimoLoteDto = ultimoLote != null ? TinyMapper.Map<LoteNodrizaDTO>(ultimoLote) : null;

            result.Data = ultimoLoteDto;
        }
        catch (Exception ex)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}