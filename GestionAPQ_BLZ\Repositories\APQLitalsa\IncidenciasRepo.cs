﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class IncidenciasRepo : IIncidenciasRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Incidencias> _dbSet;

    public IncidenciasRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Incidencias>();
    }

    public async Task<List<Incidencias>> GetAll(IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);
        return await query
            .OrderByDescending(i => i.FechaCreacion)
            .ToListAsync(cancellationToken);
    }

    public async Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(
        string codigo,
        string lote,
        string ubicacion,
        IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);
        return await query
            .Where(i => i.Codigo.Equals(codigo)
                        && i.Lote.Equals(lote)
                        && i.Ubicacion.Equals(ubicacion))
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<Incidencias?> GetById(int id, IncidenciasQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);
        return await query.FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
    }

    public async Task Add(Incidencias incidencia, CancellationToken cancellationToken = default)
    {
        await _dbSet.AddAsync(incidencia, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task Update(Incidencias incidencia, CancellationToken cancellationToken = default)
    {
        _dbSet.Update(incidencia);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }

    public async Task Delete(Incidencias incidencia, CancellationToken cancellationToken = default)
    {
        _dbSet.Remove(incidencia);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}