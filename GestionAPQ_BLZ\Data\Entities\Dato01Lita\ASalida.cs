﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;

public partial class ASalida
{
    public string Almacen0 { get; set; }

    public string Numero { get; set; }

    public string Texto { get; set; }

    public string Cliente { get; set; }

    public string Codent { get; set; }

    public string Nombre2 { get; set; }

    public string Direccion2 { get; set; }

    public string Direccio21 { get; set; }

    public string Numero2 { get; set; }

    public string Poblacion2 { get; set; }

    public string Provinci2 { get; set; }

    public string Posta2 { get; set; }

    public string Codigo { get; set; }

    public string Linea { get; set; }

    public string Descrip { get; set; }

    public string Descri1 { get; set; }

    public string Descri2 { get; set; }

    public string Descri3 { get; set; }

    public string Descri4 { get; set; }

    public string Descri5 { get; set; }

    public string Descri6 { get; set; }

    public string Descri7 { get; set; }

    public string Estadis { get; set; }

    public double? Cantidad { get; set; }

    public string Tipiva { get; set; }

    public double? Precio { get; set; }

    public double? Precio1 { get; set; }

    public double? Descuento { get; set; }

    public double? Iva { get; set; }

    public DateTime? Fecsal { get; set; }

    public double? Importe { get; set; }

    public double? Importe1 { get; set; }

    public string Factura { get; set; }

    public double? Costosal { get; set; }

    public double? Costosal1 { get; set; }

    public bool? Volcado { get; set; }

    public double? Bultos { get; set; }

    public double? Tbultos { get; set; }

    public string Transpo { get; set; }

    public string Transpor { get; set; }

    public string Portes { get; set; }

    public string Pedido { get; set; }

    public string Fpago { get; set; }

    public string Facturado { get; set; }

    public string Almacen { get; set; }

    public string Zona { get; set; }

    public string Codiprov { get; set; }

    public string Represen { get; set; }

    public double? Comision { get; set; }

    public double? Cantidad0 { get; set; }

    public string Prealba { get; set; }

    public double? Venci1 { get; set; }

    public double? Venci2 { get; set; }

    public double? Venci3 { get; set; }

    public double? Venci4 { get; set; }

    public double? Venci5 { get; set; }

    public double? Venci6 { get; set; }

    public double? Porcen1 { get; set; }

    public double? Porcen2 { get; set; }

    public double? Porcen3 { get; set; }

    public double? Porcen4 { get; set; }

    public double? Porcen5 { get; set; }

    public double? Porcen6 { get; set; }

    public double? Cambio { get; set; }

    public double? Cambio1 { get; set; }

    public string Nota1 { get; set; }

    public string Nota2 { get; set; }

    public string Nota3 { get; set; }

    public string Nota4 { get; set; }

    public string Nota5 { get; set; }

    public string Unidad { get; set; }

    public double? Imporneto { get; set; }

    public double? Imporneto1 { get; set; }

    public double? Pcostomed { get; set; }

    public double? Pcostomed1 { get; set; }

    public string Registro { get; set; }

    public string Undcompra { get; set; }

    public string Undstock { get; set; }

    public string Undventa { get; set; }

    public double? Coefic { get; set; }

    public double? Largo { get; set; }

    public double? Ancho { get; set; }

    public double? Espesor { get; set; }

    public double? Gramaje { get; set; }

    public double? Cantidadc { get; set; }

    public string Moneda { get; set; }

    public string Moneda1 { get; set; }

    public double? Redondeo { get; set; }

    public double? Redondeo1 { get; set; }

    public string Lote { get; set; }

    public string Ubicacion { get; set; }

    public DateTime? Fechalote { get; set; }

    public string Parte { get; set; }

    public string Refpedcli { get; set; }

    public string Pais2 { get; set; }

    public string Lineaped { get; set; }

    public string Fotra { get; set; }

    public string Bloqueo { get; set; }

    public string Matricula { get; set; }

    public string Clienteok { get; set; }

    public string Albaranf { get; set; }

    public string Sscc { get; set; }

    public double? Palet { get; set; }
}