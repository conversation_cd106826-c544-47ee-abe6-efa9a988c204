﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable di**ble
using System;
using System.Collections.Generic;
using GestionAQP_BLZ.Server.Data.Entities.APQLital**;
using Microsoft.EntityFrameworkCore;

namespace GestionAQP_BLZ.Server.Data.DbContexts.APQLital**;

public partial class APQLital**Context : DbContext
{
    public APQLital**Context()
    {
    }

    public APQLital**Context(DbContextOptions<APQLital**Context> options)
        : base(options)
    {
    }

    public virtual DbSet<Incidencias> Incidencias { get; set; }

    public virtual DbSet<Lotesnodrizas> Lotesnodrizas { get; set; }

    public virtual DbSet<TablaApq> TablaApq { get; set; }

    public virtual DbSet<TablaInspecciones> TablaInspecciones { get; set; }

    public virtual DbSet<TablaOperarios> TablaOperarios { get; set; }

    public virtual DbSet<TablaPesosEnvases> TablaPesosEnvases { get; set; }

    public virtual DbSet<TablaProductosNodrizas> TablaProductosNodrizas { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Data Source=QPLANT1;Initial Catalog=ApqLital**;Persist Security Info=True;User ID=**;Password=**;Encrypt=False");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Incidencias>(entity =>
        {
            entity.Property(e => e.Codigo)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.CreadoPor)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FechaCreacion).HasColumnType("datetime");
            entity.Property(e => e.Incidencia).IsRequired();
            entity.Property(e => e.Lote)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Ubicacion)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<Lotesnodrizas>(entity =>
        {
            entity.ToTable("LOTESNODRIZAS");

            entity.Property(e => e.Fecha)
                .HasColumnType("date")
                .HasColumnName("FECHA");
            entity.Property(e => e.Hora)
                .HasColumnType("time(0)")
                .HasColumnName("HORA");
            entity.Property(e => e.Idproducto).HasColumnName("IDPRODUCTO");
            entity.Property(e => e.Lote)
                .HasMaxLength(255)
                .HasColumnName("LOTE");
            entity.Property(e => e.Nodriza).HasColumnName("NODRIZA");
            entity.Property(e => e.Operario)
                .HasMaxLength(255)
                .HasColumnName("OPERARIO");
            entity.Property(e => e.Ubicacion)
                .HasMaxLength(255)
                .HasColumnName("ubicacion");
        });

        modelBuilder.Entity<TablaApq>(entity =>
        {
            entity.ToTable("TablaAPQ");

            entity.Property(e => e.IdPosición).HasMaxLength(255);
            entity.Property(e => e.Observacion).HasMaxLength(255);
        });

        modelBuilder.Entity<TablaInspecciones>(entity =>
        {
            entity.HasKey(e => e.IdInspecciones);

            entity.Property(e => e.Fecha).HasColumnType("datetime");
            entity.Property(e => e.Idlote).HasMaxLength(255);
            entity.Property(e => e.ObservacionesAplicacion).HasMaxLength(255);
            entity.Property(e => e.ObservacionesQ).HasMaxLength(255);
            entity.Property(e => e.RealizadoPor).HasMaxLength(255);
            entity.Property(e => e.Solidos).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.TemperaturaViscosidad).HasColumnType("decimal(6, 2)");
        });

        modelBuilder.Entity<TablaOperarios>(entity =>
        {
            entity.Property(e => e.Operario).HasMaxLength(255);
        });

        modelBuilder.Entity<TablaPesosEnvases>(entity =>
        {
            entity.Property(e => e.Diferencia).HasColumnName("DIFERENCIA");
            entity.Property(e => e.Fecha).HasColumnType("datetime");
            entity.Property(e => e.FechaCaducidad).HasColumnType("datetime");
            entity.Property(e => e.FechaFin).HasColumnType("datetime");
            entity.Property(e => e.Numenvase).HasColumnName("NUMENVASE");
            entity.Property(e => e.Observaciones).HasMaxLength(250);
            entity.Property(e => e.Pesoenvase).HasColumnName("PESOENVASE");
            entity.Property(e => e.Pesorealentrada).HasColumnName("PESOREALENTRADA");
            entity.Property(e => e.Pesoteoricoentrada).HasColumnName("PESOTEORICOENTRADA");
            entity.Property(e => e.Pesovacio).HasColumnName("PESOVACIO");
            entity.Property(e => e.RealizadoPor).HasMaxLength(255);
            entity.Property(e => e.StockActual).HasColumnType("decimal(18, 2)");
        });

        modelBuilder.Entity<TablaProductosNodrizas>(entity =>
        {
            entity.Property(e => e.FechaRetirada).HasColumnType("datetime");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}