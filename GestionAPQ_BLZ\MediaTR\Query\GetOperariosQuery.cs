﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;

public record GetOperariosQuery : IRequest<ListResult<OperarioDTO>>;

public class GetOperariosQueryHandler : IRequestHandler<GetOperariosQuery, ListResult<OperarioDTO>>
{
    private readonly IOperariosRepo _operariosRepo;

    public GetOperariosQueryHandler(IOperariosRepo operariosRepo)
    {
        _operariosRepo = operariosRepo;
    }

    public async Task<ListResult<OperarioDTO>> Handle(GetOperariosQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<OperarioDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var listaOperarios = await _operariosRepo.GetAllActivos(new OperariosQueryOptions(), cancellationToken);
            result.Data = TinyMapper.Map<List<OperarioDTO>>(listaOperarios);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}