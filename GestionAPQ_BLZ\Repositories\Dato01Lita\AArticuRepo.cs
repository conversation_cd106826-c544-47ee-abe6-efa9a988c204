﻿namespace GestionAPQ_BLZ.Repositories.Dato01Lita;

using System.Linq.Expressions;
using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class AArticuRepo : IAArticuRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<AArticu> _dbSet;

    public AArticuRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<AArticu>();
    }

    public IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, QueryOptions options)
    {
        var query = options.SetOptions(_dbSet);
        query = query.Where(i => i.Estadis.StartsWith("BABA"));

        if (!string.IsNullOrEmpty(idBarniz))
            query = query.Where(i => i.Codigo.Equals(idBarniz));

        return query;
    }

    public async Task<List<AArticu>> GetBarnices(string? idBarniz,
        QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        return await GetIQueryableBarnices(idBarniz, options).ToListAsync(cancellationToken);
    }

    public async Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        IAViscosRepo aViscosRepo,
        QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var queryBarnices = GetIQueryableBarnices(idBarniz, options);
        var queryViscosidades = aViscosRepo.GetIQueryableViscosidades(idBarniz, options);

        // Join en base de datos usando ambos repositorios
        var query = from barniz in queryBarnices
            join viscosidad in queryViscosidades on barniz.Codigo equals viscosidad.Codigo into viscosidadGroup
            from viscosidad in viscosidadGroup.DefaultIfEmpty()
            select new { barniz, viscosidad };

        var result = await query.ToListAsync(cancellationToken);
        return result.Select(x => (x.barniz, x.viscosidad)).ToList();
    }

    public async Task<List<(AArticu, ALotess, AEntdia)>> GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
        string? idBarniz,
        IALotessRepo aLotessRepo,
        IAEntdiaRepo aEntdiaRepo,
        QueryOptions options,
        Expression<Func<(AArticu, ALotess, AEntdia), object>>? groupBy,
        CancellationToken cancellationToken)
    {
        var queryBarnices = GetIQueryableBarnices(idBarniz, options);
        var queryLotes = aLotessRepo.GetIQueryableLotesConStock(idBarniz, options);
        var queryEntradas = aEntdiaRepo.GetIQueryableEntradas(idBarniz, options);

        // ejecutamos la query y agrupamos si fuera necesario
        var query = queryBarnices // innerjoin con lotes
            .Join(
                queryLotes,
                Barniz => Barniz.Codigo,
                Lote => Lote.Codigo,
                (Barniz, Lote) => new { Barniz, Lote }
            )
            .SelectMany(bl => // LEFT JOIN con a_entdia
                    queryEntradas
                        .Where(e =>
                            e.Codigo == (bl.Lote.Codigo ?? "")
                            && e.Lote.ToUpper() == (bl.Lote.Lote ?? "").ToUpper()
                            && e.Fecent.HasValue)
                        .OrderBy(e => e.Fecent)
                        .Take(1)
                        .DefaultIfEmpty(), // obligatorio para LEFT JOIN
                (bl, entrada) => new ValueTuple<AArticu, ALotess, AEntdia>(bl.Barniz, bl.Lote, entrada)
            );

        var resultados = await query.ToListAsync(cancellationToken);

        // hacemos el groupby postquery porque si lo hago vía ef me falla
        // por tanto, almacenamos resultados en memoria y agrupamos ahí
        if (groupBy != null)
        {
            var compiledGroupBy = groupBy.Compile();
            resultados = resultados
                .GroupBy(compiledGroupBy)
                .Select(g => g.FirstOrDefault())
                .ToList();
        }

        return resultados;
    }
}