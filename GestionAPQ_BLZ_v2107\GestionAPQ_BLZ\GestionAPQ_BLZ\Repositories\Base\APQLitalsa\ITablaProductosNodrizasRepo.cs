﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ITablaProductosNodrizasRepo : IRepository<TablaProductosNodrizas>
{
    public Task<TablaProductosNodrizas?> GetTablaProductosNodrizasById(int id, bool asNoTracking,
        CancellationToken cancellationToken);

    public Task<TablaProductosNodrizas?> GetTablaProductosNodrizasByNumNodriza(int numNodriza,
        bool asNoTracking, CancellationToken cancellationToken);
}