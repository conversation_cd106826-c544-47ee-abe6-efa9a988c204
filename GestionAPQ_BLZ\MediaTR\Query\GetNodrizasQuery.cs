﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;

public record GetNodrizasQuery : IRequest<ListResult<NodrizaDTO>>;

public class GetNodrizasQueryHandler : IRequestHandler<GetNodrizasQuery, ListResult<NodrizaDTO>>
{
    private readonly INodrizasRepo _nodrizasRepo;

    public GetNodrizasQueryHandler(INodrizasRepo nodrizasRepo)
    {
        _nodrizasRepo = nodrizasRepo;
    }

    public async Task<ListResult<NodrizaDTO>> Handle(GetNodrizasQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<NodrizaDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var listaNodrizas = await _nodrizasRepo.GetAll(new NodrizasQueryOptions(), cancellationToken);
            result.Data = TinyMapper.Map<List<NodrizaDTO>>(listaNodrizas);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}