﻿namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GrabarTablaProductosNodrizasCommand(TablaProductosNodrizasDTO TablaProductosNodrizasDTO)
    : IRequest<SingleResult<int>>;

public class
    GrabarTablaProductosNodrizasCommandHandler : IRequestHandler<GrabarTablaProductosNodrizasCommand, SingleResult<int>>
{
    private readonly ITablaProductosNodrizasRepo _tablaProductosNodrizasRepo;

    public GrabarTablaProductosNodrizasCommandHandler(ITablaProductosNodrizasRepo tablaProductosNodrizasRepo)
    {
        _tablaProductosNodrizasRepo = tablaProductosNodrizasRepo;
    }

    public async Task<SingleResult<int>> Handle(GrabarTablaProductosNodrizasCommand request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        var numNodriza = request.TablaProductosNodrizasDTO.IdNodriza;
        if (numNodriza == null)
        {
            result.Errors.Add("Tienes que indicar un número de nodriza.");
            return result;
        }

        // si es edición, debemos comprobar que exista la nodriza
        // ojo, no es lo mismo el campo id, que el campo idnodriza
        TablaProductosNodrizas? nodrizaDb;
        if (request.TablaProductosNodrizasDTO.Id != null) // es edición
        {
            // comprobamos que haya nodriza con ese id
            nodrizaDb =
                await _tablaProductosNodrizasRepo.GetTablaProductosNodrizasById(
                    request.TablaProductosNodrizasDTO.Id.Value, false, cancellationToken);
            if (nodrizaDb == null)
            {
                result.Errors.Add("No se ha encontrado la nodriza indicada en el sistema.");
                return result;
            }

            // comprobamos que no hcambie el num de la nodriza
            if (nodrizaDb.IdNodriza != numNodriza)
            {
                result.Errors.Add("No puedes cambiar el número de la nodriza.");
                return result;
            }

            // actualizamos los campos oportunos
            nodrizaDb.Idproducto = request.TablaProductosNodrizasDTO.Idproducto;
            nodrizaDb.Activo = request.TablaProductosNodrizasDTO.Activo;
        }
        else // es nuevo
        {
            // comprobamos que sea un número positivo de nodriza
            if (request.TablaProductosNodrizasDTO.IdNodriza is null or <= 0)
            {
                result.Errors.Add("Tienes que indicar un número positivo de nodriza.");
                return result;
            }

            // comprobamos que no haya una nodriza con ese número ya en el sistema
            nodrizaDb =
                await _tablaProductosNodrizasRepo.GetTablaProductosNodrizasByNumNodriza(numNodriza.Value,
                    true, cancellationToken);
            if (nodrizaDb != null)
            {
                result.Errors.Add("Ya hay una nodriza registrada en el sistema con ese número. Indica otro.");
                return result;
            }

            nodrizaDb = TinyMapper.Map<TablaProductosNodrizas>(request.TablaProductosNodrizasDTO);
        }

        if (nodrizaDb.Idproducto == 0 && nodrizaDb.Activo)
        {
            result.Errors.Add("Si quieres tener la nodriza habilitada, debes indicar un barniz.");
            return result;
        }

        var resultDb = await _tablaProductosNodrizasRepo.UpdateEntityWithId(cancellationToken, nodrizaDb);
        result.Data = resultDb;
        return result;
    }
}