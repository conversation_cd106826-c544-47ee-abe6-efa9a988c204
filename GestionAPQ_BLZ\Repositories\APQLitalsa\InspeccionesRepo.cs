namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using Microsoft.EntityFrameworkCore;

public class InspeccionesRepo : IInspeccionesRepo
{
    private readonly APQLitalsaContext _dbContext;
    private readonly DbSet<Inspecciones> _dbSet;

    public InspeccionesRepo(APQLitalsaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<Inspecciones>();
    }

    public async Task<List<Inspecciones>> GetInspeccionesPasadasProductos(int? idProducto,
        InspeccionesQueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);

        if (idProducto != null)
            query = query.Where(i => i.IdProducto == idProducto && i.Inspeccion);
        else
            query = query.Where(i => i.Inspeccion);

        return await query.ToListAsync(cancellationToken);
    }
}