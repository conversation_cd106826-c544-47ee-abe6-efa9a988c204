﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

namespace GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;

public partial class Dato01LitaContext : DbContext
{
    public Dato01LitaContext(DbContextOptions<Dato01LitaContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AArticu> AArticu { get; set; }

    public virtual DbSet<AEntdia> AEntdia { get; set; }

    public virtual DbSet<ALotess> ALotess { get; set; }

    public virtual DbSet<ASalida> ASalida { get; set; }

    public virtual DbSet<AViscos> AViscos { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("Traditional_Spanish_CI_AS");

        modelBuilder.Entity<AArticu>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("A_ARTICU", tb =>
                {
                    tb.HasTrigger("TriggerUpdate");
                    tb.HasTrigger("trg_ubicaciones_paquetes");
                });

            entity.HasIndex(e => e.Codigo, "a_artic1").IsUnique();

            entity.HasIndex(e => e.Descrip, "a_artic2");

            entity.HasIndex(e => e.Proveedor, "a_artic3");

            entity.HasIndex(e => e.Obsoleto, "a_artic4");

            entity.HasIndex(e => e.Activare, "a_artic5");

            entity.Property(e => e.Activare)
                .HasMaxLength(1)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ACTIVARE");
            entity.Property(e => e.Ancho).HasColumnName("ANCHO");
            entity.Property(e => e.Barnizfami)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("BARNIZFAMI");
            entity.Property(e => e.Barniznatu)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("BARNIZNATU");
            entity.Property(e => e.Bobcorte)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("BOBCORTE");
            entity.Property(e => e.Bobmargen).HasColumnName("BOBMARGEN");
            entity.Property(e => e.Bobmedida).HasColumnName("BOBMEDIDA");
            entity.Property(e => e.Bobprecio).HasColumnName("BOBPRECIO");
            entity.Property(e => e.Bpa)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("BPA");
            entity.Property(e => e.Calcular)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CALCULAR");
            entity.Property(e => e.Cantped).HasColumnName("CANTPED");
            entity.Property(e => e.Cantpedcli).HasColumnName("CANTPEDCLI");
            entity.Property(e => e.Certifica)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CERTIFICA");
            entity.Property(e => e.Codcli)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODCLI");
            entity.Property(e => e.Codigo)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO");
            entity.Property(e => e.Codigo2)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO2");
            entity.Property(e => e.Codprovee)
                .HasMaxLength(50)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODPROVEE");
            entity.Property(e => e.Coefic).HasColumnName("COEFIC");
            entity.Property(e => e.Comision).HasColumnName("COMISION");
            entity.Property(e => e.Costevari)
                .HasMaxLength(1)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("COSTEVARI");
            entity.Property(e => e.Costoini).HasColumnName("COSTOINI");
            entity.Property(e => e.Costoini1).HasColumnName("COSTOINI1");
            entity.Property(e => e.Cuenta)
                .HasMaxLength(7)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CUENTA");
            entity.Property(e => e.Depo1).HasColumnName("DEPO1");
            entity.Property(e => e.Depo2).HasColumnName("DEPO2");
            entity.Property(e => e.Depo3).HasColumnName("DEPO3");
            entity.Property(e => e.Depo4).HasColumnName("DEPO4");
            entity.Property(e => e.Depo5).HasColumnName("DEPO5");
            entity.Property(e => e.Depo6).HasColumnName("DEPO6");
            entity.Property(e => e.Depo7).HasColumnName("DEPO7");
            entity.Property(e => e.Depo8).HasColumnName("DEPO8");
            entity.Property(e => e.Depo9).HasColumnName("DEPO9");
            entity.Property(e => e.Descrip)
                .HasMaxLength(80)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRIP");
            entity.Property(e => e.Descuento).HasColumnName("DESCUENTO");
            entity.Property(e => e.Dias).HasColumnName("DIAS");
            entity.Property(e => e.Disolven)
                .HasMaxLength(50)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DISOLVEN");
            entity.Property(e => e.Ean13)
                .HasMaxLength(50)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EAN13");
            entity.Property(e => e.Entradas).HasColumnName("ENTRADAS");
            entity.Property(e => e.Espesor).HasColumnName("ESPESOR");
            entity.Property(e => e.Estadis)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ESTADIS");
            entity.Property(e => e.Ext0)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT0");
            entity.Property(e => e.Ext1)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT1");
            entity.Property(e => e.Ext10)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT10");
            entity.Property(e => e.Ext11)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT11");
            entity.Property(e => e.Ext12)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT12");
            entity.Property(e => e.Ext13)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT13");
            entity.Property(e => e.Ext14)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT14");
            entity.Property(e => e.Ext15)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT15");
            entity.Property(e => e.Ext16)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT16");
            entity.Property(e => e.Ext17)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT17");
            entity.Property(e => e.Ext18)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT18");
            entity.Property(e => e.Ext19)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT19");
            entity.Property(e => e.Ext2)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT2");
            entity.Property(e => e.Ext3)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT3");
            entity.Property(e => e.Ext4)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT4");
            entity.Property(e => e.Ext5)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT5");
            entity.Property(e => e.Ext6)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT6");
            entity.Property(e => e.Ext7)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT7");
            entity.Property(e => e.Ext8)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT8");
            entity.Property(e => e.Ext9)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("EXT9");
            entity.Property(e => e.Fabrica).HasColumnName("FABRICA");
            entity.Property(e => e.Fecalta)
                .HasColumnType("datetime")
                .HasColumnName("FECALTA");
            entity.Property(e => e.Fecentra)
                .HasColumnType("datetime")
                .HasColumnName("FECENTRA");
            entity.Property(e => e.Fechab)
                .HasColumnType("datetime")
                .HasColumnName("FECHAB");
            entity.Property(e => e.Fecini)
                .HasColumnType("datetime")
                .HasColumnName("FECINI");
            entity.Property(e => e.Fecreg)
                .HasColumnType("datetime")
                .HasColumnName("FECREG");
            entity.Property(e => e.Fscpor).HasColumnName("FSCPOR");
            entity.Property(e => e.Fscrecicla)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FSCRECICLA");
            entity.Property(e => e.Fsctipo)
                .HasMaxLength(20)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FSCTIPO");
            entity.Property(e => e.Gramaje).HasColumnName("GRAMAJE");
            entity.Property(e => e.Gruprodfsc)
                .HasMaxLength(15)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("GRUPRODFSC");
            entity.Property(e => e.Kgundstock).HasColumnName("KGUNDSTOCK");
            entity.Property(e => e.Largo).HasColumnName("LARGO");
            entity.Property(e => e.Lote)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LOTE");
            entity.Property(e => e.Loteubiobl)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LOTEUBIOBL");
            entity.Property(e => e.Memoria)
                .HasColumnType("ntext")
                .HasColumnName("MEMORIA");
            entity.Property(e => e.Mppt)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MPPT");
            entity.Property(e => e.Nolistado)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOLISTADO");
            entity.Property(e => e.Obslabo)
                .HasMaxLength(254)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("OBSLABO");
            entity.Property(e => e.Obsmaq)
                .HasMaxLength(254)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("OBSMAQ");
            entity.Property(e => e.Obsoleto)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("OBSOLETO");
            entity.Property(e => e.Pcostomed).HasColumnName("PCOSTOMED");
            entity.Property(e => e.Pcostomed1).HasColumnName("PCOSTOMED1");
            entity.Property(e => e.Pedclimin).HasColumnName("PEDCLIMIN");
            entity.Property(e => e.Peso).HasColumnName("PESO");
            entity.Property(e => e.Pesovar).HasColumnName("PESOVAR");
            entity.Property(e => e.Porceplanr).HasColumnName("PORCEPLANR");
            entity.Property(e => e.Precioscrap).HasColumnName("PRECIOSCRAP");
            entity.Property(e => e.Proveedor)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PROVEEDOR");
            entity.Property(e => e.Psiniva).HasColumnName("PSINIVA");
            entity.Property(e => e.Psiniva1).HasColumnName("PSINIVA1");
            entity.Property(e => e.Registro)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REGISTRO");
            entity.Property(e => e.Reserva).HasColumnName("RESERVA");
            entity.Property(e => e.Salidas).HasColumnName("SALIDAS");
            entity.Property(e => e.Sanitario)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("SANITARIO");
            entity.Property(e => e.Stock).HasColumnName("STOCK");
            entity.Property(e => e.Stockini).HasColumnName("STOCKINI");
            entity.Property(e => e.Stockmin).HasColumnName("STOCKMIN");
            entity.Property(e => e.Tempseca1).HasColumnName("TEMPSECA1");
            entity.Property(e => e.Tempseca2).HasColumnName("TEMPSECA2");
            entity.Property(e => e.Tempseca3).HasColumnName("TEMPSECA3");
            entity.Property(e => e.Tempseca4).HasColumnName("TEMPSECA4");
            entity.Property(e => e.Tintabase1)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE1");
            entity.Property(e => e.Tintabase2)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE2");
            entity.Property(e => e.Tintabase3)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE3");
            entity.Property(e => e.Tintabase4)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE4");
            entity.Property(e => e.Tintabase5)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE5");
            entity.Property(e => e.Tintabase6)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TINTABASE6");
            entity.Property(e => e.Tintapeso1).HasColumnName("TINTAPESO1");
            entity.Property(e => e.Tintapeso2).HasColumnName("TINTAPESO2");
            entity.Property(e => e.Tintapeso3).HasColumnName("TINTAPESO3");
            entity.Property(e => e.Tintapeso4).HasColumnName("TINTAPESO4");
            entity.Property(e => e.Tintapeso5).HasColumnName("TINTAPESO5");
            entity.Property(e => e.Tintapeso6).HasColumnName("TINTAPESO6");
            entity.Property(e => e.Tipocomi)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TIPOCOMI");
            entity.Property(e => e.Tipoiva)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TIPOIVA");
            entity.Property(e => e.Tipopresu)
                .HasMaxLength(15)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TIPOPRESU");
            entity.Property(e => e.Txtcompra)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TXTCOMPRA");
            entity.Property(e => e.Txtstock)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TXTSTOCK");
            entity.Property(e => e.Ubica)
                .HasMaxLength(3)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UBICA");
            entity.Property(e => e.Ubicacion)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UBICACION");
            entity.Property(e => e.Udepo1)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO1");
            entity.Property(e => e.Udepo2)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO2");
            entity.Property(e => e.Udepo3)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO3");
            entity.Property(e => e.Udepo4)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO4");
            entity.Property(e => e.Udepo5)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO5");
            entity.Property(e => e.Udepo6)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO6");
            entity.Property(e => e.Udepo7)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO7");
            entity.Property(e => e.Udepo8)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO8");
            entity.Property(e => e.Udepo9)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UDEPO9");
            entity.Property(e => e.Ultalba)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ULTALBA");
            entity.Property(e => e.Ultcant).HasColumnName("ULTCANT");
            entity.Property(e => e.Ultcosto).HasColumnName("ULTCOSTO");
            entity.Property(e => e.Ultcosto0).HasColumnName("ULTCOSTO0");
            entity.Property(e => e.Ultcosto01).HasColumnName("ULTCOSTO01");
            entity.Property(e => e.Ultcosto1).HasColumnName("ULTCOSTO1");
            entity.Property(e => e.Ultcosto11).HasColumnName("ULTCOSTO11");
            entity.Property(e => e.Ultcostoe).HasColumnName("ULTCOSTOE");
            entity.Property(e => e.Undcompra)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDCOMPRA");
            entity.Property(e => e.Undpedido)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDPEDIDO");
            entity.Property(e => e.Undstock)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDSTOCK");
            entity.Property(e => e.Undventa)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDVENTA");
            entity.Property(e => e.Unidades).HasColumnName("UNIDADES");
            entity.Property(e => e.Valentra).HasColumnName("VALENTRA");
            entity.Property(e => e.Valentra1).HasColumnName("VALENTRA1");
            entity.Property(e => e.Valoracion)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("VALORACION");
            entity.Property(e => e.Valsalid).HasColumnName("VALSALID");
            entity.Property(e => e.Valsalid1).HasColumnName("VALSALID1");
            entity.Property(e => e.Velhorno1).HasColumnName("VELHORNO1");
            entity.Property(e => e.Velhorno2).HasColumnName("VELHORNO2");
            entity.Property(e => e.Velhorno3).HasColumnName("VELHORNO3");
            entity.Property(e => e.Velhorno4).HasColumnName("VELHORNO4");
            entity.Property(e => e.Vismax).HasColumnName("VISMAX");
            entity.Property(e => e.Vismin).HasColumnName("VISMIN");
        });

        modelBuilder.Entity<AEntdia>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("A_ENTDIA");

            entity.HasIndex(e => new { e.Proveedor, e.Albaran, e.Codigo, e.Linea }, "a_entdi1").IsUnique();

            entity.HasIndex(e => new { e.Codigo, e.Fecent }, "a_entdi2");

            entity.HasIndex(e => new { e.Codigo, e.Orden }, "a_entdi3");

            entity.HasIndex(e => e.Orden, "a_entdi4");

            entity.Property(e => e.Albaran)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ALBARAN");
            entity.Property(e => e.Ancho).HasColumnName("ANCHO");
            entity.Property(e => e.Bultos).HasColumnName("BULTOS");
            entity.Property(e => e.Calidad1)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CALIDAD1");
            entity.Property(e => e.Calidad2)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CALIDAD2");
            entity.Property(e => e.Cambio).HasColumnName("CAMBIO");
            entity.Property(e => e.Cambio1).HasColumnName("CAMBIO1");
            entity.Property(e => e.Cancela)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CANCELA");
            entity.Property(e => e.Cancelado)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CANCELADO");
            entity.Property(e => e.Canfac).HasColumnName("CANFAC");
            entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");
            entity.Property(e => e.Cantpedido).HasColumnName("CANTPEDIDO");
            entity.Property(e => e.Centroc)
                .HasMaxLength(4)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CENTROC");
            entity.Property(e => e.Codfac)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODFAC");
            entity.Property(e => e.Codigo)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO");
            entity.Property(e => e.Codped)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODPED");
            entity.Property(e => e.Coefic).HasColumnName("COEFIC");
            entity.Property(e => e.Descrip)
                .HasMaxLength(80)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRIP");
            entity.Property(e => e.Descuento).HasColumnName("DESCUENTO");
            entity.Property(e => e.Escliente)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ESCLIENTE");
            entity.Property(e => e.Espesor).HasColumnName("ESPESOR");
            entity.Property(e => e.Estadis)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ESTADIS");
            entity.Property(e => e.Fecent)
                .HasColumnType("datetime")
                .HasColumnName("FECENT");
            entity.Property(e => e.Fechalote)
                .HasColumnType("datetime")
                .HasColumnName("FECHALOTE");
            entity.Property(e => e.Fentrega)
                .HasColumnType("datetime")
                .HasColumnName("FENTREGA");
            entity.Property(e => e.Gramaje).HasColumnName("GRAMAJE");
            entity.Property(e => e.Humedad).HasColumnName("HUMEDAD");
            entity.Property(e => e.Importe).HasColumnName("IMPORTE");
            entity.Property(e => e.Importe1).HasColumnName("IMPORTE1");
            entity.Property(e => e.Iva).HasColumnName("IVA");
            entity.Property(e => e.Kgsalbaran).HasColumnName("KGSALBARAN");
            entity.Property(e => e.Largo).HasColumnName("LARGO");
            entity.Property(e => e.Linea)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LINEA");
            entity.Property(e => e.Lineaped)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LINEAPED");
            entity.Property(e => e.Litaprodpr)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LITAPRODPR");
            entity.Property(e => e.Lote)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LOTE");
            entity.Property(e => e.Moneda)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MONEDA");
            entity.Property(e => e.Moneda1)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MONEDA1");
            entity.Property(e => e.Numvale)
                .HasMaxLength(11)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NUMVALE");
            entity.Property(e => e.Operacion)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("OPERACION");
            entity.Property(e => e.Operario)
                .HasMaxLength(6)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("OPERARIO");
            entity.Property(e => e.Orden)
                .HasMaxLength(7)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ORDEN");
            entity.Property(e => e.Precio).HasColumnName("PRECIO");
            entity.Property(e => e.Precio1).HasColumnName("PRECIO1");
            entity.Property(e => e.Proveedor)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PROVEEDOR");
            entity.Property(e => e.Recargo).HasColumnName("RECARGO");
            entity.Property(e => e.Redondeo).HasColumnName("REDONDEO");
            entity.Property(e => e.Redondeo1).HasColumnName("REDONDEO1");
            entity.Property(e => e.Referencia)
                .HasMaxLength(20)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REFERENCIA");
            entity.Property(e => e.Registro)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REGISTRO");
            entity.Property(e => e.Sscc)
                .HasMaxLength(21)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("SSCC");
            entity.Property(e => e.Tipiva)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TIPIVA");
            entity.Property(e => e.Ubicacion)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UBICACION");
            entity.Property(e => e.Undcompra)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDCOMPRA");
            entity.Property(e => e.Undpedido)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDPEDIDO");
            entity.Property(e => e.Undstock)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDSTOCK");
            entity.Property(e => e.Unidades).HasColumnName("UNIDADES");
        });

        modelBuilder.Entity<ALotess>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("A_LOTESS");

            entity.HasIndex(e => new { e.Codigo, e.Lote, e.Ubicacion }, "a_lotes1").IsUnique();

            entity.HasIndex(e => e.Lote, "a_lotes2");

            entity.HasIndex(e => e.Codigo, "a_lotes3");

            entity.Property(e => e.Altura).HasColumnName("ALTURA");
            entity.Property(e => e.Codigo)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO");
            entity.Property(e => e.Coefic).HasColumnName("COEFIC");
            entity.Property(e => e.Fechalote)
                .HasColumnType("datetime")
                .HasColumnName("FECHALOTE");
            entity.Property(e => e.Lote)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LOTE");
            entity.Property(e => e.Reserva).HasColumnName("RESERVA");
            entity.Property(e => e.Sscc)
                .HasMaxLength(21)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("SSCC");
            entity.Property(e => e.Stock).HasColumnName("STOCK");
            entity.Property(e => e.Ubicacion)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UBICACION");
        });

        modelBuilder.Entity<ASalida>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("A_SALIDA", tb => tb.HasTrigger("TRIG_TAI_PAQUETES_PEDIDOS"));

            entity.HasIndex(e => new { e.Numero, e.Linea }, "a_salid1").IsUnique();

            entity.HasIndex(e => new { e.Cliente, e.Numero, e.Linea }, "a_salid2");

            entity.HasIndex(e => e.Codigo, "a_salid3");

            entity.HasIndex(e => new { e.Prealba, e.Espesor }, "a_salid4");

            entity.HasIndex(e => new { e.Codigo, e.Numero }, "a_salid5");

            entity.Property(e => e.Albaranf)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ALBARANF");
            entity.Property(e => e.Almacen)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ALMACEN");
            entity.Property(e => e.Almacen0)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ALMACEN0");
            entity.Property(e => e.Ancho).HasColumnName("ANCHO");
            entity.Property(e => e.Bloqueo)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("BLOQUEO");
            entity.Property(e => e.Bultos).HasColumnName("BULTOS");
            entity.Property(e => e.Cambio).HasColumnName("CAMBIO");
            entity.Property(e => e.Cambio1).HasColumnName("CAMBIO1");
            entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");
            entity.Property(e => e.Cantidad0).HasColumnName("CANTIDAD0");
            entity.Property(e => e.Cantidadc).HasColumnName("CANTIDADC");
            entity.Property(e => e.Cliente)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CLIENTE");
            entity.Property(e => e.Clienteok)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CLIENTEOK");
            entity.Property(e => e.Codent)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODENT");
            entity.Property(e => e.Codigo)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO");
            entity.Property(e => e.Codiprov)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIPROV");
            entity.Property(e => e.Coefic).HasColumnName("COEFIC");
            entity.Property(e => e.Comision).HasColumnName("COMISION");
            entity.Property(e => e.Costosal).HasColumnName("COSTOSAL");
            entity.Property(e => e.Costosal1).HasColumnName("COSTOSAL1");
            entity.Property(e => e.Descri1)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI1");
            entity.Property(e => e.Descri2)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI2");
            entity.Property(e => e.Descri3)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI3");
            entity.Property(e => e.Descri4)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI4");
            entity.Property(e => e.Descri5)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI5");
            entity.Property(e => e.Descri6)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI6");
            entity.Property(e => e.Descri7)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRI7");
            entity.Property(e => e.Descrip)
                .HasMaxLength(80)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DESCRIP");
            entity.Property(e => e.Descuento).HasColumnName("DESCUENTO");
            entity.Property(e => e.Direccio21)
                .HasMaxLength(40)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DIRECCIO21");
            entity.Property(e => e.Direccion2)
                .HasMaxLength(40)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("DIRECCION2");
            entity.Property(e => e.Espesor).HasColumnName("ESPESOR");
            entity.Property(e => e.Estadis)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ESTADIS");
            entity.Property(e => e.Factura)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FACTURA");
            entity.Property(e => e.Facturado)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FACTURADO");
            entity.Property(e => e.Fechalote)
                .HasColumnType("datetime")
                .HasColumnName("FECHALOTE");
            entity.Property(e => e.Fecsal)
                .HasColumnType("datetime")
                .HasColumnName("FECSAL");
            entity.Property(e => e.Fotra)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FOTRA");
            entity.Property(e => e.Fpago)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("FPAGO");
            entity.Property(e => e.Gramaje).HasColumnName("GRAMAJE");
            entity.Property(e => e.Imporneto).HasColumnName("IMPORNETO");
            entity.Property(e => e.Imporneto1).HasColumnName("IMPORNETO1");
            entity.Property(e => e.Importe).HasColumnName("IMPORTE");
            entity.Property(e => e.Importe1).HasColumnName("IMPORTE1");
            entity.Property(e => e.Iva).HasColumnName("IVA");
            entity.Property(e => e.Largo).HasColumnName("LARGO");
            entity.Property(e => e.Linea)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LINEA");
            entity.Property(e => e.Lineaped)
                .HasMaxLength(6)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LINEAPED");
            entity.Property(e => e.Lote)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("LOTE");
            entity.Property(e => e.Matricula)
                .HasMaxLength(20)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MATRICULA");
            entity.Property(e => e.Moneda)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MONEDA");
            entity.Property(e => e.Moneda1)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("MONEDA1");
            entity.Property(e => e.Nombre2)
                .HasMaxLength(50)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOMBRE2");
            entity.Property(e => e.Nota1)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOTA1");
            entity.Property(e => e.Nota2)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOTA2");
            entity.Property(e => e.Nota3)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOTA3");
            entity.Property(e => e.Nota4)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOTA4");
            entity.Property(e => e.Nota5)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NOTA5");
            entity.Property(e => e.Numero)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NUMERO");
            entity.Property(e => e.Numero2)
                .HasMaxLength(5)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("NUMERO2");
            entity.Property(e => e.Pais2)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PAIS2");
            entity.Property(e => e.Palet).HasColumnName("PALET");
            entity.Property(e => e.Parte)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PARTE");
            entity.Property(e => e.Pcostomed).HasColumnName("PCOSTOMED");
            entity.Property(e => e.Pcostomed1).HasColumnName("PCOSTOMED1");
            entity.Property(e => e.Pedido)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PEDIDO");
            entity.Property(e => e.Poblacion2)
                .HasMaxLength(30)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("POBLACION2");
            entity.Property(e => e.Porcen1).HasColumnName("PORCEN1");
            entity.Property(e => e.Porcen2).HasColumnName("PORCEN2");
            entity.Property(e => e.Porcen3).HasColumnName("PORCEN3");
            entity.Property(e => e.Porcen4).HasColumnName("PORCEN4");
            entity.Property(e => e.Porcen5).HasColumnName("PORCEN5");
            entity.Property(e => e.Porcen6).HasColumnName("PORCEN6");
            entity.Property(e => e.Portes)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PORTES");
            entity.Property(e => e.Posta2)
                .HasMaxLength(15)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("POSTA2");
            entity.Property(e => e.Prealba)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PREALBA");
            entity.Property(e => e.Precio).HasColumnName("PRECIO");
            entity.Property(e => e.Precio1).HasColumnName("PRECIO1");
            entity.Property(e => e.Provinci2)
                .HasMaxLength(20)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("PROVINCI2");
            entity.Property(e => e.Redondeo).HasColumnName("REDONDEO");
            entity.Property(e => e.Redondeo1).HasColumnName("REDONDEO1");
            entity.Property(e => e.Refpedcli)
                .HasMaxLength(100)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REFPEDCLI");
            entity.Property(e => e.Registro)
                .HasMaxLength(9)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REGISTRO");
            entity.Property(e => e.Represen)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("REPRESEN");
            entity.Property(e => e.Sscc)
                .HasMaxLength(21)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("SSCC");
            entity.Property(e => e.Tbultos).HasColumnName("TBULTOS");
            entity.Property(e => e.Texto)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TEXTO");
            entity.Property(e => e.Tipiva)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TIPIVA");
            entity.Property(e => e.Transpo)
                .HasMaxLength(3)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TRANSPO");
            entity.Property(e => e.Transpor)
                .HasMaxLength(40)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("TRANSPOR");
            entity.Property(e => e.Ubicacion)
                .HasMaxLength(10)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UBICACION");
            entity.Property(e => e.Undcompra)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDCOMPRA");
            entity.Property(e => e.Undstock)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDSTOCK");
            entity.Property(e => e.Undventa)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNDVENTA");
            entity.Property(e => e.Unidad)
                .HasMaxLength(1)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("UNIDAD");
            entity.Property(e => e.Venci1).HasColumnName("VENCI1");
            entity.Property(e => e.Venci2).HasColumnName("VENCI2");
            entity.Property(e => e.Venci3).HasColumnName("VENCI3");
            entity.Property(e => e.Venci4).HasColumnName("VENCI4");
            entity.Property(e => e.Venci5).HasColumnName("VENCI5");
            entity.Property(e => e.Venci6).HasColumnName("VENCI6");
            entity.Property(e => e.Volcado).HasColumnName("VOLCADO");
            entity.Property(e => e.Zona)
                .HasMaxLength(2)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("ZONA");
        });

        modelBuilder.Entity<AViscos>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("A_VISCOS");

            entity.Property(e => e.Codigo)
                .HasMaxLength(25)
                .UseCollation("SQL_Latin1_General_CP437_BIN")
                .HasColumnName("CODIGO");
            entity.Property(e => e.Temp).HasColumnName("TEMP");
            entity.Property(e => e.Vmax).HasColumnName("VMAX");
            entity.Property(e => e.Vmin).HasColumnName("VMIN");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}