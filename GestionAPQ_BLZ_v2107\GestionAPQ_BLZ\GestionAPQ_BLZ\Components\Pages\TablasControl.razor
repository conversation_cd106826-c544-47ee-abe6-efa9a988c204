@page "/TablasControl"

<PageTitle>Tablas Control</PageTitle>

<div class="d-flex flex-column h-100 overflow-x-hidden">
	<div class="container">
		<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded shadow-sm flex-shrink-0">
			<div class="d-flex align-items-center flex-grow-1">
				<h1 class="h5 fw-bold mb-0 text-primary me-2 flex-shrink-0">
					<i class="bi bi-table me-1"></i>
					TABLAS CONTROL
				</h1>
			</div>
		</div>
	</div>
	<div class="flex-grow-1 overflow-hidden pt-3 h-100">
		<div class="w-100 h-100">
			<DxFormLayout CssClass="h-100">
				<DxFormLayoutTabPages CssClass="h-100">
					<DxFormLayoutTabPage Caption="Resultado Inspecciones" CssClass="h-100">
						<div class="w-100" style="height: calc(100vh - 215px);">
							<ResultadosInspecciones />
						</div>
					</DxFormLayoutTabPage>
				</DxFormLayoutTabPages>
			</DxFormLayout>
		</div>
	</div>
</div>

@code {
}
