﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GestionAQP_BLZ.Server.Data.Entities.DatoLita01;

public partial class AArticu
{
    public string Codigo { get; set; }

    public string Descrip { get; set; }

    public string Proveedor { get; set; }

    public string Ubica { get; set; }

    public string Tipoiva { get; set; }

    public double? Psiniva { get; set; }

    public double? Psiniva1 { get; set; }

    public double? Descuento { get; set; }

    public double? Stock { get; set; }

    public double? Stockmin { get; set; }

    public double? Stockini { get; set; }

    public double? Entradas { get; set; }

    public double? Salidas { get; set; }

    public double? Valentra { get; set; }

    public double? Valentra1 { get; set; }

    public double? Valsalid { get; set; }

    public double? Valsalid1 { get; set; }

    public double? Costoini { get; set; }

    public double? Costoini1 { get; set; }

    public DateTime? Fecalta { get; set; }

    public DateTime? Fecentra { get; set; }

    public string <PERSON><PERSON>lba { get; set; }

    public double? Ultcosto { get; set; }

    public double? Ultcostoe { get; set; }

    public double? Ultcosto0 { get; set; }

    public double? Ultcosto01 { get; set; }

    public double? Ultcosto1 { get; set; }

    public double? Ultcosto11 { get; set; }

    public double? Ultcant { get; set; }

    public double? Pcostomed { get; set; }

    public double? Pcostomed1 { get; set; }

    public double? Cantped { get; set; }

    public double? Reserva { get; set; }

    public double? Fabrica { get; set; }

    public string Estadis { get; set; }

    public double? Depo1 { get; set; }

    public double? Depo2 { get; set; }

    public double? Depo3 { get; set; }

    public double? Depo4 { get; set; }

    public double? Depo5 { get; set; }

    public double? Depo6 { get; set; }

    public double? Depo7 { get; set; }

    public double? Depo8 { get; set; }

    public double? Depo9 { get; set; }

    public string Valoracion { get; set; }

    public string Cuenta { get; set; }

    public string Undstock { get; set; }

    public string Undcompra { get; set; }

    public string Undventa { get; set; }

    public string Txtstock { get; set; }

    public string Txtcompra { get; set; }

    public double? Largo { get; set; }

    public double? Ancho { get; set; }

    public double? Espesor { get; set; }

    public double? Gramaje { get; set; }

    public double? Coefic { get; set; }

    public DateTime? Fecini { get; set; }

    public string Udepo1 { get; set; }

    public string Udepo2 { get; set; }

    public string Udepo3 { get; set; }

    public string Udepo4 { get; set; }

    public string Udepo5 { get; set; }

    public string Udepo6 { get; set; }

    public string Udepo7 { get; set; }

    public string Udepo8 { get; set; }

    public string Udepo9 { get; set; }

    public string Registro { get; set; }

    public string Tipopresu { get; set; }

    public string Mppt { get; set; }

    public double? Comision { get; set; }

    public string Tipocomi { get; set; }

    public string Undpedido { get; set; }

    public double? Unidades { get; set; }

    public string Ean13 { get; set; }

    public string Codcli { get; set; }

    public string Obsoleto { get; set; }

    public string Memoria { get; set; }

    public string Codigo2 { get; set; }

    public string Codprovee { get; set; }

    public string Bobcorte { get; set; }

    public double? Bobmedida { get; set; }

    public double? Bobmargen { get; set; }

    public double? Bobprecio { get; set; }

    public string Fsctipo { get; set; }

    public double? Fscpor { get; set; }

    public string Fscrecicla { get; set; }

    public string Certifica { get; set; }

    public double? Dias { get; set; }

    public DateTime? Fecreg { get; set; }

    public string Calcular { get; set; }

    public string Ext0 { get; set; }

    public string Ext1 { get; set; }

    public string Ext2 { get; set; }

    public string Ext3 { get; set; }

    public string Ext4 { get; set; }

    public string Ext5 { get; set; }

    public string Ext6 { get; set; }

    public string Ext7 { get; set; }

    public string Ext8 { get; set; }

    public string Ext9 { get; set; }

    public string Ext10 { get; set; }

    public string Ext11 { get; set; }

    public string Ext12 { get; set; }

    public string Ext13 { get; set; }

    public string Ext14 { get; set; }

    public string Ext15 { get; set; }

    public string Tintabase1 { get; set; }

    public string Tintabase2 { get; set; }

    public string Tintabase3 { get; set; }

    public string Tintabase4 { get; set; }

    public string Tintabase5 { get; set; }

    public string Tintabase6 { get; set; }

    public double? Tintapeso1 { get; set; }

    public double? Tintapeso2 { get; set; }

    public double? Tintapeso3 { get; set; }

    public double? Tintapeso4 { get; set; }

    public double? Tintapeso5 { get; set; }

    public double? Tintapeso6 { get; set; }

    public string Bpa { get; set; }

    public string Sanitario { get; set; }

    public string Nolistado { get; set; }

    public double? Vismin { get; set; }

    public double? Vismax { get; set; }

    public double? Peso { get; set; }

    public double? Pesovar { get; set; }

    public double? Tempseca1 { get; set; }

    public double? Velhorno1 { get; set; }

    public double? Velhorno2 { get; set; }

    public string Disolven { get; set; }

    public string Obslabo { get; set; }

    public string Obsmaq { get; set; }

    public DateTime? Fechab { get; set; }

    public double? Tempseca2 { get; set; }

    public double? Cantpedcli { get; set; }

    public string Lote { get; set; }

    public string Ubicacion { get; set; }

    public string Barniznatu { get; set; }

    public string Barnizfami { get; set; }

    public string Ext16 { get; set; }

    public string Ext17 { get; set; }

    public string Ext18 { get; set; }

    public string Ext19 { get; set; }

    public double? Tempseca3 { get; set; }

    public double? Tempseca4 { get; set; }

    public double? Velhorno3 { get; set; }

    public double? Velhorno4 { get; set; }

    public string Loteubiobl { get; set; }

    public string Activare { get; set; }

    public double? Pedclimin { get; set; }

    public double? Porceplanr { get; set; }

    public string Costevari { get; set; }

    public double? Precioscrap { get; set; }

    public double? Kgundstock { get; set; }

    public string Gruprodfsc { get; set; }
}