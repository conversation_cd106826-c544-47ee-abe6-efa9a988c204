{"version": 3, "targets": {"net8.0": {"Azure.Core/1.38.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.11.4": {"type": "package", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Ben.Demystifier/0.4.1": {"type": "package", "dependencies": {"System.Reflection.Metadata": "5.0.0"}, "compile": {"lib/netstandard2.1/Ben.Demystifier.dll": {}}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {}}}, "Blazr.RenderState/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.0"}, "compile": {"lib/net8.0/Blazr.RenderState.dll": {}}, "runtime": {"lib/net8.0/Blazr.RenderState.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Blazr.RenderState.Server/1.0.0": {"type": "package", "dependencies": {"Blazr.RenderState": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "compile": {"lib/net8.0/Blazr.RenderState.Server.dll": {"related": ".runtimeconfig.json"}}, "runtime": {"lib/net8.0/Blazr.RenderState.Server.dll": {"related": ".runtimeconfig.json"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Common.ResponseModels/1.0.0": {"type": "package", "compile": {"lib/net8.0/Common.ResponseModels.dll": {}}, "runtime": {"lib/net8.0/Common.ResponseModels.dll": {}}}, "DevExpress.AspNetCore.Common/24.2.8": {"type": "package", "dependencies": {"DevExpress.AspNetCore.Core": "[24.2.8]", "DevExpress.Data": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.AspNetCore.Common.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Common.v24.2.dll": {"related": ".xml"}}}, "DevExpress.AspNetCore.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Office.Core": "[24.2.8]", "Microsoft.Extensions.DependencyModel": "8.0.2"}, "compile": {"lib/net8.0/DevExpress.AspNetCore.Core.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Core.v24.2.dll": {"related": ".xml"}}}, "DevExpress.AspNetCore.Reporting/24.2.8": {"type": "package", "dependencies": {"DevExpress.AspNetCore.Common": "[24.2.8]", "DevExpress.AspNetCore.Core": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.DataAccess": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.Reporting.Core": "[24.2.8]", "DevExpress.Web.Reporting.Common": "[24.2.8]", "DevExpress.Web.Reporting.Common.Services": "[24.2.8]", "DevExpress.Web.Resources": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.AspNetCore.Reporting.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Reporting.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Blazor/24.2.8": {"type": "package", "dependencies": {"DevExpress.Blazor.Resources": "[24.2.8]", "DevExpress.Blazor.Themes": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExtreme.AspNet.Data": "4.0.0", "System.Drawing.Common": "4.7.2", "System.Reactive": "5.0.0"}, "compile": {"lib/net8.0/DevExpress.Blazor.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Blazor.v24.2.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/DevExpress.Blazor.props": {}, "buildTransitive/net5.0/DevExpress.Blazor.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Blazor.props": {}}}, "DevExpress.Blazor.Reporting.JSBasedControls/24.2.8": {"type": "package", "dependencies": {"DevExpress.Blazor.Reporting.JSBasedControls.Common": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.DataAccess": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.Reporting.Core": "[24.2.8]", "DevExpress.Web.Reporting.Common": "[24.2.8]", "DevExpress.Web.Reporting.Common.Services": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.dll": {"related": ".xml"}}, "build": {"buildTransitive/DevExpress.Blazor.Reporting.JSBasedControls.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Blazor.Reporting.JSBasedControls.props": {}}}, "DevExpress.Blazor.Reporting.JSBasedControls.Common/24.2.8": {"type": "package", "dependencies": {"DevExpress.Blazor.Resources": "[24.2.8]", "DevExpress.Data": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/DevExpress.Blazor.Reporting.JSBasedControls.Common.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Blazor.Reporting.JSBasedControls.Common.props": {}}}, "DevExpress.Blazor.Resources/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Blazor.Resources.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.Blazor.Resources.v24.2.dll": {}}, "build": {"buildTransitive/DevExpress.Blazor.Resources.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Blazor.Resources.props": {}}}, "DevExpress.Blazor.Themes/24.2.8": {"type": "package", "build": {"buildTransitive/DevExpress.Blazor.Themes.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Blazor.Themes.props": {}}}, "DevExpress.Charts/24.2.8": {"type": "package", "dependencies": {"DevExpress.Charts.Core": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.DataVisualization.Core": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.XtraCharts.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraCharts.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Charts.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.CodeParser/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "System.CodeDom": "4.4.0"}, "compile": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {}}}, "DevExpress.Data/24.2.8": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".xml"}}}, "DevExpress.DataAccess/24.2.8": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Office.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.RichEdit.Core": "[24.2.8]", "DevExpress.Xpo": "[24.2.8]", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.SqlClient": "4.8.6"}, "compile": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"related": ".xml"}}}, "DevExpress.DataVisualization.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {}}, "runtime": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {}}}, "DevExpress.Drawing/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Gauges.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.XtraGauges.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraGauges.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Office.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Pdf.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Drawing/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Pdf.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}}, "DevExpress.PivotGrid.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "System.Data.OleDb": "8.0.1", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.PivotGrid.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.PivotGrid.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Printing.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Pdf.Core": "[24.2.8]", "DevExpress.Pdf.Drawing": "[24.2.8]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0"}, "compile": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Reporting.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Charts": "[24.2.8]", "DevExpress.Charts.Core": "[24.2.8]", "DevExpress.CodeParser": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.DataAccess": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Gauges.Core": "[24.2.8]", "DevExpress.Office.Core": "[24.2.8]", "DevExpress.Pdf.Core": "[24.2.8]", "DevExpress.Pdf.Drawing": "[24.2.8]", "DevExpress.PivotGrid.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.RichEdit.Core": "[24.2.8]", "DevExpress.RichEdit.Export": "[24.2.8]", "DevExpress.Sparkline.Core": "[24.2.8]", "DevExpress.Xpo": "[24.2.8]", "System.CodeDom": "4.4.0", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.XtraReports.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v24.2.dll": {"related": ".xml"}}, "build": {"buildTransitive/DevExpress.Reporting.Core.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DevExpress.Reporting.Core.targets": {}}}, "DevExpress.RichEdit.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Office.Core": "[24.2.8]", "DevExpress.Pdf.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.RichEdit.Export/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Office.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.RichEdit.Core": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v24.2.Export.dll": {}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Export.dll": {}}}, "DevExpress.Sparkline.Core/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Sparkline.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Sparkline.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Web.Reporting.Common/24.2.8": {"type": "package", "dependencies": {"DevExpress.Charts": "[24.2.8]", "DevExpress.Charts.Core": "[24.2.8]", "DevExpress.Data": "[24.2.8]", "DevExpress.DataAccess": "[24.2.8]", "DevExpress.Drawing": "[24.2.8]", "DevExpress.Gauges.Core": "[24.2.8]", "DevExpress.PivotGrid.Core": "[24.2.8]", "DevExpress.Printing.Core": "[24.2.8]", "DevExpress.Reporting.Core": "[24.2.8]", "DevExpress.Sparkline.Core": "[24.2.8]", "DevExpress.Xpo": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.XtraReports.v24.2.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v24.2.Web.dll": {"related": ".xml"}}}, "DevExpress.Web.Reporting.Common.Services/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "DevExpress.DataAccess": "[24.2.8]", "DevExpress.Reporting.Core": "[24.2.8]", "DevExpress.Web.Reporting.Common": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Web.Reporting.v24.2.Common.Services.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Web.Reporting.v24.2.Common.Services.dll": {"related": ".xml"}}}, "DevExpress.Web.Resources/24.2.8": {"type": "package", "dependencies": {"DevExpress.AspNetCore.Core": "[24.2.8]", "DevExpress.Data": "[24.2.8]"}, "compile": {"lib/net8.0/DevExpress.Web.Resources.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.Web.Resources.v24.2.dll": {}}}, "DevExpress.Xpo/24.2.8": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.8]", "Microsoft.Extensions.DependencyInjection": "6.0.0", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0"}, "compile": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"related": ".xml"}}}, "DevExtreme.AspNet.Data/4.0.0": {"type": "package", "compile": {"lib/net6.0/DevExtreme.AspNet.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/DevExtreme.AspNet.Data.dll": {"related": ".xml"}}}, "Logging.Shared/1.0.0": {"type": "package", "dependencies": {"MediatR": "12.5.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Serilog": "4.3.0", "Serilog.AspNetCore": "8.0.3", "Serilog.Enrichers.AspNetCore": "1.0.0", "Serilog.Enrichers.AspNetCore.HttpContext": "1.0.1", "Serilog.Enrichers.CallerInfo": "1.0.5", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Exceptions.EntityFrameworkCore": "8.4.0", "Serilog.Sinks.Async": "2.1.0", "Serilog.Sinks.MSSqlServer": "8.2.0"}, "compile": {"lib/net8.0/Logging.Shared.dll": {}}, "runtime": {"lib/net8.0/Logging.Shared.dll": {}}}, "MediatR/12.5.0": {"type": "package", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}}, "MediatR.Contracts/2.0.1": {"type": "package", "compile": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/8.0.15": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.15", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/8.0.15": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.15", "Microsoft.AspNetCore.Components.Analyzers": "8.0.15"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.15"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.15", "Microsoft.AspNetCore.Components.Forms": "8.0.15", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.15", "System.IO.Pipelines": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.15": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.15", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.JSInterop.WebAssembly": "8.0.15"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"related": ".xml"}}, "build": {"build/net8.0/_._": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/8.0.15": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"build/Microsoft.AspNetCore.Components.WebAssembly.Server.targets": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/8.0.15": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.0.3": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.0.2", "Microsoft.Net.Http.Headers": "2.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.0.2", "System.Text.Encodings.Web": "4.4.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.EntityFrameworkCore/8.0.15": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.15", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.15", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.15": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.15": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.15": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.15", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.15": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "8.0.15", "System.Formats.Asn1": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.JSInterop/8.0.15": {"type": "package", "compile": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Microsoft.JSInterop.WebAssembly/8.0.15": {"type": "package", "dependencies": {"Microsoft.JSInterop": "8.0.15"}, "compile": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0", "System.Buffers": "4.4.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.4": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Serilog/4.3.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "Serilog.AspNetCore/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.4", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/net8.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Enrichers.AspNetCore/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.AspNetCore.Mvc.Abstractions": "2.0.3", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Serilog": "2.6.0"}, "compile": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.dll": {}}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.dll": {}}}, "Serilog.Enrichers.AspNetCore.HttpContext/1.0.1": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.0", "Microsoft.Extensions.Options": "2.0.0", "Serilog": "2.5.0"}, "compile": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.HttpContext.dll": {}}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.HttpContext.dll": {}}}, "Serilog.Enrichers.CallerInfo/1.0.5": {"type": "package", "dependencies": {"Ben.Demystifier": "0.4.1", "Serilog": "3.0.1"}, "compile": {"lib/net6.0/Serilog.Enrichers.CallerInfo.dll": {}}, "runtime": {"lib/net6.0/Serilog.Enrichers.CallerInfo.dll": {}}}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}}, "Serilog.Exceptions/8.4.0": {"type": "package", "dependencies": {"Serilog": "2.8.0", "System.Reflection.TypeExtensions": "4.7.0"}, "compile": {"lib/net6.0/Serilog.Exceptions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Serilog.Exceptions.dll": {"related": ".pdb;.xml"}}}, "Serilog.Exceptions.EntityFrameworkCore/8.4.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "6.0.0", "Serilog.Exceptions": "8.4.0"}, "compile": {"lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.dll": {"related": ".pdb;.xml"}}}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "compile": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1"}, "compile": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.0"}, "compile": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/8.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.2", "Serilog": "3.1.1"}, "compile": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Async/2.1.0": {"type": "package", "dependencies": {"Serilog": "4.1.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.0"}, "compile": {"lib/net7.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "Serilog.Sinks.MSSqlServer/8.2.0": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Serilog": "4.1.0", "System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/Serilog.Sinks.MSSqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.MSSqlServer.dll": {"related": ".xml"}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ClientModel/1.0.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.3.0": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.OleDb/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "compile": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/4.7.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/8.0.2": {"type": "package", "compile": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Reflection.Emit.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/8.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Http/6.2.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "compile": {"lib/net6.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "resource": {"lib/net6.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/6.2.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "compile": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {}}, "resource": {"lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/6.2.0": {"type": "package", "dependencies": {"System.ServiceModel.NetFramingBase": "6.2.0", "System.ServiceModel.Primitives": "6.2.0"}, "compile": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}, "resource": {"lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/6.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"ref/net6.0/System.ServiceModel.Primitives.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}}, "resource": {"lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "compile": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TinyMapper/3.0.3": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0"}, "compile": {"lib/netstandard1.3/TinyMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/TinyMapper.dll": {"related": ".xml"}}}, "GestionAPQ_BLZ.Client/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": "8.0.15"}, "compile": {"bin/placeholder/GestionAPQ_BLZ.Client.dll": {}}, "runtime": {"bin/placeholder/GestionAPQ_BLZ.Client.dll": {}}}}}, "libraries": {"Azure.Core/1.38.0": {"sha512": "IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "type": "package", "path": "azure.core/1.38.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.38.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.11.4": {"sha512": "Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "type": "package", "path": "azure.identity/1.11.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.11.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Ben.Demystifier/0.4.1": {"sha512": "axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "type": "package", "path": "ben.demystifier/0.4.1", "files": [".nupkg.metadata", ".signature.p7s", "ben.demystifier.0.4.1.nupkg.sha512", "ben.demystifier.nuspec", "icon.png", "lib/net45/Ben.Demystifier.dll", "lib/netstandard2.0/Ben.Demystifier.dll", "lib/netstandard2.1/Ben.Demystifier.dll", "readme.md"]}, "Blazr.RenderState/1.0.0": {"sha512": "JMT7jZsTWHVs1gwPOjEvnlBkLZiB6vWjTV8S6hcO4fkXxtj1/cHMDyjwGTfalXmthkCs3RrI+5l0dV5ct92/1g==", "type": "package", "path": "blazr.renderstate/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Package.md", "blazr.renderstate.1.0.0.nupkg.sha512", "blazr.renderstate.nuspec", "content/Properties/launchSettings.json", "contentFiles/any/net8.0/Properties/launchSettings.json", "lib/net8.0/Blazr.RenderState.dll", "license.txt"]}, "Blazr.RenderState.Server/1.0.0": {"sha512": "ZBwIFm2lDui7JC2684+9sIiZ6ycEvrX4WSDit1q2qHlauDmXND5+9LbeuX8Hi4NhO9GwvgycnGVXL9l3hAhUZg==", "type": "package", "path": "blazr.renderstate.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Package.md", "blazr.renderstate.server.1.0.0.nupkg.sha512", "blazr.renderstate.server.nuspec", "lib/net8.0/Blazr.RenderState.Server.dll", "lib/net8.0/Blazr.RenderState.Server.runtimeconfig.json", "license.txt"]}, "Common.ResponseModels/1.0.0": {"sha512": "WNMbfi6SkIO1T7wMqLSgdNWJlBvjOYSx+ndiRnuhciGtURSos2kj7mjYqaFlubbi9jcMQfZbFBRmZyZu+nwjcA==", "type": "package", "path": "common.responsemodels/1.0.0", "files": [".nupkg.metadata", "common.responsemodels.1.0.0.nupkg.sha512", "common.responsemodels.nuspec", "lib/net8.0/Common.ResponseModels.dll"]}, "DevExpress.AspNetCore.Common/24.2.8": {"sha512": "dM6uiCqiZ0XWW0F1i2Je1oRZvSsmRAAQnhaVUXIrKr6fgvAmtq9YFhCL1tWA+BAFxM44cLSaTvkekWS7VD7Xhg==", "type": "package", "path": "devexpress.aspnetcore.common/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.aspnetcore.common.24.2.8.nupkg.sha512", "devexpress.aspnetcore.common.nuspec", "lib/net8.0/DevExpress.AspNetCore.Common.v24.2.dll", "lib/net8.0/DevExpress.AspNetCore.Common.v24.2.xml"]}, "DevExpress.AspNetCore.Core/24.2.8": {"sha512": "Mx9RwHIbYguKRHIgkFpvxjiavvoG7HJIShrd08k5GGpexkOuzbCJbbWVvHMOE7zXRyRQC7vRYdpv1jKReha1zQ==", "type": "package", "path": "devexpress.aspnetcore.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.aspnetcore.core.24.2.8.nupkg.sha512", "devexpress.aspnetcore.core.nuspec", "lib/net8.0/DevExpress.AspNetCore.Core.v24.2.dll", "lib/net8.0/DevExpress.AspNetCore.Core.v24.2.xml"]}, "DevExpress.AspNetCore.Reporting/24.2.8": {"sha512": "Grd0qCXGoJ/jpIuysyNUjh6xhlSnjlyfS974QpzMv+nJt9vAkOtf32jUdmPGb2S4s8H+Kr//7t3mxj5q9aHSuA==", "type": "package", "path": "devexpress.aspnetcore.reporting/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.aspnetcore.reporting.24.2.8.nupkg.sha512", "devexpress.aspnetcore.reporting.nuspec", "lib/net8.0/DevExpress.AspNetCore.Reporting.v24.2.dll", "lib/net8.0/DevExpress.AspNetCore.Reporting.v24.2.xml"]}, "DevExpress.Blazor/24.2.8": {"sha512": "Y2BQIp+SF2mGlW1cPI+Hva/job0/ksV+tAUoGa3Kho3QysJu21ufuhfg253gpDc6g0FNPAVCgIrPTKVtcenr8g==", "type": "package", "path": "devexpress.blazor/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Blazor.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/DevExpress.Blazor.props", "buildTransitive/DevExpress.Blazor.props", "buildTransitive/net5.0/DevExpress.Blazor.props", "buildTransitive/net5.0/DevExpress.Blazor.targets", "devexpress.blazor.24.2.8.nupkg.sha512", "devexpress.blazor.nuspec", "lib/net8.0/DevExpress.Blazor.v24.2.dll", "lib/net8.0/DevExpress.Blazor.v24.2.xml", "staticwebassets/dx-blazor.svg", "staticwebassets/modules/_commonjsHelpers-41cdd1e7.js", "staticwebassets/modules/adaptivedropdowncomponents-e32c6eb0.js", "staticwebassets/modules/after-render-utils.js", "staticwebassets/modules/baseChart-7893c8e2.js", "staticwebassets/modules/branch-8aad40ac.js", "staticwebassets/modules/browser-3fc721b7.js", "staticwebassets/modules/capture-manager-e7723742.js", "staticwebassets/modules/chart-824df52c.js", "staticwebassets/modules/client-component-style-helper-195fa7c3.js", "staticwebassets/modules/column-resize-4437857c.js", "staticwebassets/modules/columnchooser-5d3fa019.js", "staticwebassets/modules/const-90026e45.js", "staticwebassets/modules/constants-08d84957.js", "staticwebassets/modules/constants-38572198.js", "staticwebassets/modules/constants-7c047c0d.js", "staticwebassets/modules/constants-da6cacac.js", "staticwebassets/modules/constants-ed56e953.js", "staticwebassets/modules/constants-f2324760.js", "staticwebassets/modules/create-after-timeout-fn-executor-38b3d79d.js", "staticwebassets/modules/css-classes-1103f305.js", "staticwebassets/modules/custom-color-area-13585d04.js", "staticwebassets/modules/custom-element-267f9a21.js", "staticwebassets/modules/custom-events-helper-e7f279d3.js", "staticwebassets/modules/data-qa-utils-8be7c726.js", "staticwebassets/modules/define-custom-element-7c2e65e2.js", "staticwebassets/modules/devextreme-widget-wrapper-26d438cc.js", "staticwebassets/modules/devices-17b9ba08.js", "staticwebassets/modules/disposable-d2c2d283.js", "staticwebassets/modules/dom-4684d8d6.js", "staticwebassets/modules/dom-utils-bf33c859.js", "staticwebassets/modules/dragAndDropUnit-e111d952.js", "staticwebassets/modules/draggable-fb58a538.js", "staticwebassets/modules/dragging-helper-b80ad36c.js", "staticwebassets/modules/dropdown-49cc2db8.js", "staticwebassets/modules/dropdown-menu-keyboard-strategy-d6397fc5.js", "staticwebassets/modules/dropdowncomponents-b0c764c6.js", "staticwebassets/modules/dropdowns-ea1bb0c2.js", "staticwebassets/modules/dx-accordion-85df31b8.js", "staticwebassets/modules/dx-adaptive-container-b8ebccb7.js", "staticwebassets/modules/dx-bar-gauge-00a38fcb.js", "staticwebassets/modules/dx-blazor-all.js", "staticwebassets/modules/dx-blazor-quill.js", "staticwebassets/modules/dx-button-ac589911.js", "staticwebassets/modules/dx-calendar-3a4910c5.js", "staticwebassets/modules/dx-carousel-b0b70369.js", "staticwebassets/modules/dx-check-internal-ea00d00d.js", "staticwebassets/modules/dx-color-palette-5b91bed6.js", "staticwebassets/modules/dx-combo-box-44a0b058.js", "staticwebassets/modules/dx-combobox-70a2e148.js", "staticwebassets/modules/dx-context-menu-45dc01dc.js", "staticwebassets/modules/dx-css-runtime-b3e20aad.js", "staticwebassets/modules/dx-date-edit-84aba1ad.js", "staticwebassets/modules/dx-date-edit-base-f326d0d9.js", "staticwebassets/modules/dx-date-range-picker-42da515d.js", "staticwebassets/modules/dx-date-time-edit-6fd627b0.js", "staticwebassets/modules/dx-drawer-12b1b8a4.js", "staticwebassets/modules/dx-dropdown-base3-937bb428.js", "staticwebassets/modules/dx-dropdown-box-1f049a65.js", "staticwebassets/modules/dx-dropdown-list-box-8be80e15.js", "staticwebassets/modules/dx-dropdown-owner-6cfa3e3f.js", "staticwebassets/modules/dx-form-layout-4ef1d069.js", "staticwebassets/modules/dx-grid-ca522bd9.js", "staticwebassets/modules/dx-grid-layout-942510e5.js", "staticwebassets/modules/dx-grid-layout-element-base-96f14b8b.js", "staticwebassets/modules/dx-group-control-7a9d4426.js", "staticwebassets/modules/dx-html-editor-8bd9d4a9.js", "staticwebassets/modules/dx-html-element-base-737a11d3.js", "staticwebassets/modules/dx-html-element-pointer-events-helper-6dda355e.js", "staticwebassets/modules/dx-license-e80137ef.js", "staticwebassets/modules/dx-list-box-d59d864f.js", "staticwebassets/modules/dx-list-box-events-e97c49db.js", "staticwebassets/modules/dx-listbox-8fcae22c.js", "staticwebassets/modules/dx-loading-panel-625d858c.js", "staticwebassets/modules/dx-map-07dbfddc.js", "staticwebassets/modules/dx-memo-e0a33086.js", "staticwebassets/modules/dx-menu-96d37562.js", "staticwebassets/modules/dx-menu-item-f59c4ce7.js", "staticwebassets/modules/dx-menu-preloader-c40bcae2.js", "staticwebassets/modules/dx-pivot-table-346fb71c.js", "staticwebassets/modules/dx-progress-bar-7661d610.js", "staticwebassets/modules/dx-range-selector-7c623da2.js", "staticwebassets/modules/dx-ribbon-eece14bc.js", "staticwebassets/modules/dx-sankey-04fcfc17.js", "staticwebassets/modules/dx-scroll-viewer-e66c94a4.js", "staticwebassets/modules/dx-sparkline-e4ae1ec1.js", "staticwebassets/modules/dx-splitter-8df86779.js", "staticwebassets/modules/dx-splitter-pane-2df7f1d4.js", "staticwebassets/modules/dx-splitter-separator-ed6c6cb5.js", "staticwebassets/modules/dx-stack-layout-a2266142.js", "staticwebassets/modules/dx-style-helper-e638fb51.js", "staticwebassets/modules/dx-tab-item-98ebe86f.js", "staticwebassets/modules/dx-tab-list-3d4f6f72.js", "staticwebassets/modules/dx-tabs-a03fda94.js", "staticwebassets/modules/dx-tag-box-a70c062b.js", "staticwebassets/modules/dx-tag-names-3559f3f6.js", "staticwebassets/modules/dx-tagbox-3ab9ad03.js", "staticwebassets/modules/dx-toast-d9dbde54.js", "staticwebassets/modules/dx-toast-portal-739fa382.js", "staticwebassets/modules/dx-tree-list-f9b9f544.js", "staticwebassets/modules/dx-tree-view-00cf1e22.js", "staticwebassets/modules/dx-ui-element-e4215c11.js", "staticwebassets/modules/dx-ui-handlers-bridge-c2148178.js", "staticwebassets/modules/dx-virtual-scroll-viewer-559b79cd.js", "staticwebassets/modules/dxbl-itemlistdropdown-3ec1372a.js", "staticwebassets/modules/dxbl-window-710913e0.js", "staticwebassets/modules/dynamic-stylesheet-component-569b697b.js", "staticwebassets/modules/enumConverter-6047c3ff.js", "staticwebassets/modules/eventRegister-fb9b0e47.js", "staticwebassets/modules/eventhelper-8bcec49f.js", "staticwebassets/modules/events-5ceb0642.js", "staticwebassets/modules/events-70d90aad.js", "staticwebassets/modules/events-a8fe5872.js", "staticwebassets/modules/events-interseptor-a522582a.js", "staticwebassets/modules/evt-a132c1ec.js", "staticwebassets/modules/expandable-container-faae900a.js", "staticwebassets/modules/file-input-9a9a5253.js", "staticwebassets/modules/flyout-88edbb78.js", "staticwebassets/modules/flyoutcomponents-185b6dcf.js", "staticwebassets/modules/focus-utils-5bdb305f.js", "staticwebassets/modules/focushelper-97620014.js", "staticwebassets/modules/focustrap-f13754bd.js", "staticwebassets/modules/grid-167f78e4.js", "staticwebassets/modules/grid-scroll-utils-ca30b8c6.js", "staticwebassets/modules/index-83904c73.js", "staticwebassets/modules/input-da6e3c40.js", "staticwebassets/modules/key-a66127fc.js", "staticwebassets/modules/keyboard-navigation-strategy-d4a198b5.js", "staticwebassets/modules/layouthelper-42078dcb.js", "staticwebassets/modules/lit-element-462e7ad3.js", "staticwebassets/modules/lit-element-base-4441b4bb.js", "staticwebassets/modules/logicaltreehelper-f5ff1848.js", "staticwebassets/modules/masked-input-1675ad5c.js", "staticwebassets/modules/menu-keyboard-strategy-3d773b70.js", "staticwebassets/modules/modal-keyboard-strategy-7d8503c7.js", "staticwebassets/modules/modalcomponents-2fddca6a.js", "staticwebassets/modules/nameof-factory-64d95f5b.js", "staticwebassets/modules/observables-589a5615.js", "staticwebassets/modules/pager-000fd353.js", "staticwebassets/modules/pie-chart-9a884c97.js", "staticwebassets/modules/point-e4ec110e.js", "staticwebassets/modules/polar-chart-06a2a49a.js", "staticwebassets/modules/popup-24e7aeb3.js", "staticwebassets/modules/popupportal-e4dd19ef.js", "staticwebassets/modules/portal-e7c7352c.js", "staticwebassets/modules/positionlistener-f8adb13f.js", "staticwebassets/modules/positiontracker-4ebc0866.js", "staticwebassets/modules/property-4ec0b52d.js", "staticwebassets/modules/query-44b9267f.js", "staticwebassets/modules/rafaction-bba7928b.js", "staticwebassets/modules/ribbon-1daa71c6.js", "staticwebassets/modules/ribbon-item-7252ae23.js", "staticwebassets/modules/ribbon-utils-8b4ac48a.js", "staticwebassets/modules/roller-fa65f1a9.js", "staticwebassets/modules/scheduler-b9b4c0d7.js", "staticwebassets/modules/screenhelper-b61057ea.js", "staticwebassets/modules/scroll-viewer-css-classes-cc34804c.js", "staticwebassets/modules/settings-e5eb3375.js", "staticwebassets/modules/single-slot-element-base-1e532e64.js", "staticwebassets/modules/spinedit-14c6b6ed.js", "staticwebassets/modules/state-c294470d.js", "staticwebassets/modules/string-2448bf95.js", "staticwebassets/modules/svg-utils-63bfb9ee.js", "staticwebassets/modules/tabbable-98087f1b.js", "staticwebassets/modules/tabs-events-a1f15b18.js", "staticwebassets/modules/text-editor-287f0e89.js", "staticwebassets/modules/thumb-31d768d7.js", "staticwebassets/modules/toolbar-413c8126.js", "staticwebassets/modules/toolbar-css-classes-795f4ae0.js", "staticwebassets/modules/touch-b56439ef.js", "staticwebassets/modules/transformhelper-ebad0156.js", "staticwebassets/modules/tslib.es6-d65164b3.js", "staticwebassets/modules/upload-b6f70c01.js", "staticwebassets/modules/upload-base-212fd47b.js", "staticwebassets/modules/utils-b5b2c8a9.js", "staticwebassets/modules/webview-svg-loader-7cd1bef2.js", "staticwebassets/modules/window-resize-helper-8b32f302.js"]}, "DevExpress.Blazor.Reporting.JSBasedControls/24.2.8": {"sha512": "l/SX92SytW3HDmIhBqGTaaERn5thoVNYaZTnDFCnxzgLFuXbBw+2L3RIGZwdnQbuxcAGL46LdyMuML0D0sQe7w==", "type": "package", "path": "devexpress.blazor.reporting.jsbasedcontrols/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Blazor.Reporting.JSBasedControls.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/DevExpress.Blazor.Reporting.JSBasedControls.props", "buildTransitive/DevExpress.Blazor.Reporting.JSBasedControls.props", "devexpress.blazor.reporting.jsbasedcontrols.24.2.8.nupkg.sha512", "devexpress.blazor.reporting.jsbasedcontrols.nuspec", "lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.dll", "lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.xml"]}, "DevExpress.Blazor.Reporting.JSBasedControls.Common/24.2.8": {"sha512": "zKRHXK106+xNWIJq7fB72UutMKOM/l9CYUs2QNUNlaYnARXATYDtyn3p/362Y3+txU/cbfkBaDdeKe1HPjGY5g==", "type": "package", "path": "devexpress.blazor.reporting.jsbasedcontrols.common/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Blazor.Reporting.JSBasedControls.Common.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/DevExpress.Blazor.Reporting.JSBasedControls.Common.props", "buildTransitive/DevExpress.Blazor.Reporting.JSBasedControls.Common.props", "devexpress.blazor.reporting.jsbasedcontrols.common.24.2.8.nupkg.sha512", "devexpress.blazor.reporting.jsbasedcontrols.common.nuspec", "lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.Common.dll", "lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.Common.xml", "staticwebassets/assets/ambiance-1.png", "staticwebassets/assets/ambiance-2.png", "staticwebassets/assets/ambiance-3.png", "staticwebassets/assets/dreamweaver-1.png", "staticwebassets/assets/dreamweaver-2.png", "staticwebassets/assets/dxicons.ttf", "staticwebassets/assets/dxicons.woff", "staticwebassets/assets/dxicons.woff2", "staticwebassets/assets/main-1.png", "staticwebassets/assets/main-10.svg", "staticwebassets/assets/main-11.svg", "staticwebassets/assets/main-12.svg", "staticwebassets/assets/main-13.png", "staticwebassets/assets/main-14.png", "staticwebassets/assets/main-15.png", "staticwebassets/assets/main-16.png", "staticwebassets/assets/main-17.png", "staticwebassets/assets/main-18.png", "staticwebassets/assets/main-19.png", "staticwebassets/assets/main-2.png", "staticwebassets/assets/main-20.png", "staticwebassets/assets/main-21.png", "staticwebassets/assets/main-22.png", "staticwebassets/assets/main-23.png", "staticwebassets/assets/main-24.png", "staticwebassets/assets/main-25.svg", "staticwebassets/assets/main-26.png", "staticwebassets/assets/main-3.png", "staticwebassets/assets/main-4.png", "staticwebassets/assets/main-5.svg", "staticwebassets/assets/main-6.svg", "staticwebassets/assets/main-7.svg", "staticwebassets/assets/main-8.svg", "staticwebassets/assets/main-9.svg", "staticwebassets/dx-blazor-reportdesigner.js", "staticwebassets/dx-blazor-reporting.css", "staticwebassets/dx-blazor-webdocumentviewer.js", "staticwebassets/dx-reportdesigner.min.js", "staticwebassets/dx-reporting-skeleton-screen.css", "staticwebassets/dx-webdocumentviewer.min.js"]}, "DevExpress.Blazor.Resources/24.2.8": {"sha512": "LuyEsAnSqM+t2nL8+2RTLwj1XBVLJndh0DHAX96rHCUYnqswm+0A8+9PpWkWrhGoTl76nCwPhK5lXZfELFNnqQ==", "type": "package", "path": "devexpress.blazor.resources/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Blazor.Resources.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/DevExpress.Blazor.Resources.props", "buildTransitive/DevExpress.Blazor.Resources.props", "devexpress.blazor.resources.24.2.8.nupkg.sha512", "devexpress.blazor.resources.nuspec", "lib/net8.0/DevExpress.Blazor.Resources.v24.2.dll", "staticwebassets/js/analytics-core/ace.js", "staticwebassets/js/analytics-core/dx-analytics-core.min.js", "staticwebassets/js/analytics-core/dx-querybuilder.min.js", "staticwebassets/js/analytics-core/ext-language_tools.js", "staticwebassets/js/analytics-core/jquery.min.js", "staticwebassets/js/analytics-core/knockout-latest.js", "staticwebassets/js/devextreme/dx.all.js", "staticwebassets/js/import-scripts.js"]}, "DevExpress.Blazor.Themes/24.2.8": {"sha512": "jujD4hb3UdpIDISS+H1FKV5gwb7oR5FYVrCrjwWfJkx7JJk/Qm8jxsXrHxZTNebW8oCpiWFpPicELdEOnNXsPg==", "type": "package", "path": "devexpress.blazor.themes/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Blazor.Themes.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "buildMultiTargeting/DevExpress.Blazor.Themes.props", "buildTransitive/DevExpress.Blazor.Themes.props", "devexpress.blazor.themes.24.2.8.nupkg.sha512", "devexpress.blazor.themes.nuspec", "staticwebassets/blazing-berry.bs4.css", "staticwebassets/blazing-berry.bs4.min.css", "staticwebassets/blazing-berry.bs5.css", "staticwebassets/blazing-berry.bs5.min.css", "staticwebassets/blazing-dark.bs4.css", "staticwebassets/blazing-dark.bs4.min.css", "staticwebassets/blazing-dark.bs5.css", "staticwebassets/blazing-dark.bs5.min.css", "staticwebassets/bootstrap-external.bs4.css", "staticwebassets/bootstrap-external.bs4.min.css", "staticwebassets/bootstrap-external.bs5.css", "staticwebassets/bootstrap-external.bs5.min.css", "staticwebassets/fluent-dark.bs4.css", "staticwebassets/fluent-dark.bs4.min.css", "staticwebassets/fluent-dark.bs5.css", "staticwebassets/fluent-dark.bs5.min.css", "staticwebassets/fluent-dark.css", "staticwebassets/fluent-dark.min.css", "staticwebassets/fluent-light.bs4.css", "staticwebassets/fluent-light.bs4.min.css", "staticwebassets/fluent-light.bs5.css", "staticwebassets/fluent-light.bs5.min.css", "staticwebassets/fluent-light.css", "staticwebassets/fluent-light.min.css", "staticwebassets/office-white.bs4.css", "staticwebassets/office-white.bs4.min.css", "staticwebassets/office-white.bs5.css", "staticwebassets/office-white.bs5.min.css", "staticwebassets/purple.bs4.css", "staticwebassets/purple.bs4.min.css", "staticwebassets/purple.bs5.css", "staticwebassets/purple.bs5.min.css"]}, "DevExpress.Charts/24.2.8": {"sha512": "FKxDEiaNzZgtmaLHH0XBGWgx9p3L5RbWcU9ebOqAJqCMk/loZvIuU9832QBvE+5CTaw+RNSeS6bS230dUk+cTw==", "type": "package", "path": "devexpress.charts/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.24.2.8.nupkg.sha512", "devexpress.charts.nuspec", "lib/net462/DevExpress.XtraCharts.v24.2.dll", "lib/net462/DevExpress.XtraCharts.v24.2.xml", "lib/net8.0/DevExpress.XtraCharts.v24.2.dll", "lib/net8.0/DevExpress.XtraCharts.v24.2.xml"]}, "DevExpress.Charts.Core/24.2.8": {"sha512": "afW8PCkGZMApYDk5TdXEApLd6cXVM561fW8Mmx1/F5Q64m+w93WQCYV71WfYBnFnwXBWC0vzvjxBkhfMJ5qrBA==", "type": "package", "path": "devexpress.charts.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.core.24.2.8.nupkg.sha512", "devexpress.charts.core.nuspec", "lib/net462/DevExpress.Charts.v24.2.Core.dll", "lib/net462/DevExpress.Charts.v24.2.Core.xml", "lib/net8.0/DevExpress.Charts.v24.2.Core.dll", "lib/net8.0/DevExpress.Charts.v24.2.Core.xml"]}, "DevExpress.CodeParser/24.2.8": {"sha512": "KN8X1RifFCPaaaazFcA+/ZLuG3c7VmEqNbnkgwcqTh74TBQzCa7heoBII/lIJJI7Ym52Ut3yndiLTSCck5oz+g==", "type": "package", "path": "devexpress.codeparser/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.codeparser.24.2.8.nupkg.sha512", "devexpress.codeparser.nuspec", "lib/net462/DevExpress.CodeParser.v24.2.dll", "lib/net8.0/DevExpress.CodeParser.v24.2.dll"]}, "DevExpress.Data/24.2.8": {"sha512": "6G7pQcsYF7kTgfWJfYEHtbjtZJSHC7V2+IdcTx+OHWq8Xu51EDE8b2B1j5s+blqBeKXyQcYCQAAexsMHHacAVw==", "type": "package", "path": "devexpress.data/24.2.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/DevExpress.Generator.dll", "devexpress.data.24.2.8.nupkg.sha512", "devexpress.data.nuspec", "lib/net462/DevExpress.Data.v24.2.dll", "lib/net462/DevExpress.Data.v24.2.xml", "lib/net8.0/DevExpress.Data.v24.2.dll", "lib/net8.0/DevExpress.Data.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataAccess/24.2.8": {"sha512": "dyA97gfesHQGDJBSL8A8PZFUz1XQkZFmOZoCV4xpMDW0pF9+8ybj05mkeRS3cKIPgTarLALnXHsUD9fRlTZGvg==", "type": "package", "path": "devexpress.dataaccess/24.2.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.24.2.8.nupkg.sha512", "devexpress.dataaccess.nuspec", "lib/net462/DevExpress.DataAccess.v24.2.dll", "lib/net462/DevExpress.DataAccess.v24.2.xml", "lib/net8.0/DevExpress.DataAccess.v24.2.dll", "lib/net8.0/DevExpress.DataAccess.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataVisualization.Core/24.2.8": {"sha512": "2hoKvgmdw2Rld9rL0dWPYG2D6OjZVRUoVrDvc1H1JhvRq/F/rei2MYTYFy7GJyIo+5JB0gtmex4L5SN8CRsPCA==", "type": "package", "path": "devexpress.datavisualization.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.datavisualization.core.24.2.8.nupkg.sha512", "devexpress.datavisualization.core.nuspec", "lib/net462/DevExpress.DataVisualization.v24.2.Core.dll", "lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll"]}, "DevExpress.Drawing/24.2.8": {"sha512": "ALdAEAFWb3Ooyz6dUxZARZaCBlB/HN8sa3ivuW7SZUrbysnJ2nNJ0H0UaTvNIkaZ5K62+8oiomTiHEnqiWOKwA==", "type": "package", "path": "devexpress.drawing/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.drawing.24.2.8.nupkg.sha512", "devexpress.drawing.nuspec", "lib/net462/DevExpress.Drawing.v24.2.dll", "lib/net462/DevExpress.Drawing.v24.2.xml", "lib/net8.0/DevExpress.Drawing.v24.2.dll", "lib/net8.0/DevExpress.Drawing.v24.2.xml"]}, "DevExpress.Gauges.Core/24.2.8": {"sha512": "CUhJ9zkVUw5ePu5GFHCxJSKrNTPhef0HbGktiD5HXTHG8OVNURFSlPw8yPsFEji3VHuP0KNVT6wQDEbCCruwjg==", "type": "package", "path": "devexpress.gauges.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.gauges.core.24.2.8.nupkg.sha512", "devexpress.gauges.core.nuspec", "lib/net462/DevExpress.XtraGauges.v24.2.Core.dll", "lib/net462/DevExpress.XtraGauges.v24.2.Core.xml", "lib/net8.0/DevExpress.XtraGauges.v24.2.Core.dll", "lib/net8.0/DevExpress.XtraGauges.v24.2.Core.xml"]}, "DevExpress.Office.Core/24.2.8": {"sha512": "yTl8zP51s2AErC2EiBFzfs6l4wwQ4vEiIZd+qC7FvJMVVTjzTIPgQnHGjOGLT6RUIc6FEREi0U9Ss1yi5PFs2w==", "type": "package", "path": "devexpress.office.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.office.core.24.2.8.nupkg.sha512", "devexpress.office.core.nuspec", "lib/net462/DevExpress.Office.v24.2.Core.dll", "lib/net462/DevExpress.Office.v24.2.Core.xml", "lib/net8.0/DevExpress.Office.v24.2.Core.dll", "lib/net8.0/DevExpress.Office.v24.2.Core.xml"]}, "DevExpress.Pdf.Core/24.2.8": {"sha512": "0bslRmvczovySN8kiWMotFl4LoYqcCAfMABEiNGIe/P76XmUnnHZJnjgB+ejZ/MdDLYsK6E/GMV85h35B+R7FA==", "type": "package", "path": "devexpress.pdf.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.core.24.2.8.nupkg.sha512", "devexpress.pdf.core.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Core.dll", "lib/net462/DevExpress.Pdf.v24.2.Core.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Core.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Core.xml"]}, "DevExpress.Pdf.Drawing/24.2.8": {"sha512": "FRsvrggRVGWL2JYMC4hgjQzh+D1uBu3GAGzwd8JNwwxWAi4RzKDcYAU+c4EQlXqUwHH7aTfnHac5BVnqsGHkmQ==", "type": "package", "path": "devexpress.pdf.drawing/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.drawing.24.2.8.nupkg.sha512", "devexpress.pdf.drawing.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net462/DevExpress.Pdf.v24.2.Drawing.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.xml"]}, "DevExpress.PivotGrid.Core/24.2.8": {"sha512": "m0ugcNdjISklEqGjPEgNQ8kvnOg1iUNTkTIEesPYpQXywEM3T/Hl1uGzeGaCEOevnVeOzmUZa0ZEGhNhLn6WZA==", "type": "package", "path": "devexpress.pivotgrid.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pivotgrid.core.24.2.8.nupkg.sha512", "devexpress.pivotgrid.core.nuspec", "lib/net462/DevExpress.PivotGrid.v24.2.Core.dll", "lib/net462/DevExpress.PivotGrid.v24.2.Core.xml", "lib/net8.0/DevExpress.PivotGrid.v24.2.Core.dll", "lib/net8.0/DevExpress.PivotGrid.v24.2.Core.xml"]}, "DevExpress.Printing.Core/24.2.8": {"sha512": "j0B/vRWs0O7RugLtfd7cOrosL20CIB5cA1+iyc04mOqoozrnK0XX4u7e38Th1hKl42nPkV+Ijs9HLjAb/MvzdQ==", "type": "package", "path": "devexpress.printing.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.printing.core.24.2.8.nupkg.sha512", "devexpress.printing.core.nuspec", "lib/net462/DevExpress.Printing.v24.2.Core.dll", "lib/net462/DevExpress.Printing.v24.2.Core.xml", "lib/net8.0/DevExpress.Printing.v24.2.Core.dll", "lib/net8.0/DevExpress.Printing.v24.2.Core.xml"]}, "DevExpress.Reporting.Core/24.2.8": {"sha512": "vj6+v34P/CFH4w6dQyNszfAg8PMiW9PzWM/9IeFdoXq1AH76GwXm1uunUjatUUJFrjkMcU3xlyd1ENXglVu5GQ==", "type": "package", "path": "devexpress.reporting.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "build/DevExpress.Reporting.Core.targets", "buildMultiTargeting/DevExpress.Reporting.Core.targets", "buildTransitive/DevExpress.Reporting.Core.targets", "devexpress.reporting.core.24.2.8.nupkg.sha512", "devexpress.reporting.core.nuspec", "lib/net462/DevExpress.XtraReports.v24.2.dll", "lib/net462/DevExpress.XtraReports.v24.2.xml", "lib/net8.0/DevExpress.XtraReports.v24.2.dll", "lib/net8.0/DevExpress.XtraReports.v24.2.xml"]}, "DevExpress.RichEdit.Core/24.2.8": {"sha512": "SMiLKJCxh0pB7ldQKym7H+S2zmTfQupRmyfV0u63+np77BKOO99GYe1INO6/jvhaAu4IHhAd0lrViDUEBzVkPg==", "type": "package", "path": "devexpress.richedit.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.core.24.2.8.nupkg.sha512", "devexpress.richedit.core.nuspec", "lib/net462/DevExpress.RichEdit.v24.2.Core.dll", "lib/net462/DevExpress.RichEdit.v24.2.Core.xml", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.xml"]}, "DevExpress.RichEdit.Export/24.2.8": {"sha512": "Dpo0j65prtoBaZeEaLqrpvRcWwWkhKKLgomUDjWth9D/x+O3HTxQGpkznAx8UTnTf+iDiB74cqiQ18BufX/2cA==", "type": "package", "path": "devexpress.richedit.export/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.export.24.2.8.nupkg.sha512", "devexpress.richedit.export.nuspec", "lib/net462/DevExpress.RichEdit.v24.2.Export.dll", "lib/net8.0/DevExpress.RichEdit.v24.2.Export.dll"]}, "DevExpress.Sparkline.Core/24.2.8": {"sha512": "4RU+tiVcZYl6p/Ltrq2CH3rObIfZj/wpFKMWW/64Gu7+a2Wn/1z8yB4HM6oYI/N8YeuUb80ebpk2LD0zIf2CsQ==", "type": "package", "path": "devexpress.sparkline.core/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.sparkline.core.24.2.8.nupkg.sha512", "devexpress.sparkline.core.nuspec", "lib/net462/DevExpress.Sparkline.v24.2.Core.dll", "lib/net462/DevExpress.Sparkline.v24.2.Core.xml", "lib/net8.0/DevExpress.Sparkline.v24.2.Core.dll", "lib/net8.0/DevExpress.Sparkline.v24.2.Core.xml"]}, "DevExpress.Web.Reporting.Common/24.2.8": {"sha512": "tfSFGcEV+wMA/Tm9Y/eDQZ6z0LRXwhdvIhyBxnvR7TNQGzu5oZg/Ni1mJj1M5EzSjGKJw08OISRoGo4Py98xAA==", "type": "package", "path": "devexpress.web.reporting.common/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.web.reporting.common.24.2.8.nupkg.sha512", "devexpress.web.reporting.common.nuspec", "lib/net462/DevExpress.XtraReports.v24.2.Web.dll", "lib/net462/DevExpress.XtraReports.v24.2.Web.xml", "lib/net8.0/DevExpress.XtraReports.v24.2.Web.dll", "lib/net8.0/DevExpress.XtraReports.v24.2.Web.xml"]}, "DevExpress.Web.Reporting.Common.Services/24.2.8": {"sha512": "/nIKDGbtryaOWWnvPtHZ49sSqAtuQkBJ0htFfCeyEIEklwmmEOJO+W7OS+SzwFzHKd491U51Ua6EDwtWjx1nZA==", "type": "package", "path": "devexpress.web.reporting.common.services/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.web.reporting.common.services.24.2.8.nupkg.sha512", "devexpress.web.reporting.common.services.nuspec", "lib/net8.0/DevExpress.Web.Reporting.v24.2.Common.Services.dll", "lib/net8.0/DevExpress.Web.Reporting.v24.2.Common.Services.xml"]}, "DevExpress.Web.Resources/24.2.8": {"sha512": "294xUUQPKne3enOMor75CJjNA9LuADE4H63hkJZne7QyUOLqJ3vXaxeD18zWzQL/TecaI6dhWZ4E+55lna/YPw==", "type": "package", "path": "devexpress.web.resources/24.2.8", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.web.resources.24.2.8.nupkg.sha512", "devexpress.web.resources.nuspec", "lib/net462/DevExpress.Web.Resources.v24.2.dll", "lib/net8.0/DevExpress.Web.Resources.v24.2.dll"]}, "DevExpress.Xpo/24.2.8": {"sha512": "1i16FLh/MRG/CAXFDiR+mLNMux919tbEHN6OW0A6ApF38iU9LEQe3JkVewfGeZda3KNixuH2rmsHX77VIm8cHQ==", "type": "package", "path": "devexpress.xpo/24.2.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.xpo.24.2.8.nupkg.sha512", "devexpress.xpo.nuspec", "lib/net462/DevExpress.Xpo.v24.2.dll", "lib/net462/DevExpress.Xpo.v24.2.xml", "lib/net8.0/DevExpress.Xpo.v24.2.dll", "lib/net8.0/DevExpress.Xpo.v24.2.xml", "readme.txt", "tools/VisualStudioToolsManifest.xml"]}, "DevExtreme.AspNet.Data/4.0.0": {"sha512": "BfADnQ9HD3L1/du53CrDbqfb+3zTBc+yGYG1sblYKL33cf2BEjljUEAopI6K4i35fcR4pI6dUbsQL4qOMrRqZg==", "type": "package", "path": "devextreme.aspnet.data/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "devextreme.aspnet.data.4.0.0.nupkg.sha512", "devextreme.aspnet.data.nuspec", "lib/net461/DevExtreme.AspNet.Data.dll", "lib/net461/DevExtreme.AspNet.Data.xml", "lib/net6.0/DevExtreme.AspNet.Data.dll", "lib/net6.0/DevExtreme.AspNet.Data.xml"]}, "Logging.Shared/1.0.0": {"sha512": "OcR4KiuIDz/K3ZZCu76cWbYdktBP8usLFRv/8mP6+tW8Xln9Glnm8S1SfkK1b6MRli0J4XbLXsNimrsay4f4dQ==", "type": "package", "path": "logging.shared/1.0.0", "files": [".nupkg.metadata", "lib/net8.0/Logging.Shared.dll", "logging.shared.1.0.0.nupkg.sha512", "logging.shared.nuspec"]}, "MediatR/12.5.0": {"sha512": "vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "type": "package", "path": "mediatr/12.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "gradient_128x128.png", "lib/net6.0/MediatR.dll", "lib/net6.0/MediatR.xml", "lib/netstandard2.0/MediatR.dll", "lib/netstandard2.0/MediatR.xml", "mediatr.12.5.0.nupkg.sha512", "mediatr.nuspec"]}, "MediatR.Contracts/2.0.1": {"sha512": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "type": "package", "path": "mediatr.contracts/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/netstandard2.0/MediatR.Contracts.dll", "lib/netstandard2.0/MediatR.Contracts.xml", "mediatr.contracts.2.0.1.nupkg.sha512", "mediatr.contracts.nuspec"]}, "Microsoft.AspNetCore.Authorization/8.0.15": {"sha512": "AIpppyCOYv0hJjmYqytMSVohAV8THV6sTk7VoM6rHo1Jq0D1fOfkRi9lnmEz20nfKt50wX2OlciEQop3JlMC6A==", "type": "package", "path": "microsoft.aspnetcore.authorization/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net8.0/Microsoft.AspNetCore.Authorization.dll", "lib/net8.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.8.0.15.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/8.0.15": {"sha512": "eQIgPU1oTkqR8hWvDjyZFGBBtgPY4bYl4a/WN7g0txmoww+fbE1nZ4D+5MeSbZu1kfHWX/Ruog/pMF9zUX6D7g==", "type": "package", "path": "microsoft.aspnetcore.components/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.dll", "lib/net8.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {"sha512": "Uqc/wecFqwfpC9bUK9DH3W3nrriRM6ZBL8OozV6GHNNgrqvzdnqsPKo3NcmjhokvMo2/XHTrreRC2yA2aTLS/A==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"sha512": "579O5c3u0FXnOAjk6JNzcy+/+qazMbedAeOy1vT1B6CBYovG8iGJhyUukqCbHcNQq3/lqxBTyB9rFzlH5zOttA==", "type": "package", "path": "microsoft.aspnetcore.components.forms/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net8.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"sha512": "ttRxlR+yM0sNi+Ecw0ze26l2IcBIF2L+YNQGs4v159v+Ge51yn8bwehnlzs9MY5y3Xamvek4l71uZfk1oouYSA==", "type": "package", "path": "microsoft.aspnetcore.components.web/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net8.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.15": {"sha512": "KFugRNQGBQINzcMcoaTzZr8t30kGuamOtHCmn38Bygx2/FdDJ3UuHvEm0ALMttUNoD5ewQyJPqBsi3olH/LXoQ==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/net8.0/Microsoft.AspNetCore.Components.WebAssembly.props", "build/net8.0/blazor.webassembly.js", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.xml", "microsoft.aspnetcore.components.webassembly.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.nuspec"]}, "Microsoft.AspNetCore.Components.WebAssembly.Server/8.0.15": {"sha512": "MUux2oI92MELxT+a5GWVMoKD3wxfUWsMQzjY1oUz+J8jA2DEOOK6jykOXR94rI555rXNaBGf7wrO4w6difNAkQ==", "type": "package", "path": "microsoft.aspnetcore.components.webassembly.server/8.0.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "build/Microsoft.AspNetCore.Components.WebAssembly.Server.targets", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll", "lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.Server.xml", "microsoft.aspnetcore.components.webassembly.server.8.0.15.nupkg.sha512", "microsoft.aspnetcore.components.webassembly.server.nuspec", "tools/BlazorDebugProxy/BrowserDebugHost.dll", "tools/BlazorDebugProxy/BrowserDebugHost.runtimeconfig.json", "tools/BlazorDebugProxy/BrowserDebugProxy.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.CSharp.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.Scripting.dll", "tools/BlazorDebugProxy/Microsoft.CodeAnalysis.dll", "tools/BlazorDebugProxy/Microsoft.FileFormats.dll", "tools/BlazorDebugProxy/Microsoft.NET.WebAssembly.Webcil.dll", "tools/BlazorDebugProxy/Microsoft.SymbolStore.dll", "tools/BlazorDebugProxy/Newtonsoft.Json.dll"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Metadata/8.0.15": {"sha512": "eKlqQasjNZD0B3thB+nX7kkpG/RiZlXObmjX+JurWycnssGp1q9iZjKn2SqKA45Pouw+529aQUYYP2/QCE5xIQ==", "type": "package", "path": "microsoft.aspnetcore.metadata/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net8.0/Microsoft.AspNetCore.Metadata.dll", "lib/net8.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.8.0.15.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.AspNetCore.Mvc.Abstractions/2.0.3": {"sha512": "iXPYz6zZE6vLLJYjQA7F8vtyPqYgOR1bOhChkfuhbIzrU4VELB2I3ozOdMGviXlmApbpRXZKd4z7viqlKKXiIg==", "type": "package", "path": "microsoft.aspnetcore.mvc.abstractions/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.xml", "microsoft.aspnetcore.mvc.abstractions.2.0.3.nupkg.sha512", "microsoft.aspnetcore.mvc.abstractions.nuspec"]}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"sha512": "sqI4xsQYm/11KsY8P892yrpL3ALAp6e6u12mrnbdWhQt/IiWhK4X9OIQVVMM+ofrPkAKsjP96ctEkJcDKysNVw==", "type": "package", "path": "microsoft.aspnetcore.routing.abstractions/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.xml", "microsoft.aspnetcore.routing.abstractions.2.0.2.nupkg.sha512", "microsoft.aspnetcore.routing.abstractions.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"sha512": "dvn80+p1AIQKOfJ+VrOhVMUktWRvJs7Zb+UapZGBNSyrCzTsYiXbb9C7Mzw+nGj5UevnLNFcWWc7BUlLMD2qpw==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.0.2.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/5.2.2": {"sha512": "mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "type": "package", "path": "microsoft.data.sqlclient/5.2.2", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/net6.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/de/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/es/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/fr/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/it/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ja/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ko/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/ru/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.1/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "microsoft.data.sqlclient.5.2.2.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"sha512": "po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.EntityFrameworkCore/8.0.15": {"sha512": "v6gDYdi2j+BbUHzl5sb835mefpFlSCsQOOsn/xjky45kFA9Xrc2hjVa0yQGVLc2J7+fQDqoHWRD5c+RHU3klSQ==", "type": "package", "path": "microsoft.entityframeworkcore/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.8.0.15.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.15": {"sha512": "wzBUYqfNTyclVaVo6we4Vo0GsfNSmHHJPn3SF4gEsR+e5QZ4r5Ck5Y9oRcEoeqxS+eFPvJzWN6xpTbwCCoWPnw==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.8.0.15.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.15": {"sha512": "viyTqFw09xwhynmvYggUu48R0Y6WEU2dDKBKm7WwozDH5t1K7+RS9P+iXGe5uUNJstTH/STd2AMFKpT9GaX4OQ==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.8.0.15.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/8.0.15": {"sha512": "rOge9UqPepLr4fDSbAjH+gMl4TwK7byKgUy66vgsjlwl11n/+MjbYZAYqfrD8mpGDwJ6Nzv2LIIK2fBHDqA+CQ==", "type": "package", "path": "microsoft.entityframeworkcore.relational/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.8.0.15.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.15": {"sha512": "Kouryy99RU+YvRpfxJ4+ajaILxindm/Fq67zFBd8Lzikl7k/b9c7Ow5ilIB7anrhh6tUXc2MIHfwDV1AcrQS/g==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.8.0.15.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"sha512": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"sha512": "7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"sha512": "EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"sha512": "L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"sha512": "cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.2": {"sha512": "mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"sha512": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"sha512": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.1": {"sha512": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "type": "package", "path": "microsoft.extensions.logging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.1.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"sha512": "pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/6.0.16": {"sha512": "OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "type": "package", "path": "microsoft.extensions.objectpool/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net6.0/Microsoft.Extensions.ObjectPool.dll", "lib/net6.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.61.3": {"sha512": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "type": "package", "path": "microsoft.identity.client/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.61.3.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"sha512": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"sha512": "xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"sha512": "9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.35.0": {"sha512": "jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "type": "package", "path": "microsoft.identitymodel.logging/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.35.0": {"sha512": "BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "type": "package", "path": "microsoft.identitymodel.protocols/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"sha512": "LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.35.0": {"sha512": "RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "type": "package", "path": "microsoft.identitymodel.tokens/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.JSInterop/8.0.15": {"sha512": "JQjYabj1o3M5rUp/GM8uVlLpgc9tgxZea+89IkvGl8AcY7Tfwn0Q5qltVa9rfm3mzCsr2ecVCKzWMSMYiXS0Kg==", "type": "package", "path": "microsoft.jsinterop/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.JSInterop.dll", "lib/net8.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.8.0.15.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Microsoft.JSInterop.WebAssembly/8.0.15": {"sha512": "L7Rnicz9pvb3d3vSLHOHTM8SKv248aFWH9RhH/RB6+siUygIKRah+r+6hJX98is01ygfuoKocdU6uYOQ954rAg==", "type": "package", "path": "microsoft.jsinterop.webassembly/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net8.0/Microsoft.JSInterop.WebAssembly.dll", "lib/net8.0/Microsoft.JSInterop.WebAssembly.xml", "microsoft.jsinterop.webassembly.8.0.15.nupkg.sha512", "microsoft.jsinterop.webassembly.nuspec"]}, "Microsoft.Net.Http.Headers/2.0.2": {"sha512": "hNhJU+Sd7Ws/yrBnakUWKWMyGiDUJE5lTkJfWe5xPL8YGTiL6Es07H9CcTyaYYwVlgW06uDVN0YhhH+t4EjdCw==", "type": "package", "path": "microsoft.net.http.headers/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.0.2.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.4": {"sha512": "9/y05/CuxE+j184Nr4KihhB9KcUkvGojmD4JV4Vt/mHhVZR+eOCD5WCM+CXye9K0OFMsaPXbN+IcaIpjgBGZmg==", "type": "package", "path": "microsoft.netcore.platforms/3.1.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.4.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/4.7.0": {"sha512": "mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "type": "package", "path": "microsoft.win32.systemevents/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.4.7.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "ref/net461/Microsoft.Win32.SystemEvents.dll", "ref/net461/Microsoft.Win32.SystemEvents.xml", "ref/net472/Microsoft.Win32.SystemEvents.dll", "ref/net472/Microsoft.Win32.SystemEvents.xml", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Serilog/4.3.0": {"sha512": "+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "type": "package", "path": "serilog/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Serilog.targets", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.3.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/8.0.3": {"sha512": "Y5at41mc0OV982DEJslBKHd6uzcWO6POwR3QceJ6gtpMPxCzm4+FElGPF0RdaTD7MGsP6XXE05LMbSi0NO+sXg==", "type": "package", "path": "serilog.aspnetcore/8.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.AspNetCore.dll", "lib/net462/Serilog.AspNetCore.xml", "lib/net6.0/Serilog.AspNetCore.dll", "lib/net6.0/Serilog.AspNetCore.xml", "lib/net7.0/Serilog.AspNetCore.dll", "lib/net7.0/Serilog.AspNetCore.xml", "lib/net8.0/Serilog.AspNetCore.dll", "lib/net8.0/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "serilog.aspnetcore.8.0.3.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Enrichers.AspNetCore/1.0.0": {"sha512": "s1MTjjHxDPrSxUCKE5WvVaKlYEsT52z9sK94u1V5RqJpNncWzaa8ypg2zNyF/qsI5tn5qP4q0MUQsf6eyvkcYg==", "type": "package", "path": "serilog.enrichers.aspnetcore/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Enrichers.AspNetCore.dll", "serilog.enrichers.aspnetcore.1.0.0.nupkg.sha512", "serilog.enrichers.aspnetcore.nuspec"]}, "Serilog.Enrichers.AspNetCore.HttpContext/1.0.1": {"sha512": "fcD2pJ3p7iqdsp/L+i9vBkSQN6hvLR6NGGcfOm81tcYfLEOqlZ8FIN07sfQtBZ7sLX3UBrXAlciYmswVG4lIgQ==", "type": "package", "path": "serilog.enrichers.aspnetcore.httpcontext/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Enrichers.AspNetCore.HttpContext.dll", "serilog.enrichers.aspnetcore.httpcontext.1.0.1.nupkg.sha512", "serilog.enrichers.aspnetcore.httpcontext.nuspec"]}, "Serilog.Enrichers.CallerInfo/1.0.5": {"sha512": "Y6WI4dv8c7DUcy1tMUOPi/jcmut2r0eqyc0bM3lKzi8Z7SWoM7L+pUOJbgzkwL1u4QnErYst3VuH9Y0/KTJPdg==", "type": "package", "path": "serilog.enrichers.callerinfo/1.0.5", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net6.0/Serilog.Enrichers.CallerInfo.dll", "lib/netstandard2.0/Serilog.Enrichers.CallerInfo.dll", "serilog.enrichers.callerinfo.1.0.5.nupkg.sha512", "serilog.enrichers.callerinfo.nuspec"]}, "Serilog.Enrichers.Thread/4.0.0": {"sha512": "C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "type": "package", "path": "serilog.enrichers.thread/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Thread.dll", "lib/net462/Serilog.Enrichers.Thread.xml", "lib/net471/Serilog.Enrichers.Thread.dll", "lib/net471/Serilog.Enrichers.Thread.xml", "lib/net6.0/Serilog.Enrichers.Thread.dll", "lib/net6.0/Serilog.Enrichers.Thread.xml", "lib/net8.0/Serilog.Enrichers.Thread.dll", "lib/net8.0/Serilog.Enrichers.Thread.xml", "lib/netstandard2.0/Serilog.Enrichers.Thread.dll", "lib/netstandard2.0/Serilog.Enrichers.Thread.xml", "serilog-enricher-nuget.png", "serilog.enrichers.thread.4.0.0.nupkg.sha512", "serilog.enrichers.thread.nuspec"]}, "Serilog.Exceptions/8.4.0": {"sha512": "nc/+hUw3lsdo0zCj0KMIybAu7perMx79vu72w0za9Nsi6mWyNkGXxYxakAjWB7nEmYL6zdmhEQRB4oJ2ALUeug==", "type": "package", "path": "serilog.exceptions/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net461/Serilog.Exceptions.dll", "lib/net461/Serilog.Exceptions.pdb", "lib/net461/Serilog.Exceptions.xml", "lib/net472/Serilog.Exceptions.dll", "lib/net472/Serilog.Exceptions.pdb", "lib/net472/Serilog.Exceptions.xml", "lib/net5.0/Serilog.Exceptions.dll", "lib/net5.0/Serilog.Exceptions.pdb", "lib/net5.0/Serilog.Exceptions.xml", "lib/net6.0/Serilog.Exceptions.dll", "lib/net6.0/Serilog.Exceptions.pdb", "lib/net6.0/Serilog.Exceptions.xml", "lib/netstandard1.3/Serilog.Exceptions.dll", "lib/netstandard1.3/Serilog.Exceptions.pdb", "lib/netstandard1.3/Serilog.Exceptions.xml", "lib/netstandard2.0/Serilog.Exceptions.dll", "lib/netstandard2.0/Serilog.Exceptions.pdb", "lib/netstandard2.0/Serilog.Exceptions.xml", "lib/netstandard2.1/Serilog.Exceptions.dll", "lib/netstandard2.1/Serilog.Exceptions.pdb", "lib/netstandard2.1/Serilog.Exceptions.xml", "serilog.exceptions.8.4.0.nupkg.sha512", "serilog.exceptions.nuspec"]}, "Serilog.Exceptions.EntityFrameworkCore/8.4.0": {"sha512": "3mOul1jW79IL6CVGpaX3zdaMXpKv20/X/gg9D10kHfabjQ35s7aNYeT3Rm3uXA0MovfURB/41aosKcyfboBNCQ==", "type": "package", "path": "serilog.exceptions.entityframeworkcore/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net5.0/Serilog.Exceptions.EntityFrameworkCore.dll", "lib/net5.0/Serilog.Exceptions.EntityFrameworkCore.pdb", "lib/net5.0/Serilog.Exceptions.EntityFrameworkCore.xml", "lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.dll", "lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.pdb", "lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.xml", "lib/netstandard2.0/Serilog.Exceptions.EntityFrameworkCore.dll", "lib/netstandard2.0/Serilog.Exceptions.EntityFrameworkCore.pdb", "lib/netstandard2.0/Serilog.Exceptions.EntityFrameworkCore.xml", "lib/netstandard2.1/Serilog.Exceptions.EntityFrameworkCore.dll", "lib/netstandard2.1/Serilog.Exceptions.EntityFrameworkCore.pdb", "lib/netstandard2.1/Serilog.Exceptions.EntityFrameworkCore.xml", "serilog.exceptions.entityframeworkcore.8.4.0.nupkg.sha512", "serilog.exceptions.entityframeworkcore.nuspec"]}, "Serilog.Extensions.Hosting/8.0.0": {"sha512": "db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "type": "package", "path": "serilog.extensions.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net6.0/Serilog.Extensions.Hosting.dll", "lib/net6.0/Serilog.Extensions.Hosting.xml", "lib/net7.0/Serilog.Extensions.Hosting.dll", "lib/net7.0/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.8.0.0.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/8.0.0": {"sha512": "YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "type": "package", "path": "serilog.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net6.0/Serilog.Extensions.Logging.dll", "lib/net6.0/Serilog.Extensions.Logging.xml", "lib/net7.0/Serilog.Extensions.Logging.dll", "lib/net7.0/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.8.0.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/2.0.0": {"sha512": "ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "type": "package", "path": "serilog.formatting.compact/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Formatting.Compact.dll", "lib/net462/Serilog.Formatting.Compact.xml", "lib/net471/Serilog.Formatting.Compact.dll", "lib/net471/Serilog.Formatting.Compact.xml", "lib/net6.0/Serilog.Formatting.Compact.dll", "lib/net6.0/Serilog.Formatting.Compact.xml", "lib/net7.0/Serilog.Formatting.Compact.dll", "lib/net7.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.1/Serilog.Formatting.Compact.dll", "lib/netstandard2.1/Serilog.Formatting.Compact.xml", "serilog-extension-nuget.png", "serilog.formatting.compact.2.0.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/8.0.4": {"sha512": "pkxvq0umBKK8IKFJc1aV5S/HGRG/NIxJ6FV42KaTPLfDmBOAbBUB1m5gqqlGxzEa1MgDDWtQlWJdHTSxVWNx+Q==", "type": "package", "path": "serilog.settings.configuration/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net6.0/Serilog.Settings.Configuration.dll", "lib/net6.0/Serilog.Settings.Configuration.xml", "lib/net7.0/Serilog.Settings.Configuration.dll", "lib/net7.0/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.8.0.4.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Async/2.1.0": {"sha512": "SnmRknWsSMgyo9wDXeZZCqSp48kkQYy44taSM6vcpxfiRICzSf09oLKEmVr0RCwQnfd8mJQ2WNN6nvhqf0RowQ==", "type": "package", "path": "serilog.sinks.async/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.Async.dll", "lib/net462/Serilog.Sinks.Async.xml", "lib/net471/Serilog.Sinks.Async.dll", "lib/net471/Serilog.Sinks.Async.xml", "lib/net6.0/Serilog.Sinks.Async.dll", "lib/net6.0/Serilog.Sinks.Async.xml", "lib/net8.0/Serilog.Sinks.Async.dll", "lib/net8.0/Serilog.Sinks.Async.xml", "lib/netstandard2.0/Serilog.Sinks.Async.dll", "lib/netstandard2.0/Serilog.Sinks.Async.xml", "serilog-sink-nuget.png", "serilog.sinks.async.2.1.0.nupkg.sha512", "serilog.sinks.async.nuspec"]}, "Serilog.Sinks.Console/5.0.0": {"sha512": "IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "type": "package", "path": "serilog.sinks.console/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net7.0/Serilog.Sinks.Console.dll", "lib/net7.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "lib/netstandard2.1/Serilog.Sinks.Console.dll", "lib/netstandard2.1/Serilog.Sinks.Console.xml", "serilog.sinks.console.5.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/2.0.0": {"sha512": "Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "type": "package", "path": "serilog.sinks.debug/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Debug.dll", "lib/net45/Serilog.Sinks.Debug.xml", "lib/net46/Serilog.Sinks.Debug.dll", "lib/net46/Serilog.Sinks.Debug.xml", "lib/netstandard1.0/Serilog.Sinks.Debug.dll", "lib/netstandard1.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.1/Serilog.Sinks.Debug.dll", "lib/netstandard2.1/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.2.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.MSSqlServer/8.2.0": {"sha512": "iFDMXySagOYQGFF7l27I1YxKZFk9T3hNnpvA6mY8GolOctYNZJja+j1k2Mjc+t2XxqkQBdUxIEPt3ujtwwfWTg==", "type": "package", "path": "serilog.sinks.mssqlserver/8.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.MSSqlServer.dll", "lib/net462/Serilog.Sinks.MSSqlServer.xml", "lib/net472/Serilog.Sinks.MSSqlServer.dll", "lib/net472/Serilog.Sinks.MSSqlServer.xml", "lib/net8.0/Serilog.Sinks.MSSqlServer.dll", "lib/net8.0/Serilog.Sinks.MSSqlServer.xml", "lib/netstandard2.0/Serilog.Sinks.MSSqlServer.dll", "lib/netstandard2.0/Serilog.Sinks.MSSqlServer.xml", "serilog-sink-nuget.png", "serilog.sinks.mssqlserver.8.2.0.nupkg.sha512", "serilog.sinks.mssqlserver.nuspec"]}, "System.Buffers/4.4.0": {"sha512": "AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "type": "package", "path": "system.buffers/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "system.buffers.4.4.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ClientModel/1.0.0": {"sha512": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "type": "package", "path": "system.clientmodel/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.0.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.NonGeneric/4.3.0": {"sha512": "LE/oChpRvkSi3U25u0KnJcI44JeDZ1QJCyN4qFDx2uusEypdqR24w7lKYw21eYe5esuCBuc862wRmpF63Yy1KQ==", "type": "package", "path": "system.collections.nongeneric/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.3.0.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Collections.Specialized/4.3.0": {"sha512": "Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "type": "package", "path": "system.collections.specialized/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.specialized.4.3.0.nupkg.sha512", "system.collections.specialized.nuspec"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Primitives/4.3.0": {"sha512": "j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "type": "package", "path": "system.componentmodel.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.primitives.4.3.0.nupkg.sha512", "system.componentmodel.primitives.nuspec"]}, "System.ComponentModel.TypeConverter/4.3.0": {"sha512": "16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "type": "package", "path": "system.componentmodel.typeconverter/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "system.componentmodel.typeconverter.nuspec"]}, "System.Configuration.ConfigurationManager/8.0.1": {"sha512": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "type": "package", "path": "system.configuration.configurationmanager/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/8.0.1": {"sha512": "RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "type": "package", "path": "system.data.oledb/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/net8.0/System.Data.OleDb.dll", "lib/net8.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "runtimes/win/lib/net8.0/System.Data.OleDb.dll", "runtimes/win/lib/net8.0/System.Data.OleDb.xml", "system.data.oledb.8.0.1.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/9.0.5": {"sha512": "WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.1": {"sha512": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "type": "package", "path": "system.diagnostics.eventlog/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.1.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/8.0.1": {"sha512": "9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/4.7.2": {"sha512": "I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "type": "package", "path": "system.drawing.common/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.4.7.2.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Formats.Asn1/8.0.2": {"sha512": "yUsFqNGa7tbwm5QOOnOR3VSoh8a0Yki39mTbhOnErdbg8hVSFtrK0EXerj286PXcegiF1LkE7lL++qqMZW5jIQ==", "type": "package", "path": "system.formats.asn1/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.8.0.2.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Extensions/4.3.0": {"sha512": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "type": "package", "path": "system.globalization.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.3.0.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"sha512": "yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.35.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.7.0": {"sha512": "VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "type": "package", "path": "system.reflection.typeextensions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net461/System.Reflection.TypeExtensions.dll", "lib/net461/System.Reflection.TypeExtensions.xml", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netcoreapp1.0/System.Reflection.TypeExtensions.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/System.Reflection.TypeExtensions.dll", "lib/netstandard1.3/System.Reflection.TypeExtensions.xml", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.xml", "lib/netstandard2.0/System.Reflection.TypeExtensions.dll", "lib/netstandard2.0/System.Reflection.TypeExtensions.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net461/System.Reflection.TypeExtensions.dll", "ref/net461/System.Reflection.TypeExtensions.xml", "ref/net472/System.Reflection.TypeExtensions.dll", "ref/net472/System.Reflection.TypeExtensions.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard2.0/System.Reflection.TypeExtensions.dll", "ref/netstandard2.0/System.Reflection.TypeExtensions.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "runtimes/aot/lib/uap10.0.16299/_._", "system.reflection.typeextensions.4.7.0.nupkg.sha512", "system.reflection.typeextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/8.0.0": {"sha512": "4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "type": "package", "path": "system.runtime.caching/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/net7.0/System.Runtime.Caching.dll", "lib/net7.0/System.Runtime.Caching.xml", "lib/net8.0/System.Runtime.Caching.dll", "lib/net8.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/net7.0/System.Runtime.Caching.dll", "runtimes/win/lib/net7.0/System.Runtime.Caching.xml", "runtimes/win/lib/net8.0/System.Runtime.Caching.dll", "runtimes/win/lib/net8.0/System.Runtime.Caching.xml", "system.runtime.caching.8.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Http/6.2.0": {"sha512": "lMk8MEw1OCvyyKY4HMg4ro1eYtWY7azIoDc2FBEGP8uOTJouWn3DemOQvM/GUpgrFbkpjuHPbEG5hgUbNtpiYA==", "type": "package", "path": "system.servicemodel.http/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.Http.dll", "lib/net6.0/System.ServiceModel.Http.pdb", "lib/net6.0/cs/System.ServiceModel.Http.resources.dll", "lib/net6.0/de/System.ServiceModel.Http.resources.dll", "lib/net6.0/es/System.ServiceModel.Http.resources.dll", "lib/net6.0/fr/System.ServiceModel.Http.resources.dll", "lib/net6.0/it/System.ServiceModel.Http.resources.dll", "lib/net6.0/ja/System.ServiceModel.Http.resources.dll", "lib/net6.0/ko/System.ServiceModel.Http.resources.dll", "lib/net6.0/pl/System.ServiceModel.Http.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.Http.resources.dll", "lib/net6.0/ru/System.ServiceModel.Http.resources.dll", "lib/net6.0/tr/System.ServiceModel.Http.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.Http.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.Http.resources.dll", "system.servicemodel.http.6.2.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetFramingBase/6.2.0": {"sha512": "204c9SNDKyQrDKv6F9MLlWKnM7UthRErFByJCHj8y9DtcgMAQnEB5xJvh+9ECmJgG13LJLOAMB5f3CjMatzz/A==", "type": "package", "path": "system.servicemodel.netframingbase/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.NetFramingBase.dll", "lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll", "system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "system.servicemodel.netframingbase.nuspec"]}, "System.ServiceModel.NetTcp/6.2.0": {"sha512": "FXTDhh8DgCfNyY5k9sNlqvhBVYqVM+0GZBsJfFMH5P5q7qGmTxql3bG9tae1Z+uMXJpG2jLbo1CfgusZ75lADA==", "type": "package", "path": "system.servicemodel.nettcp/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.NetTcp.dll", "lib/net6.0/System.ServiceModel.NetTcp.pdb", "lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll", "system.servicemodel.nettcp.6.2.0.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/6.2.0": {"sha512": "ro+c4JKNuX6dDpTWh9ZICYr4pIe7uJToauPPgZt2qqFPjVB78ZDUz3rPCZX89dA+IoRZ+9T1ngLBKsgkTmx7UA==", "type": "package", "path": "system.servicemodel.primitives/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.Primitives.dll", "lib/net6.0/System.ServiceModel.Primitives.pdb", "lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll", "ref/net6.0/System.ServiceModel.Primitives.dll", "system.servicemodel.primitives.6.2.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encodings.Web/4.7.2": {"sha512": "iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "type": "package", "path": "system.text.encodings.web/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.7.2.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TinyMapper/3.0.3": {"sha512": "a070h+NbFjMMclmWjEjGDnihUs4wE3ykx7R4BG7Oo38Jf66spzc3h3CbvbJYFlkbYl65dt5vbJFFH72vFdRr1A==", "type": "package", "path": "tinymapper/3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/TinyMapper.dll", "lib/net35/TinyMapper.xml", "lib/net40/TinyMapper.dll", "lib/net40/TinyMapper.xml", "lib/netstandard1.3/TinyMapper.dll", "lib/netstandard1.3/TinyMapper.xml", "tinymapper.3.0.3.nupkg.sha512", "tinymapper.nuspec"]}, "GestionAPQ_BLZ.Client/1.0.0": {"type": "project", "path": "../GestionAPQ_BLZ.Client/GestionAPQ_BLZ.Client.csproj", "msbuildProject": "../GestionAPQ_BLZ.Client/GestionAPQ_BLZ.Client.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["Blazr.RenderState.Server >= 1.0.0", "Common.ResponseModels >= 1.0.0", "DevExpress.AspNetCore.Reporting >= 24.2.8", "DevExpress.Blazor >= 24.2.8", "DevExpress.Blazor.Reporting.JSBasedControls >= 24.2.8", "GestionAPQ_BLZ.Client >= 1.0.0", "Logging.Shared >= 1.0.0", "MediatR >= 12.5.0", "Microsoft.AspNetCore.Components.WebAssembly.Server >= 8.0.15", "Microsoft.EntityFrameworkCore.SqlServer >= 8.0.15", "TinyMapper >= 3.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ_v2107\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj", "projectName": "GestionAPQ_BLZ", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ_v2107\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ_v2107\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\dc1\\basesdatos\\Nugets": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.devexpress.com/bBEsu1fcNg3j5nDB7vS5tMYM5MEeAGDbvhEuRJUrMlechyNUJq/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ_v2107\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ_v2107\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Blazr.RenderState.Server": {"target": "Package", "version": "[1.0.0, )"}, "Common.ResponseModels": {"target": "Package", "version": "[1.0.0, )"}, "DevExpress.AspNetCore.Reporting": {"target": "Package", "version": "[24.2.8, )"}, "DevExpress.Blazor": {"target": "Package", "version": "[24.2.8, )"}, "DevExpress.Blazor.Reporting.JSBasedControls": {"target": "Package", "version": "[24.2.8, )"}, "Logging.Shared": {"target": "Package", "version": "[1.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Components.WebAssembly.Server": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.15, )"}, "TinyMapper": {"target": "Package", "version": "[3.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}