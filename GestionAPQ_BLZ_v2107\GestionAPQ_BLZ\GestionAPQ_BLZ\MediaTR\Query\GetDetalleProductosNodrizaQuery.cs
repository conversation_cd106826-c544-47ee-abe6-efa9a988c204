﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GetDetalleProductosNodrizaQuery(int NumNodriza)
    : IRequest<SingleResult<DetalleTablaProductoNodrizaDTO>>;

public class
    GetDetalleProductosNodrizaQueryHandler : IRequestHandler<GetDetalleProductosNodrizaQuery,
    SingleResult<DetalleTablaProductoNodrizaDTO>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;
    private readonly ITablaProductosNodrizasRepo _tablaProductosNodrizasRepo;

    public GetDetalleProductosNodrizaQueryHandler(ITablaProductosNodrizasRepo tablaProductosNodrizasRepo,
        ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _tablaProductosNodrizasRepo = tablaProductosNodrizasRepo;
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<DetalleTablaProductoNodrizaDTO>> Handle(
        GetDetalleProductosNodrizaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleTablaProductoNodrizaDTO>
        {
            Data = null,
            Errors = []
        };

        // query para sacar el producto de la nodriza
        var tablaProductoNodriza =
            await _tablaProductosNodrizasRepo.GetTablaProductosNodrizasByNumNodriza(request.NumNodriza,
                true, cancellationToken);
        var idProducto = tablaProductoNodriza?.Idproducto ?? null;
        if (idProducto == null)
        {
            result.Errors.Add($"No se ha encontrado el barniz de la nodriza {request.NumNodriza}.");
            return result;
        }

        // query para sacar el último lote del barniz
        var ultimoLoteNodriza = await _lotesNodrizasRepo.GetUltimoLoteNodrizaByIdProducto(
            request.NumNodriza,
            idProducto.Value, true, cancellationToken);

        // cargamos data en result
        var tablaProductoNodrizaDto = TinyMapper.Map<TablaProductosNodrizasDTO>(tablaProductoNodriza);
        var ultimoLoteNodrizaDto =
            ultimoLoteNodriza != null ? TinyMapper.Map<LotesNodrizasDTO>(ultimoLoteNodriza) : null;
        result.Data = new DetalleTablaProductoNodrizaDTO
        {
            TablaProductoNodriza = tablaProductoNodrizaDto,
            UltimoLote = ultimoLoteNodrizaDto
        };

        return result;
    }
}