﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class IncidenciasRepo : Repository<Incidencias, APQLitalsaContext>, IIncidenciasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public IncidenciasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(string codigo, string lote,
        string ubicacion, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken,
            i =>
                i.Codigo.Equals(codigo)
                && i.Lote.Equals(lote)
                && i.Ubicacion.Equals(ubicacion));
    }

    public async Task<Incidencias?> GetIncidenciaPorId(int id, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken,
            i => i.Id == id);
    }
}