namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetSiguientesLotesMaquinaQuery(int Cantidad, int IdProducto) : IRequest<ListResult<DetalleLoteBarnizDTO>>;

public class
    GetSiguientesLotesMaquinaQueryHandler : IRequestHandler<GetSiguientesLotesMaquinaQuery,
    ListResult<DetalleLoteBarnizDTO>>
{
    private readonly IALotessRepo _aLotessRepo;
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetSiguientesLotesMaquinaQueryHandler(
        IALotessRepo aLotessRepo,
        IAArticuRepo aArticuRepo,
        IAEntdiaRepo aEntdiaRepo,
        IInspeccionesRepo inspeccionesRepo)
    {
        _aLotessRepo = aLotessRepo;
        _aArticuRepo = aArticuRepo;
        _inspeccionesRepo = inspeccionesRepo;
    }

    public async Task<ListResult<DetalleLoteBarnizDTO>> Handle(GetSiguientesLotesMaquinaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<DetalleLoteBarnizDTO> { Data = [], Errors = [] };

        try
        {
            var idProducto = request.IdProducto;

            // recuperamos el artículo barniz y los lotes del mismo con stock
            var lotesConStockPorBarniz = await _aArticuRepo.GetBarnicesInnerJoinLotesConStock(
                Convert.ToString(idProducto),
                _aLotessRepo,
                new QueryOptions(),
                cancellationToken);
            var barniz = lotesConStockPorBarniz.FirstOrDefault().Item1;
            if (barniz == null)
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            var lotesBarniz = lotesConStockPorBarniz.Select(i => i.Item2) ?? [];
            if (!lotesBarniz.Any())
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            // comprobamos que haya inspecciones pasadas por ese idProducto
            var listaInspeccionesAprobadasPorBarniz =
                await _inspeccionesRepo.GetInspeccionesPasadasProductos(idProducto,
                    new InspeccionesQueryOptions(),
                    cancellationToken);
            if (!listaInspeccionesAprobadasPorBarniz.Any())
            {
                result.Errors.Add($"No hay inspecciones pasadas para el producto {idProducto}.");
                return result;
            }

            // ahora, sabiendo
            // - que el producto es un barniz
            // - que tiene lotes con stock
            // - que hay lotes de ese barniz con inspecciones pasadas
            // estamos en posición de saber el siguiente lote a usar sería el empezado más antiguo
            lotesBarniz = lotesBarniz
                .OrderBy(i => i.Fechalote)
                .ThenBy(i => i.Stock)
                .ToList();
            var cantidadPendiente = request.Cantidad;
            List<DetalleLoteBarnizDTO> listaSiguientesLotesDto = [];

            foreach (var lote in lotesBarniz)
            {
                var detalleLoteDto = new DetalleLoteBarnizDTO
                {
                    Lote = TinyMapper.Map<ALotessDTO>(lote),
                    CantidadPendienteLote = cantidadPendiente -= (int)lote.Stock.Value
                };
                listaSiguientesLotesDto.Add(detalleLoteDto);
                if (cantidadPendiente <= 0)
                    break;
            }

            if (cantidadPendiente > 0)
                result.Errors.Add($"No hay suficiente barniz para el producto {idProducto}.");
            else
                result.Data = listaSiguientesLotesDto;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}