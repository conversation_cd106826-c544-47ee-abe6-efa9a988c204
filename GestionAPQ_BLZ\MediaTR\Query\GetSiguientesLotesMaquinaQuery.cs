namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetSiguientesLotesMaquinaQuery(int Cantidad, int IdProducto) : IRequest<ListResult<ALotessDTO>>;

public class
    GetSiguientesLotesMaquinaQueryHandler : IRequestHandler<GetSiguientesLotesMaquinaQuery, ListResult<ALotessDTO>>
{
    private readonly IALotessRepo _aLotessRepo;
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAEntdiaRepo _aEntdiaRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetSiguientesLotesMaquinaQueryHandler(
        IALotessRepo aLotessRepo,
        IAArticuRepo aArticuRepo,
        IAEntdiaRepo aEntdiaRepo,
        IInspeccionesRepo inspeccionesRepo)
    {
        _aLotessRepo = aLotessRepo;
        _aArticuRepo = aArticuRepo;
        _aEntdiaRepo = aEntdiaRepo;
        _inspeccionesRepo = inspeccionesRepo;
    }

    public async Task<ListResult<ALotessDTO>> Handle(GetSiguientesLotesMaquinaQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<ALotessDTO> { Data = [], Errors = [] };

        try
        {
            var idProducto = request.IdProducto;

            // 1. Ejecutar consultas de forma secuencial
            var lotesConStock = await _aArticuRepo.GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
                idProducto.ToString(), _aLotessRepo, _aEntdiaRepo, new QueryOptions(), null, cancellationToken);

            var inspecciones = await _inspeccionesRepo.GetInspeccionesPasadasProductos(
                idProducto, new InspeccionesQueryOptions(), cancellationToken);

            if (!lotesConStock.Any())
            {
                result.Errors.Add($"No hay lotes disponibles para el producto {idProducto}.");
                return result;
            }

            // 2. Crear índice optimizado de lotes aprobados
            var lotesAprobados = inspecciones.Select(i => i.IdLote).ToHashSet();

            // 3. Filtrar y ordenar lotes disponibles de forma optimizada
            var lotesDisponibles = lotesConStock.Select(x => x.Item2)
                .Where(l => l.Stock > 0 && lotesAprobados.Contains(l.Lote))
                .OrderBy(l => l.Fechalote)
                .ThenBy(l => l.Stock)
                .ToList();

            // 4. Seleccionar lotes usando algoritmo optimizado
            var cantidadPendiente = request.Cantidad;
            var lotesSeleccionados = new List<ALotess>();

            foreach (var lote in lotesDisponibles)
            {
                if (cantidadPendiente <= 0) break;

                lotesSeleccionados.Add(lote);
                cantidadPendiente -= (int)(lote.Stock ?? 0);
            }

            if (!lotesSeleccionados.Any())
                result.Errors.Add(
                    $"No hay lotes suficientes para cubrir la cantidad {request.Cantidad} del producto {idProducto}.");
            else
                result.Data = TinyMapper.Map<List<ALotessDTO>>(lotesSeleccionados);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}