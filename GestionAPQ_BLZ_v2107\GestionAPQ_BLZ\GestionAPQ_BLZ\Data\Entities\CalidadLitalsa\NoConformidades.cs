﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GestionNoConformidades.Server.Data.Entities.CalidadLitalsa;

public partial class NoConformidades
{
    public int Id { get; set; }

    public string IdNoConformidad { get; set; }

    public int? IdCliente { get; set; }

    public string DatosCliente { get; set; }

    public string IdNoCoCliente { get; set; }

    public DateTime FechaCreacion { get; set; }

    public int IdTipoNoCo { get; set; }

    public int IdTipoPedido { get; set; }

    public string IdPedidoAfectado { get; set; }

    public string IdPedidoClienteAfectado { get; set; }

    public string DescPedidoAfectado { get; set; }

    public int? HojasPedidoAfectado { get; set; }

    public string TituloNoCo { get; set; }

    public string InfoRecibida { get; set; }

    public string Comentario1 { get; set; }

    public string Comentario2 { get; set; }

    public string ComentariosCarpeta { get; set; }

    public string AnalisisCausa { get; set; }

    public string MedidaContingencia { get; set; }

    public DateTime? FechaFinContingencia { get; set; }

    public string Acciones { get; set; }

    public string EficaciaAccion { get; set; }

    public DateTime? FechaComprobacionEficacia { get; set; }

    public int? IdMaquina { get; set; }

    public int IdTipoSituacion { get; set; }

    public int? Operario { get; set; }

    public string IdReproceso { get; set; }

    public decimal? CosteReproceso { get; set; }

    public int? HojasReproceso { get; set; }

    public decimal? CosteNoCalidad { get; set; }

    public bool ImputableLitalsa { get; set; }

    public bool AfectaSegAlim { get; set; }

    public bool ImputableProveedor { get; set; }

    public bool ImputableCliente { get; set; }

    public DateTime? FechaCierre { get; set; }

    public string NumFactura { get; set; }

    public decimal? CosteFactura { get; set; }

    public bool Borrado { get; set; }

    public string Transporte { get; set; }

    public decimal? CosteTransporte { get; set; }

    public string ManoObra { get; set; }

    public decimal? CosteManoObra { get; set; }

    public int? HojasAchatarrar { get; set; }

    public decimal? CosteHojasAchatarrar { get; set; }

    public DateTime? FechaContestacion { get; set; }

    public int? HojasAfectadas { get; set; }

    public int? EnvasesAfectados { get; set; }

    public bool? AceptadoLitalsa { get; set; }

    public int? EstatusCliente { get; set; }

    public int? EstatusInterno { get; set; }

    public virtual TipoNoCo IdTipoNoCoNavigation { get; set; }

    public virtual TipoPedido IdTipoPedidoNavigation { get; set; }

    public virtual TipoSituacion IdTipoSituacionNavigation { get; set; }

    public virtual ICollection<Muestras> Muestras { get; set; } = new List<Muestras>();
}