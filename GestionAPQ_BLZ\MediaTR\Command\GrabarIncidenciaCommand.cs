namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.APQLitalsa.Base;

// Command para crear nueva incidencia
public record CrearIncidenciaCommand(IncidenciaDTO Incidencia) : IRequest<SingleResult<int>>;

public class CrearIncidenciaCommandHandler : IRequestHandler<CrearIncidenciaCommand, SingleResult<int>>
{
    private readonly IIncidenciasRepo _incidenciasRepo;

    public CrearIncidenciaCommandHandler(IIncidenciasRepo incidenciasRepo)
    {
        _incidenciasRepo = incidenciasRepo;
    }

    public async Task<SingleResult<int>> Handle(CrearIncidenciaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        try
        {
            // Validaciones
            var validationErrors = ValidateCrearIncidencia(request.Incidencia);
            if (validationErrors.Any())
            {
                foreach (var error in validationErrors)
                    result.Errors.Add(error);
                return result;
            }

            var incidenciaDb = TinyMapper.Map<Incidencias>(request.Incidencia);

            // Establecer fechas de creación
            incidenciaDb.Id = 0; // Asegurar que es nueva
            incidenciaDb.FechaCreacion = DateTime.Now;
            incidenciaDb.FechaModificacion = DateTime.Now;

            await _incidenciasRepo.Add(incidenciaDb, cancellationToken);
            result.Data = incidenciaDb.Id;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private static List<string> ValidateCrearIncidencia(IncidenciaDTO incidencia)
    {
        var errors = new List<string>();

        if (incidencia == null)
        {
            errors.Add("La incidencia no puede ser nula");
            return errors;
        }

        if (string.IsNullOrWhiteSpace(incidencia.Incidencia))
            errors.Add("La descripción de la incidencia es obligatoria");
        else if (incidencia.Incidencia.Length > 500)
            errors.Add("La descripción no puede exceder 500 caracteres");

        if (!incidencia.IdOperario.HasValue || incidencia.IdOperario.Value <= 0)
            errors.Add("Debe seleccionar un operario válido");

        if (string.IsNullOrWhiteSpace(incidencia.Codigo))
            errors.Add("El código del producto es obligatorio");
        else if (incidencia.Codigo.Length > 50)
            errors.Add("El código no puede exceder 50 caracteres");

        if (string.IsNullOrWhiteSpace(incidencia.Lote))
            errors.Add("El lote es obligatorio");
        else if (incidencia.Lote.Length > 50)
            errors.Add("El lote no puede exceder 50 caracteres");

        if (string.IsNullOrWhiteSpace(incidencia.Ubicacion))
            errors.Add("La ubicación es obligatoria");
        else if (incidencia.Ubicacion.Length > 50)
            errors.Add("La ubicación no puede exceder 50 caracteres");

        if (incidencia.Id.HasValue && incidencia.Id.Value > 0)
            errors.Add("No debe especificar ID para crear una nueva incidencia");

        return errors;
    }
}

// Command para actualizar incidencia existente
public record ActualizarIncidenciaCommand(IncidenciaDTO Incidencia) : IRequest<SingleResult<int>>;

public class ActualizarIncidenciaCommandHandler : IRequestHandler<ActualizarIncidenciaCommand, SingleResult<int>>
{
    private readonly IIncidenciasRepo _incidenciasRepo;

    public ActualizarIncidenciaCommandHandler(IIncidenciasRepo incidenciasRepo)
    {
        _incidenciasRepo = incidenciasRepo;
    }

    public async Task<SingleResult<int>> Handle(ActualizarIncidenciaCommand request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        try
        {
            // Validaciones
            var validationErrors = ValidateActualizarIncidencia(request.Incidencia);
            if (validationErrors.Any())
            {
                foreach (var error in validationErrors)
                    result.Errors.Add(error);
                return result;
            }

            var incidenciaDb = TinyMapper.Map<Incidencias>(request.Incidencia);

            // Solo actualizar fecha de modificación
            incidenciaDb.FechaModificacion = DateTime.Now;

            await _incidenciasRepo.Update(incidenciaDb, cancellationToken);
            result.Data = incidenciaDb.Id;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private static List<string> ValidateActualizarIncidencia(IncidenciaDTO incidencia)
    {
        var errors = new List<string>();

        if (incidencia == null)
        {
            errors.Add("La incidencia no puede ser nula");
            return errors;
        }

        if (!incidencia.Id.HasValue || incidencia.Id.Value <= 0)
            errors.Add("El ID es obligatorio para actualizar una incidencia");

        if (string.IsNullOrWhiteSpace(incidencia.Incidencia))
            errors.Add("La descripción de la incidencia es obligatoria");
        else if (incidencia.Incidencia.Length > 500)
            errors.Add("La descripción no puede exceder 500 caracteres");

        if (!incidencia.IdOperario.HasValue || incidencia.IdOperario.Value <= 0)
            errors.Add("Debe seleccionar un operario válido");

        if (string.IsNullOrWhiteSpace(incidencia.Codigo))
            errors.Add("El código del producto es obligatorio");
        else if (incidencia.Codigo.Length > 50)
            errors.Add("El código no puede exceder 50 caracteres");

        if (string.IsNullOrWhiteSpace(incidencia.Lote))
            errors.Add("El lote es obligatorio");
        else if (incidencia.Lote.Length > 50)
            errors.Add("El lote no puede exceder 50 caracteres");

        if (string.IsNullOrWhiteSpace(incidencia.Ubicacion))
            errors.Add("La ubicación es obligatoria");
        else if (incidencia.Ubicacion.Length > 50)
            errors.Add("La ubicación no puede exceder 50 caracteres");

        return errors;
    }
}