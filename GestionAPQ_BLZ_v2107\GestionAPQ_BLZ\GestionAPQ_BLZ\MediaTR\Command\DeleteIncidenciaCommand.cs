﻿namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Repositories.Base.APQLitalsa;

public record DeleteIncidenciaCommand(int IdIncidencia) : IRequest<SingleResult<int>>;

public class DeleteIncidenciaCommandHandler : IRequestHandler<DeleteIncidenciaCommand, SingleResult<int>>
{
    private readonly IIncidenciasRepo _incidenciasRepo;

    public DeleteIncidenciaCommandHandler(IIncidenciasRepo incidenciasRepo)
    {
        _incidenciasRepo = incidenciasRepo;
    }


    public async Task<SingleResult<int>> Handle(DeleteIncidenciaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = [],
            Data = 0
        };

        var incidencia = await _incidenciasRepo.GetIncidenciaPorId(request.IdIncidencia, false, cancellationToken);
        if (incidencia == null)
        {
            result.Errors.Add("No se ha encontrado la incidencia en la base de datos.");
            return result;
        }

        var resultDb = await _incidenciasRepo.DeleteEntity(cancellationToken, incidencia);
        result.Data = resultDb;

        return result;
    }
}