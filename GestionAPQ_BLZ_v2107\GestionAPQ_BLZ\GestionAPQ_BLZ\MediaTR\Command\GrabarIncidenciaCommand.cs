﻿namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GrabarIncidenciaCommand(IncidenciaDTO Incidencia) : IRequest<SingleResult<int>>;

public class GrabarIncidenciaCommandHandler : IRequestHandler<GrabarIncidenciaCommand, SingleResult<int>>
{
    private readonly IIncidenciasRepo _incidenciasRepo;

    public GrabarIncidenciaCommandHandler(IIncidenciasRepo incidenciasRepo)
    {
        _incidenciasRepo = incidenciasRepo;
    }

    public async Task<SingleResult<int>> Handle(GrabarIncidenciaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        Incidencias? incidencia;
        // si tiene ID, es una actualización
        if (request.Incidencia.Id != null)
        {
            incidencia =
                await _incidenciasRepo.GetIncidenciaPorId(request.Incidencia.Id.Value, false, cancellationToken);
            if (incidencia == null)
            {
                result.Errors.Add("No se ha encontrado la incidencia en la base de datos.");
                return result;
            }

            // actualizamos campos
            incidencia.Incidencia = request.Incidencia.Incidencia;
        }
        else
        {
            incidencia = TinyMapper.Map<Incidencias>(request.Incidencia);
            incidencia.FechaCreacion = DateTime.Now;
        }

        var resultDb = await _incidenciasRepo.UpdateEntityWithId(cancellationToken, incidencia);
        result.Data = resultDb;
        return result;
    }
}