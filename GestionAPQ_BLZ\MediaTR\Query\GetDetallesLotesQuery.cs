namespace GestionAPQ_BLZ.MediaTR.Query;

using System.Linq.Expressions;
using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetDetallesLotesQuery(string? IdProducto, bool AgruparPorCodigoyLote)
    : IRequest<ListResult<BarnizDTO>>;

public class GetDetallesLotesQueryHandler : IRequestHandler<GetDetallesLotesQuery, ListResult<BarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAEntdiaRepo _aEntdiaRepo;
    private readonly IALotessRepo _aLotessRepo;
    private readonly IIncidenciasRepo _incidenciasRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetDetallesLotesQueryHandler(
        IInspeccionesRepo inspeccionesRepo,
        IIncidenciasRepo incidenciasRepo,
        IAArticuRepo aArticuRepo,
        IALotessRepo aLotessRepo,
        IAEntdiaRepo aEntdiaRepo)
    {
        _inspeccionesRepo = inspeccionesRepo;
        _incidenciasRepo = incidenciasRepo;
        _aArticuRepo = aArticuRepo;
        _aLotessRepo = aLotessRepo;
        _aEntdiaRepo = aEntdiaRepo;
    }

    public async Task<ListResult<BarnizDTO>> Handle(
        GetDetallesLotesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<BarnizDTO> { Data = [], Errors = [] };

        try
        {
            // 1. Obtener datos base en paralelo
            var (lotesActivos, inspeccionesPasadas, incidencias) = await ObtenerDatosBase(request, cancellationToken);

            // 2. Crear índices para búsquedas rápidas
            var indices = CrearIndices(lotesActivos, inspeccionesPasadas, incidencias);

            // 3. Procesar lotes inspeccionados
            var barnicesInspeccionados = ProcesarLotesInspeccionados(inspeccionesPasadas, indices);

            // 4. Procesar lotes pendientes de inspección
            var barnicesPendientes = ProcesarLotesPendientes(lotesActivos, indices);

            // 5. Combinar y consolidar resultados
            result.Data = CombinarYConsolidarResultados(barnicesInspeccionados, barnicesPendientes, incidencias);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    /// <summary>
    ///     Obtiene todos los datos base necesarios en paralelo para optimizar el rendimiento
    /// </summary>
    private async Task<(
        List<(AArticu, ALotess, AEntdia)> lotesActivos,
        List<Inspecciones> inspeccionesPasadas,
        Dictionary<string, Incidencias> incidencias
        )> ObtenerDatosBase(GetDetallesLotesQuery request, CancellationToken cancellationToken)
    {
        // Configurar agrupación si es necesaria
        Expression<Func<(AArticu, ALotess, AEntdia), object>>? groupBy =
            request.AgruparPorCodigoyLote ? i => new { i.Item2.Codigo, i.Item2.Lote } : null;

        int? idProducto = null;
        if (!string.IsNullOrEmpty(request.IdProducto) && int.TryParse(request.IdProducto, out var idProd))
            idProducto = idProd;

        // Ejecutar consultas de forma secuencial
        var lotesActivos = await _aArticuRepo.GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
            request.IdProducto, _aLotessRepo, _aEntdiaRepo, new QueryOptions(), groupBy, cancellationToken);

        var inspeccionesPasadas = await _inspeccionesRepo.GetInspeccionesPasadasProductos(
            idProducto, new InspeccionesQueryOptions(), cancellationToken);

        // Obtener incidencias para lotes activos sin inspección
        var incidencias =
            await ObtenerIncidenciasParaLotesSinInspeccion(lotesActivos, inspeccionesPasadas, cancellationToken);

        return (lotesActivos, inspeccionesPasadas, incidencias);
    }

    /// <summary>
    ///     Obtiene incidencias solo para lotes que no tienen inspección
    /// </summary>
    private async Task<Dictionary<string, Incidencias>> ObtenerIncidenciasParaLotesSinInspeccion(
        List<(AArticu, ALotess, AEntdia)> lotesActivos,
        List<Inspecciones> inspeccionesPasadas,
        CancellationToken cancellationToken)
    {
        var lotesInspeccionados = inspeccionesPasadas.Select(i => i.IdLote).ToHashSet();
        var lotesSinInspeccion = lotesActivos
            .Where(l => !lotesInspeccionados.Contains(l.Item2.Lote))
            .Select(l => new { l.Item2.Codigo, l.Item2.Lote, l.Item2.Ubicacion })
            .Distinct()
            .ToList();

        // Obtener incidencias de forma secuencial para evitar problemas de DbContext
        var incidencias = new Dictionary<string, Incidencias>();

        foreach (var lote in lotesSinInspeccion)
        {
            var incidencia = await _incidenciasRepo.GetIncidenciaByIdProductoxLotexUbicacion(
                lote.Codigo, lote.Lote, lote.Ubicacion, new IncidenciasQueryOptions(), cancellationToken);

            if (incidencia != null)
            {
                var key = $"{lote.Codigo}|{lote.Lote}|{lote.Ubicacion}";
                incidencias[key] = incidencia;
            }
        }

        return incidencias;
    }

    /// <summary>
    ///     Crea índices para búsquedas rápidas y evitar loops anidados
    /// </summary>
    private (
        Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorArticulo,
        Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorLote,
        HashSet<string> lotesInspeccionados
        ) CrearIndices(
            List<(AArticu, ALotess, AEntdia)> lotesActivos,
            List<Inspecciones> inspeccionesPasadas,
            Dictionary<string, Incidencias> incidencias)
    {
        var lotesActivosPorArticulo = lotesActivos
            .GroupBy(l => l.Item1.Codigo)
            .ToDictionary(g => g.Key, g => g.ToList());

        var lotesActivosPorLote = lotesActivos
            .GroupBy(l => $"{l.Item1.Codigo}|{l.Item2.Lote}")
            .ToDictionary(g => g.Key, g => g.ToList());

        var lotesInspeccionados = inspeccionesPasadas
            .Select(i => i.IdLote)
            .ToHashSet();

        return (lotesActivosPorArticulo, lotesActivosPorLote, lotesInspeccionados);
    }

    /// <summary>
    ///     Procesa lotes que ya han sido inspeccionados
    /// </summary>
    private List<BarnizDTO> ProcesarLotesInspeccionados(
        List<Inspecciones> inspeccionesPasadas,
        (Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorArticulo,
            Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorLote,
            HashSet<string> lotesInspeccionados) indices)
    {
        return inspeccionesPasadas
            .GroupBy(i => i.IdProducto)
            .Select(grupo => CrearBarnizDTOParaInspeccionados(grupo.Key, grupo.ToList(), indices))
            .ToList();
    }

    /// <summary>
    ///     Crea un BarnizDTO para lotes inspeccionados de un artículo específico
    /// </summary>
    private BarnizDTO CrearBarnizDTOParaInspeccionados(
        int? idProducto,
        List<Inspecciones> inspecciones,
        (Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorArticulo,
            Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorLote,
            HashSet<string> lotesInspeccionados) indices)
    {
        var codigoArticulo = idProducto?.ToString() ?? "";

        // Obtener artículo (real o fake)
        var articuloReal = indices.lotesActivosPorArticulo.GetValueOrDefault(codigoArticulo)?.FirstOrDefault().Item1;
        var articuloDto = articuloReal != null
            ? TinyMapper.Map<AArticuDTO>(articuloReal)
            : new AArticuDTO { Codigo = codigoArticulo };

        // Procesar lotes inspeccionados
        var detallesLotes = inspecciones.Select(inspeccion =>
            CrearDetalleLoteParaInspeccionado(inspeccion, codigoArticulo, indices)
        ).ToList();

        return new BarnizDTO
        {
            Articulo = articuloDto,
            ListaDetallesLotes = detallesLotes
        };
    }

    /// <summary>
    ///     Crea un DetalleLoteBarnizDTO para un lote inspeccionado
    /// </summary>
    private DetalleLoteBarnizDTO CrearDetalleLoteParaInspeccionado(
        Inspecciones inspeccion,
        string codigoArticulo,
        (Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorArticulo,
            Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorLote,
            HashSet<string> lotesInspeccionados) indices)
    {
        var inspeccionDto = TinyMapper.Map<InspeccionDTO>(inspeccion);
        var claveLote = $"{codigoArticulo}|{inspeccion.IdLote}";

        // Buscar lote real en los activos
        var lotesReales = indices.lotesActivosPorLote.GetValueOrDefault(claveLote);

        if (lotesReales?.Any() == true)
        {
            // Usar datos reales del primer lote encontrado
            var (articulo, lote, entrada) = lotesReales.First();
            return new DetalleLoteBarnizDTO
            {
                Inspeccion = inspeccionDto,
                Lote = TinyMapper.Map<ALotessDTO>(lote),
                PrimeraEntrada = entrada != null ? TinyMapper.Map<AEntdiaDTO>(entrada) : null
            };
        }
        else
        {
            // Crear lote fake para inspecciones históricas
            return new DetalleLoteBarnizDTO
            {
                Inspeccion = inspeccionDto,
                Lote = new ALotessDTO { Lote = inspeccion.IdLote, Codigo = codigoArticulo },
                PrimeraEntrada = null
            };
        }
    }

    /// <summary>
    ///     Procesa lotes que están pendientes de inspección
    /// </summary>
    private List<BarnizDTO> ProcesarLotesPendientes(
        List<(AArticu, ALotess, AEntdia)> lotesActivos,
        (Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorArticulo,
            Dictionary<string, List<(AArticu, ALotess, AEntdia)>> lotesActivosPorLote,
            HashSet<string> lotesInspeccionados) indices)
    {
        return lotesActivos
            .Where(l => !indices.lotesInspeccionados.Contains(l.Item2.Lote))
            .GroupBy(l => l.Item1.Codigo)
            .Select(grupo => CrearBarnizDTOParaPendientes(grupo.Key, grupo.ToList()))
            .ToList();
    }

    /// <summary>
    ///     Crea un BarnizDTO para lotes pendientes de inspección
    /// </summary>
    private BarnizDTO CrearBarnizDTOParaPendientes(string codigoArticulo, List<(AArticu, ALotess, AEntdia)> lotes)
    {
        var primerLote = lotes.First();
        var articuloDto = TinyMapper.Map<AArticuDTO>(primerLote.Item1);

        var detallesLotes = lotes.Select(l => new DetalleLoteBarnizDTO
        {
            Lote = TinyMapper.Map<ALotessDTO>(l.Item2),
            Inspeccion = null, // Pendiente de inspección
            Incidencia = null, // Se asignará en CombinarYConsolidarResultados si existe
            PrimeraEntrada = l.Item3 != null ? TinyMapper.Map<AEntdiaDTO>(l.Item3) : null
        }).ToList();

        return new BarnizDTO
        {
            Articulo = articuloDto,
            ListaDetallesLotes = detallesLotes
        };
    }

    /// <summary>
    ///     Combina y consolida los resultados de lotes inspeccionados y pendientes
    /// </summary>
    private List<BarnizDTO> CombinarYConsolidarResultados(
        List<BarnizDTO> barnicesInspeccionados,
        List<BarnizDTO> barnicesPendientes,
        Dictionary<string, Incidencias> incidencias)
    {
        var resultado = new Dictionary<string, BarnizDTO>();

        // Agregar lotes inspeccionados
        foreach (var barniz in barnicesInspeccionados)
        {
            resultado[barniz.Articulo.Codigo] = barniz;
        }

        // Agregar o combinar lotes pendientes
        foreach (var barnizPendiente in barnicesPendientes)
        {
            var codigo = barnizPendiente.Articulo.Codigo;

            // Asignar incidencias a lotes pendientes
            foreach (var detalleLote in barnizPendiente.ListaDetallesLotes)
            {
                var claveIncidencia = $"{detalleLote.Lote.Codigo}|{detalleLote.Lote.Lote}|{detalleLote.Lote.Ubicacion}";
                if (incidencias.TryGetValue(claveIncidencia, out var incidencia))
                {
                    detalleLote.Incidencia = TinyMapper.Map<IncidenciaDTO>(incidencia);
                }
            }

            if (resultado.TryGetValue(codigo, out var barnizExistente))
            {
                // Combinar lotes pendientes con el artículo existente
                barnizExistente.ListaDetallesLotes ??= [];
                barnizExistente.ListaDetallesLotes.AddRange(barnizPendiente.ListaDetallesLotes);

                // Actualizar datos del artículo con información real (no fake)
                barnizExistente.Articulo = barnizPendiente.Articulo;
            }
            else
            {
                // Agregar nuevo artículo con lotes pendientes
                resultado[codigo] = barnizPendiente;
            }
        }

        return resultado.Values.ToList();
    }
}