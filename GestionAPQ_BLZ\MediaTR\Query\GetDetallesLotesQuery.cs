namespace GestionAPQ_BLZ.MediaTR.Query;

using System.Linq.Expressions;
using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.APQLitalsa.Base;
using Repositories.Dato01Lita.Base;

public record GetDetallesLotesQuery(string? IdProducto, bool AgruparPorCodigoyLote)
    : IRequest<ListResult<BarnizDTO>>;

public class GetDetallesLotesQueryHandler : IRequestHandler<GetDetallesLotesQuery, ListResult<BarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAEntdiaRepo _aEntdiaRepo;
    private readonly IALotessRepo _aLotessRepo;
    private readonly IIncidenciasRepo _incidenciasRepo;
    private readonly IInspeccionesRepo _inspeccionesRepo;

    public GetDetallesLotesQueryHandler(
        IInspeccionesRepo inspeccionesRepo,
        IIncidenciasRepo incidenciasRepo,
        IAArticuRepo aArticuRepo,
        IALotessRepo aLotessRepo,
        IAEntdiaRepo aEntdiaRepo)
    {
        _inspeccionesRepo = inspeccionesRepo;
        _incidenciasRepo = incidenciasRepo;
        _aArticuRepo = aArticuRepo;
        _aLotessRepo = aLotessRepo;
        _aEntdiaRepo = aEntdiaRepo;
    }

    public async Task<ListResult<BarnizDTO>> Handle(
        GetDetallesLotesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<BarnizDTO> { Data = [], Errors = [] };

        try
        {
            // sacamos, dependiendo del request.idproducto, la lista de artículos con lotesactivos así como la primera entrada del mismo lote
            Expression<Func<(AArticu, ALotess, AEntdia), object>>? groupBy =
                request.AgruparPorCodigoyLote ? i => new { i.Item2.Codigo, i.Item2.Lote } : null;
            var listaArticulosConLotesActivos =
                await _aArticuRepo.GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
                    request.IdProducto, _aLotessRepo, _aEntdiaRepo, new QueryOptions(), groupBy, cancellationToken);

            // sacamos, dependiendo del request.idproducto, la la lista de inspeccionespasadas
            int? idProdSel = null;
            if (!string.IsNullOrEmpty(request.IdProducto) && int.TryParse(request.IdProducto, out var idProd))
                idProdSel = idProd;
            var listaInspeccionesPasadas =
                await _inspeccionesRepo.GetInspeccionesPasadasProductos(idProdSel, new InspeccionesQueryOptions(),
                    cancellationToken);

            // ahora, con el total de inspecciones pasadas y los lotes activos, necesitamos recapitular:
            // 1 - todas las inspecciones pasadas por el material, independientemente de si hay coincidencia con lotes activos o no
            // 2 - los lotes con inspección pendiente
            // Por cada artículo de la lista de inspecciones pasadas, tenemos que añadirlo a nuestra lista y buscar sus datos de lotes si contáramos con ellos.
            // Como estos registros de artículos y lotes van desde el año de la pera, habrá que "fakear" los artículos y lotes que no se hayan
            // devuelto en la consulta que se ha hecho a in2 porque habrá artículos y lotes en el histórico que no se tienen en cuenta en la consulta inicial.
            // Dicho esto,
            //      - Lo primero será recorrer la listaInspeccionesPasadas para añadir sí o sí sus lotes, relacionando con los lotes con stock de In2 para traspasar sus datos si los tuviéramos,
            //          así como la primeraentrada.
            //      - Lo segundo, buscar entre nuestros lotes activos los que tengan inspección pendiente, para añadirlos, o actualizar los datos del artículo/lotes/primeraEntrada ya añadida.

            List<BarnizDTO> listaArticulos = [];

            //      - Lo primero será recorrer la listaInspeccionesPasadas para añadir sí o sí sus lotes, relacionando con los lotes con stock de In2 para traspasar sus datos si los tuviéramos,
            //          así como la primeraentrada.
            var listaInspeccionesPasadasPorArticulo = listaInspeccionesPasadas.GroupBy(i => i.IdProducto);
            foreach (var kvLotesPorArticulo in listaInspeccionesPasadasPorArticulo)
            {
                var codArticulo = kvLotesPorArticulo.Key;
                var listaInspecciones = kvLotesPorArticulo.ToList();
                List<DetalleLoteBarnizDTO> listaLotes = [];

                // sacamos el artículoDto
                var aleIn2First =
                    listaArticulosConLotesActivos.FirstOrDefault(ale =>
                        ale.Item1.Codigo.Equals(codArticulo.ToString()));
                var aArticuDto = aleIn2First.Item1 != null
                    ? TinyMapper.Map<AArticuDTO>(aleIn2First.Item1)
                    : new AArticuDTO
                        { Codigo = codArticulo.ToString() }; // fakeamos el artículo, por lo dicho antes

                foreach (var inspeccion in listaInspecciones)
                {
                    var inspeccionDto = TinyMapper.Map<InspeccionDTO>(inspeccion);
                    // usamos where porque puede haber mismos lotes en distintas ubicaciones
                    var listaAleIn2 = listaArticulosConLotesActivos.Where(
                        ale =>
                            ale.Item1.Codigo.Equals(aArticuDto.Codigo)
                            && ale.Item2.Lote.Equals(inspeccionDto.IdLote));

                    if (listaAleIn2.Any())
                    {
                        foreach (var aleIn2 in listaAleIn2)
                        {
                            var detalleLoteDto = new DetalleLoteBarnizDTO
                            {
                                Inspeccion = inspeccionDto,
                                Lote = TinyMapper.Map<ALotessDTO>(aleIn2.Item2),
                                PrimeraEntrada = aleIn2.Item3 != null ? TinyMapper.Map<AEntdiaDTO>(aleIn2.Item3) : null
                                //Incidencia = incidenciaDto, // las incidencias solo hacen falta en lotes con inspección pendiente, y como estamos recorriendo las que han pasado... pues eso
                            };
                            listaLotes.Add(detalleLoteDto);
                        }
                    }
                    else
                    {
                        var detalleLoteDto = new DetalleLoteBarnizDTO
                        {
                            Inspeccion = inspeccionDto,
                            Lote = new ALotessDTO // fakeamos el lote, por lo dicho antes,
                            {
                                Lote = inspeccionDto.IdLote,
                                Codigo = aArticuDto.Codigo
                            },
                            PrimeraEntrada = null
                            //Incidencia = incidenciaDto, // las incidencias solo hacen falta en lotes con inspección pendiente, y como estamos recorriendo las que han pasado... pues eso
                        };
                        listaLotes.Add(detalleLoteDto);
                    }
                }

                listaArticulos.Add(new BarnizDTO
                {
                    Articulo = aArticuDto,
                    ListaDetallesLotes = listaLotes
                });
            }

            //      - Lo segundo, buscar entre nuestros lotes activos los que tengan inspección pendiente, para añadirlos, o actualizar los datos del artículo/lotes/primeraEntrada ya añadida.
            var listaLotesActivosPorArticulo = listaArticulosConLotesActivos.GroupBy(i => i.Item1);
            foreach (var kvLotesPorArticulo in listaLotesActivosPorArticulo)
            {
                var aArticu = kvLotesPorArticulo.Key;
                var lotesDelArticulo = kvLotesPorArticulo.ToList();

                // buscar si ya existe este artículo en listaArticulos y, por cada uno de sus lotes en lotesDelArticulo, verificamos si ya han sido inspeccionados
                var detalleArticuloExistenteDto =
                    listaArticulos.FirstOrDefault(a => a.Articulo.Codigo == aArticu.Codigo);
                foreach (var (articulo, lote, primeraEntrada) in lotesDelArticulo)
                {
                    var loteYaInspeccionado = !(detalleArticuloExistenteDto == null
                                                || detalleArticuloExistenteDto?.ListaDetallesLotes == null
                                                || !detalleArticuloExistenteDto.ListaDetallesLotes.Any());
                    var loteDto = TinyMapper.Map<ALotessDTO>(lote);
                    if (loteYaInspeccionado)
                    {
                        // la búsqueda es por código, lote y ubicación
                        var detalleLoteDto = detalleArticuloExistenteDto?.ListaDetallesLotes
                            ?.FirstOrDefault(i =>
                                i.Lote.Lote.Equals(lote.Lote)
                                && i.Lote.Codigo.Equals(lote.Codigo)
                                && i.Lote.Ubicacion.Equals(lote.Ubicacion)
                            );
                        loteYaInspeccionado = detalleLoteDto?.Lote != null;

                        // actualizamos detalleArticuloExistenteDto para pisar si hubieran sido fakeados en el paso anterior
                        if (loteYaInspeccionado)
                        {
                            detalleLoteDto.Lote = loteDto;
                            detalleArticuloExistenteDto.Articulo = TinyMapper.Map<AArticuDTO>(articulo);
                        }
                    }

                    if (!loteYaInspeccionado)
                    {
                        var primeraEntradaDto =
                            primeraEntrada != null ? TinyMapper.Map<AEntdiaDTO>(primeraEntrada) : null;
                        IncidenciaDTO? incidenciaDto = null;
                        var incidencia = await _incidenciasRepo.GetIncidenciaByIdProductoxLotexUbicacion(
                            loteDto.Codigo,
                            loteDto.Lote,
                            loteDto.Ubicacion,
                            new IncidenciasQueryOptions(),
                            cancellationToken);
                        incidenciaDto = incidencia != null ? TinyMapper.Map<IncidenciaDTO>(incidencia) : null;

                        var detalleLoteDto = new DetalleLoteBarnizDTO
                        {
                            Lote = loteDto,
                            Inspeccion = null, // No hay inspección porque está pendiente
                            Incidencia = incidenciaDto,
                            PrimeraEntrada = primeraEntradaDto
                        };

                        if (detalleArticuloExistenteDto != null)
                        {
                            detalleArticuloExistenteDto.ListaDetallesLotes ??= [];
                            detalleArticuloExistenteDto.ListaDetallesLotes.Add(detalleLoteDto);
                        }
                        else
                        {
                            var aArticuDto = TinyMapper.Map<AArticuDTO>(aArticu);
                            listaArticulos.Add(new BarnizDTO
                            {
                                Articulo = aArticuDto,
                                ListaDetallesLotes = [detalleLoteDto]
                            });
                        }
                    }
                }
            }

            result.Data = listaArticulos;
        }
        catch (Exception ex)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}