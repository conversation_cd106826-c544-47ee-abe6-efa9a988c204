﻿namespace GestionAPQ_BLZ.DTO.Genericos;

public class OperarioDTO
{
    public int? Id { get; set; }

    public string Nombre { get; set; }

    public string Apellido1 { get; set; }

    public string Apellido2 { get; set; }

    public bool Activo { get; set; }

    public DateTime? FechaCreacion { get; set; }

    public DateTime? FechaModificacion { get; set; }

    // Navigation properties
    public List<LoteNodrizaDTO>? LotesNodrizas { get; set; }

    public List<IncidenciaDTO>? Incidencias { get; set; }

    public List<InspeccionDTO>? Inspecciones { get; set; }

    // Props calculadas
    public string NombreCompleto => $"{Nombre} {Apellido1} {Apellido2}".Trim();
}