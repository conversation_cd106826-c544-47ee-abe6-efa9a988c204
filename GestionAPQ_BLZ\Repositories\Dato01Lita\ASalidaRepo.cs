namespace GestionAPQ_BLZ.Repositories.Dato01Lita;

using Base;
using GestionAQP_BLZ.Server.Data.DbContexts.Dato01Lita;
using GestionAQP_BLZ.Server.Data.Entities.Dato01Lita;
using Microsoft.EntityFrameworkCore;

public class ASalidaRepo : IASalidaRepo
{
    private readonly Dato01LitaContext _dbContext;
    private readonly DbSet<ASalida> _dbSet;

    public ASalidaRepo(Dato01LitaContext dbContext)
    {
        _dbContext = dbContext;
        _dbSet = dbContext.Set<ASalida>();
    }

    public async Task<List<ASalida>> GetSalidasByIdProducto(string idProducto, QueryOptions options,
        CancellationToken cancellationToken = default)
    {
        var query = options.SetOptions(_dbSet);

        query = query.Where(i =>
            i.Estadis.StartsWith("BABA") &&
            !i.Numero.StartsWith("T") &&
            i.Codigo == idProducto);

        return await query.ToListAsync(cancellationToken);
    }
}