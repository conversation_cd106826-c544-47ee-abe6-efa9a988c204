﻿namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public class GrabarLotesNodrizasCommand : IRequest<SingleResult<int>>
{
    public GrabarLotesNodrizasCommand(LotesNodrizasDTO lote)
    {
        Lote = lote;
    }

    public LotesNodrizasDTO Lote { get; set; }
}

public class GrabarLoteCommandHandler : IRequestHandler<GrabarLotesNodrizasCommand, SingleResult<int>>
{
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public GrabarLoteCommandHandler(ILotesNodrizasRepo lotesNodrizasRepo)
    {
        _lotesNodrizasRepo = lotesNodrizasRepo;
    }

    public async Task<SingleResult<int>> Handle(GrabarLotesNodrizasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };
        var loteDb = TinyMapper.Map<Lotesnodrizas>(request.Lote);
        var resultDb = await _lotesNodrizasRepo.UpdateEntityWithId(cancellationToken, loteDb);
        result.Data = resultDb;
        return result;
    }
}