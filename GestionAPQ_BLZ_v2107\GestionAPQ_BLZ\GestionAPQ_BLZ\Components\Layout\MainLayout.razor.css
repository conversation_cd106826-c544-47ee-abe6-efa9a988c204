.navbar-header {
    background-color: #a91c32;
    border: none;
    border-radius: 0;
    box-shadow: 8px 2px 6px rgba(0, 0, 0, 0.35);
    flex-grow: 0;
    flex-wrap: nowrap;
    height: 3.5rem;
    min-height: 3.5rem;
}

.contenedor-icono-hamburger {
    max-width: 65px;
    min-width: 65px;
    width: 65px;
}

.contenedor-icono-sidebar {
    max-width: 36px;
    min-width: 36px;
    width: 36px;
}

.border-end { border-right-color: rgba(34, 34, 34, 0.15) !important; }

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255, 1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
    height: 2rem;
    width: 2rem;
}

.icono-sidebar { font-size: 1.25rem; }

.nav-item {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

.nav-item:first-of-type { padding-top: 1rem; }

.nav-item:last-of-type { padding-bottom: 1rem; }

.nav-item ::deep .nav-link { padding: 0.5rem 5px !important; }

.nav-item ::deep a {
    align-items: center;
    border-radius: 4px;
    color: #d7d7d7;
    display: flex;
    height: 3rem;
    line-height: 3rem;
}

.nav-item ::deep a.active {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
}

.nav-item ::deep a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}