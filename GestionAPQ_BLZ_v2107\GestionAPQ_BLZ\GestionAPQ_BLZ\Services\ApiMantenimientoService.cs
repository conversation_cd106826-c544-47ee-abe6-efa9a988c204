﻿namespace GestionAPQ_BLZ.Services;

using System.Text.Json;
using Base;
using Common.ResponseModels.ResponseModels;

public class ApiMantenimientoService : IApiMantenimientoService
{
    public const string BASE_URL_MANTENIMIENTO = "api/mantenimiento/dx-table";
    public const string BASE_URL_RRHH = "api/rrhh/trabajador/dx-table";

    public ApiMantenimientoService(HttpClient httpClient)
    {
        HttpClient = httpClient;
        Options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };
    }

    private protected HttpClient HttpClient { get; }
    private protected JsonSerializerOptions Options { get; }

    public async Task<HttpResponseMessage> PostFromForm(string url, List<KeyValuePair<string, string>> listaValores)
    {
        var formContent = new FormUrlEncodedContent(listaValores);
        return await HttpClient.PostAsync(url, formContent);
    }

    public async Task<HttpResponseMessage> PutFromForm(string url, List<KeyValuePair<string, string>> listaValores)
    {
        var formContent = new FormUrlEncodedContent(listaValores);
        return await HttpClient.PutAsync(url, formContent);
    }

    public async Task<SingleResult<T>> GetSingleResult<T>(string url)
    {
        using var response = await HttpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();

        var jsonResp = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<SingleResult<T>>(jsonResp, Options);
    }

    public async Task<ListResult<T>> GetListResult<T>(string url)
    {
        using var response = await HttpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();

        var jsonResp = await response.Content.ReadAsStringAsync();
        using var jsonDocument = JsonDocument.Parse(jsonResp);
        var dataArray = jsonDocument.RootElement.GetProperty("data");

        var data = JsonSerializer.Deserialize<List<T>>(dataArray.GetRawText(), Options);
        return new ListResult<T>
        {
            Data = data,
            Errors = new List<string>()
        };
    }
}