﻿@inherits DxGridCommandColumn

@base.BuildRenderTree

@* src: https://supportcenter.devexpress.com/ticket/details/t1135370/grid-for-blazor-how-to-create-reusable-dxgridcommandcolumn *@

@code {
	[CascadingParameter] public IGrid MyGrid { get; set; }
	[Parameter] public string? CustomFieldName { get; set; }
	[Parameter] public Action<GridCommandColumnCellDisplayTemplateContext>? OnDeleteButtonClicked { get; set; }
	[Parameter] public string? TooltipBtnNuevo { get; set; }
	[Parameter] public string? TooltipBtnEditar { get; set; }
	[Parameter] public string? TooltipBtnEliminar { get; set; }
	[Parameter] public string? TooltipBtnGuardar { get; set; }

	protected override bool ShouldRender()
	{
		return false;
	}

	protected override async Task OnInitializedAsync()
	{
		Width = "70px";
		Name = CustomFieldName;
		HeaderTemplate = context =>
			@<text>
				 @if (NewButtonVisible)
				 {
					 <a class="btn btn-link p-0 bi bi-plus-lg text-success" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 15 : 19)}px;") title="@(TooltipBtnNuevo ?? "Nuevo")"
					    @onclick="@(async () => await HandleStartEditNewRowAsync())">
					 </a>
				 }
		</text>;
		FilterRowCellTemplate = context =>
			@<text>
				 @if (ClearFilterButtonVisible)
				 {
					 <a class="btn btn-link p-0" title="Limpiar" @onclick="@(HandleClearFilter)" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 12 : 16)}px;")>Limpiar</a>
				 }
		</text>;
		CellDisplayTemplate = context =>
			@<text>
				 @if (EditButtonVisible)
				 {
					 <a class="btn btn-link p-0 bi bi-pencil" title="@(TooltipBtnEditar ?? "Editar")" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 13 : 16)}px;") @onclick="@(async () => await HandleStartEditRowAsync(context))">
					 </a>
				 }
				 @if (DeleteButtonVisible)
				 {
					 <a class="btn btn-link p-0 ps-2 bi bi-trash text-danger" title="@(TooltipBtnEliminar ?? "Eliminar")" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 13 : 16)}px;") @onclick="@(() => HandlePopupDelete(context))">
					 </a>
				 }
		</text>;
		CellEditTemplate = context =>
				@<text>
					 @if (SaveButtonVisible)
					 {
						 <a class="btn btn-link p-0 bi bi-floppy" title="@(TooltipBtnGuardar ?? "Guardar")" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 13 : 16)}px;") @onclick="@(async () => await HandleSaveChangesAsync())">
						 </a>
					 }
					 @if (CancelButtonVisible)
					 {
						 <a class="btn btn-link p-0 ps-2 bi bi-x-lg text-danger" title="Cancelar Cambios" style=@($"font-size: {(MyGrid.SizeMode == SizeMode.Small ? 13 : 16)}px;") @onclick="@(async () => await HandleCancelEditAsync())">
						 </a>
					 }
			</text>
			;

		await base.OnInitializedAsync();
	}

	private void HandleClearFilter()
	{
		MyGrid.ClearFilter();
	}

	private async Task HandleStartEditNewRowAsync()
	{
		await MyGrid.StartEditNewRowAsync();
	}

	private async Task HandleStartEditRowAsync(GridCommandColumnCellDisplayTemplateContext context)
	{
		await MyGrid.StartEditRowAsync(context.VisibleIndex);
	}

	private void HandlePopupDelete(GridCommandColumnCellDisplayTemplateContext context)
	{
		OnDeleteButtonClicked?.Invoke(context);
	}

	private async Task HandleSaveChangesAsync()
	{
		await MyGrid.SaveChangesAsync();
	}

	private async Task HandleCancelEditAsync()
	{
		await MyGrid.CancelEditAsync();
	}

}