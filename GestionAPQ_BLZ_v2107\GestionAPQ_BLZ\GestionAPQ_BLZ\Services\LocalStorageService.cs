﻿namespace GestionAPQ_BLZ.Services;

using System.Text.Json;
using Base;
using Microsoft.JSInterop;

public class LocalStorageService : ILocalStorageService
{
    private readonly IJSRuntime _jsRuntime;

    public LocalStorageService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public async Task SetItemAsync<T>(EnumKeysLocalStorage key, T obj) where T : class
    {
        var json = JsonSerializer.Serialize(obj);
        await _jsRuntime.InvokeVoidAsync("localStorageService.setItem", key, json);
    }

    public async Task<T?> GetItemAsync<T>(EnumKeysLocalStorage key) where T : class
    {
        T? obj = null;
        var json = await _jsRuntime.InvokeAsync<string>("localStorageService.getItem", key);
        if (string.IsNullOrEmpty(json))
            return obj;

        try
        {
            obj = JsonSerializer.Deserialize<T>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch
        {
            obj = null;
        }

        return obj;
    }

    public async Task RemoveItemAsync(EnumKeysLocalStorage key)
    {
        await _jsRuntime.InvokeVoidAsync("localStorageService.removeItem", key);
    }
}