﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ITablaInspeccionesRepo : IRepository<TablaInspecciones>
{
    public Task<TablaInspecciones?> GetInspeccionPorId(int idInspeccion,
        bool asNoTracking, CancellationToken cancellationToken);

    public Task<List<TablaInspecciones>> GetInspeccionesPasadasProductos(int? idProducto,
        bool asNoTracking, CancellationToken cancellationToken);

    public Task<TablaInspecciones?> GetInspeccionByProductoLote(int idProducto, string lote,
        bool asNoTracking, CancellationToken cancellationToken);
}