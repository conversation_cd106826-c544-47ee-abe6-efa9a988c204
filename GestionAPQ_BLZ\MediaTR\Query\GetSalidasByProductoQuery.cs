namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories;
using Repositories.Dato01Lita.Base;

public record GetSalidasByProductoQuery(int IdProducto)
    : IRequest<ListResult<ASalidaDTO>>;

public class GetSalidasByProductoQueryHandler : IRequestHandler<GetSalidasByProductoQuery,
    ListResult<ASalidaDTO>>
{
    private readonly IASalidaRepo _aSalidaRepo;

    public GetSalidasByProductoQueryHandler(IASalidaRepo aSalidaRepo)
    {
        _aSalidaRepo = aSalidaRepo;
    }

    public async Task<ListResult<ASalidaDTO>> Handle(
        GetSalidasByProductoQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ASalidaDTO>
        {
            Data = [],
            Errors = []
        };

        try
        {
            var salidas = await _aSalidaRepo.GetSalidasByIdProducto(request.IdProducto.ToString(), new QueryOptions(),
                cancellationToken);
            result.Data = TinyMapper.Map<List<ASalidaDTO>>(salidas);
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: {request.GetType().Name} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}

public class LotesNodrizasySalidasProductoDTO
{
    public List<LoteNodrizaDTO> ListaLotesNodrizas { get; set; } = [];
    public List<ASalidaDTO> ListaSalidas { get; set; } = [];
}