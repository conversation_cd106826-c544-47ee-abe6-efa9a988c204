﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa.Base;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface ILotesNodrizasRepo
{
    Task<LotesNodrizas?> GetUltimoLoteByNodrizaYProducto(
        int idNodriza,
        int idProducto,
        LotesNodrizasQueryOptions options,
        CancellationToken cancellationToken = default);

    Task<List<LotesNodrizas>> GetLotesByProducto(
        int idProducto,
        LotesNodrizasQueryOptions options,
        CancellationToken cancellationToken = default);

    Task Add(LotesNodrizas loteNodriza, CancellationToken cancellationToken = default);

    Task Update(LotesNodrizas loteNodriza, CancellationToken cancellationToken = default);
}